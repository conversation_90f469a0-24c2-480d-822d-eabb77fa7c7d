# Test ABC Banking API
Write-Host "Testing ABC Banking API..." -ForegroundColor Green

# Test 1: Health Check
Write-Host "`n1. Testing Health Check..." -ForegroundColor Yellow
try {
    $health = Invoke-RestMethod -Uri "http://localhost:8080/api/test/health" -Method GET
    Write-Host "✅ Health Check: $($health.status)" -ForegroundColor Green
    Write-Host "   Message: $($health.message)" -ForegroundColor Cyan
} catch {
    Write-Host "❌ Health Check Failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 2: Login with <PERSON>e
Write-Host "`n2. Testing Login..." -ForegroundColor Yellow
$loginBody = @{
    username = "john.doe"
    password = "password123"
} | ConvertTo-Json

try {
    $loginResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/auth/login" -Method POST -ContentType "application/json" -Body $loginBody
    Write-Host "✅ Login Successful!" -ForegroundColor Green
    Write-Host "   User: $($loginResponse.user.firstName) $($loginResponse.user.lastName)" -ForegroundColor Cyan
    Write-Host "   Email: $($loginResponse.user.email)" -ForegroundColor Cyan
    Write-Host "   Token: $($loginResponse.accessToken.Substring(0,20))..." -ForegroundColor Cyan
    
    $token = $loginResponse.accessToken
    $headers = @{
        "Authorization" = "Bearer $token"
        "Content-Type" = "application/json"
    }
    
    # Test 3: Get User's Accounts
    Write-Host "`n3. Testing Get User Accounts..." -ForegroundColor Yellow
    try {
        $accounts = Invoke-RestMethod -Uri "http://localhost:8080/api/accounts/my" -Method GET -Headers $headers
        Write-Host "✅ Found $($accounts.Count) accounts:" -ForegroundColor Green
        foreach ($account in $accounts) {
            Write-Host "   Account: $($account.accountNumber) - $($account.type) - Balance: $($account.balance)" -ForegroundColor Cyan
        }
        
        # Test 4: Money Transfer (if we have multiple accounts)
        if ($accounts.Count -ge 2) {
            Write-Host "`n4. Testing Money Transfer..." -ForegroundColor Yellow
            $fromAccount = $accounts[0].id
            $toAccount = $accounts[1].id
            $transferAmount = 100.00
            
            Write-Host "   Before Transfer:" -ForegroundColor Cyan
            Write-Host "   From Account Balance: $($accounts[0].balance)" -ForegroundColor Cyan
            Write-Host "   To Account Balance: $($accounts[1].balance)" -ForegroundColor Cyan
            
            $transferBody = @{
                fromAccountId = $fromAccount
                toAccountId = $toAccount
                amount = $transferAmount
                description = "Test transfer from API"
            } | ConvertTo-Json
            
            try {
                $transferResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/transactions/transfer" -Method POST -Headers $headers -Body $transferBody
                Write-Host "✅ Transfer Successful!" -ForegroundColor Green
                Write-Host "   Transaction ID: $($transferResponse.transactionId)" -ForegroundColor Cyan
                Write-Host "   Amount: $($transferResponse.amount)" -ForegroundColor Cyan
                Write-Host "   Status: $($transferResponse.status)" -ForegroundColor Cyan
                
                # Test 5: Check Updated Balances
                Write-Host "`n5. Checking Updated Balances..." -ForegroundColor Yellow
                $updatedAccounts = Invoke-RestMethod -Uri "http://localhost:8080/api/accounts/my" -Method GET -Headers $headers
                Write-Host "   After Transfer:" -ForegroundColor Cyan
                foreach ($account in $updatedAccounts) {
                    Write-Host "   Account: $($account.accountNumber) - Balance: $($account.balance)" -ForegroundColor Cyan
                }
                
            } catch {
                Write-Host "❌ Transfer Failed: $($_.Exception.Message)" -ForegroundColor Red
            }
        } else {
            Write-Host "⚠️  Need at least 2 accounts for transfer test" -ForegroundColor Yellow
        }
        
    } catch {
        Write-Host "❌ Get Accounts Failed: $($_.Exception.Message)" -ForegroundColor Red
    }
    
} catch {
    Write-Host "❌ Login Failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n🎉 API Testing Complete!" -ForegroundColor Green
