package org.example.abcbanking.dto;

import org.example.abcbanking.model.CardStatus;
import org.example.abcbanking.model.CardType;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

public class CardDto {
    private Long id;
    private String cardNumber;
    private String maskedCardNumber;
    private String cardHolderName;
    private CardType cardType;
    private LocalDate expiryDate;
    private CardStatus status;
    private BigDecimal dailyLimit;
    private BigDecimal monthlyLimit;
    private BigDecimal creditLimit;
    private BigDecimal availableCredit;
    private BigDecimal outstandingBalance;
    private Boolean isBlocked;
    private String blockReason;
    private LocalDateTime blockedDate;
    private LocalDateTime lastUsedDate;
    private LocalDate activationDate;
    private Boolean contactlessEnabled;
    private Boolean onlineTransactionsEnabled;
    private Boolean internationalTransactionsEnabled;
    private Boolean atmTransactionsEnabled;
    private Boolean posTransactionsEnabled;
    private Long rewardsPoints;
    private BigDecimal cashbackEarned;
    private Long accountId;
    private String accountName;
    
    // Constructors
    public CardDto() {}
    
    public CardDto(Long id, String cardNumber, String cardHolderName, CardType cardType, 
                   LocalDate expiryDate, CardStatus status, Long accountId) {
        this.id = id;
        this.cardNumber = cardNumber;
        this.cardHolderName = cardHolderName;
        this.cardType = cardType;
        this.expiryDate = expiryDate;
        this.status = status;
        this.accountId = accountId;
    }
    
    // Getters and Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    
    public String getCardNumber() { return cardNumber; }
    public void setCardNumber(String cardNumber) { this.cardNumber = cardNumber; }
    
    public String getMaskedCardNumber() { return maskedCardNumber; }
    public void setMaskedCardNumber(String maskedCardNumber) { this.maskedCardNumber = maskedCardNumber; }
    
    public String getCardHolderName() { return cardHolderName; }
    public void setCardHolderName(String cardHolderName) { this.cardHolderName = cardHolderName; }
    
    public CardType getCardType() { return cardType; }
    public void setCardType(CardType cardType) { this.cardType = cardType; }
    
    public LocalDate getExpiryDate() { return expiryDate; }
    public void setExpiryDate(LocalDate expiryDate) { this.expiryDate = expiryDate; }
    
    public CardStatus getStatus() { return status; }
    public void setStatus(CardStatus status) { this.status = status; }
    
    public BigDecimal getDailyLimit() { return dailyLimit; }
    public void setDailyLimit(BigDecimal dailyLimit) { this.dailyLimit = dailyLimit; }
    
    public BigDecimal getMonthlyLimit() { return monthlyLimit; }
    public void setMonthlyLimit(BigDecimal monthlyLimit) { this.monthlyLimit = monthlyLimit; }
    
    public BigDecimal getCreditLimit() { return creditLimit; }
    public void setCreditLimit(BigDecimal creditLimit) { this.creditLimit = creditLimit; }
    
    public BigDecimal getAvailableCredit() { return availableCredit; }
    public void setAvailableCredit(BigDecimal availableCredit) { this.availableCredit = availableCredit; }
    
    public BigDecimal getOutstandingBalance() { return outstandingBalance; }
    public void setOutstandingBalance(BigDecimal outstandingBalance) { this.outstandingBalance = outstandingBalance; }
    
    public Boolean getIsBlocked() { return isBlocked; }
    public void setIsBlocked(Boolean isBlocked) { this.isBlocked = isBlocked; }
    
    public String getBlockReason() { return blockReason; }
    public void setBlockReason(String blockReason) { this.blockReason = blockReason; }
    
    public LocalDateTime getBlockedDate() { return blockedDate; }
    public void setBlockedDate(LocalDateTime blockedDate) { this.blockedDate = blockedDate; }
    
    public LocalDateTime getLastUsedDate() { return lastUsedDate; }
    public void setLastUsedDate(LocalDateTime lastUsedDate) { this.lastUsedDate = lastUsedDate; }
    
    public LocalDate getActivationDate() { return activationDate; }
    public void setActivationDate(LocalDate activationDate) { this.activationDate = activationDate; }
    
    public Boolean getContactlessEnabled() { return contactlessEnabled; }
    public void setContactlessEnabled(Boolean contactlessEnabled) { this.contactlessEnabled = contactlessEnabled; }
    
    public Boolean getOnlineTransactionsEnabled() { return onlineTransactionsEnabled; }
    public void setOnlineTransactionsEnabled(Boolean onlineTransactionsEnabled) { this.onlineTransactionsEnabled = onlineTransactionsEnabled; }
    
    public Boolean getInternationalTransactionsEnabled() { return internationalTransactionsEnabled; }
    public void setInternationalTransactionsEnabled(Boolean internationalTransactionsEnabled) { this.internationalTransactionsEnabled = internationalTransactionsEnabled; }
    
    public Boolean getAtmTransactionsEnabled() { return atmTransactionsEnabled; }
    public void setAtmTransactionsEnabled(Boolean atmTransactionsEnabled) { this.atmTransactionsEnabled = atmTransactionsEnabled; }
    
    public Boolean getPosTransactionsEnabled() { return posTransactionsEnabled; }
    public void setPosTransactionsEnabled(Boolean posTransactionsEnabled) { this.posTransactionsEnabled = posTransactionsEnabled; }
    
    public Long getRewardsPoints() { return rewardsPoints; }
    public void setRewardsPoints(Long rewardsPoints) { this.rewardsPoints = rewardsPoints; }
    
    public BigDecimal getCashbackEarned() { return cashbackEarned; }
    public void setCashbackEarned(BigDecimal cashbackEarned) { this.cashbackEarned = cashbackEarned; }
    
    public Long getAccountId() { return accountId; }
    public void setAccountId(Long accountId) { this.accountId = accountId; }
    
    public String getAccountName() { return accountName; }
    public void setAccountName(String accountName) { this.accountName = accountName; }
}
