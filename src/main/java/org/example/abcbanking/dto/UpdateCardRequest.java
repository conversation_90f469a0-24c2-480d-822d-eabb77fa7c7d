package org.example.abcbanking.dto;

import jakarta.validation.constraints.*;
import java.math.BigDecimal;

public class UpdateCardRequest {
    
    @NotBlank(message = "Card holder name is required")
    @Size(max = 100, message = "Card holder name must not exceed 100 characters")
    private String cardHolderName;
    
    @DecimalMin(value = "1000.0", message = "Daily limit must be at least 1000")
    @DecimalMax(value = "1000000.0", message = "Daily limit must not exceed 1000000")
    private BigDecimal dailyLimit;
    
    @DecimalMin(value = "5000.0", message = "Monthly limit must be at least 5000")
    @DecimalMax(value = "5000000.0", message = "Monthly limit must not exceed 5000000")
    private BigDecimal monthlyLimit;
    
    private Boolean contactlessEnabled;
    private Boolean onlineTransactionsEnabled;
    private Boolean internationalTransactionsEnabled;
    private Boolean atmTransactionsEnabled;
    private Boolean posTransactionsEnabled;
    
    // Constructors
    public UpdateCardRequest() {}
    
    // Getters and Setters
    public String getCardHolderName() { return cardHolderName; }
    public void setCardHolderName(String cardHolderName) { this.cardHolderName = cardHolderName; }
    
    public BigDecimal getDailyLimit() { return dailyLimit; }
    public void setDailyLimit(BigDecimal dailyLimit) { this.dailyLimit = dailyLimit; }
    
    public BigDecimal getMonthlyLimit() { return monthlyLimit; }
    public void setMonthlyLimit(BigDecimal monthlyLimit) { this.monthlyLimit = monthlyLimit; }
    
    public Boolean getContactlessEnabled() { return contactlessEnabled; }
    public void setContactlessEnabled(Boolean contactlessEnabled) { this.contactlessEnabled = contactlessEnabled; }
    
    public Boolean getOnlineTransactionsEnabled() { return onlineTransactionsEnabled; }
    public void setOnlineTransactionsEnabled(Boolean onlineTransactionsEnabled) { this.onlineTransactionsEnabled = onlineTransactionsEnabled; }
    
    public Boolean getInternationalTransactionsEnabled() { return internationalTransactionsEnabled; }
    public void setInternationalTransactionsEnabled(Boolean internationalTransactionsEnabled) { this.internationalTransactionsEnabled = internationalTransactionsEnabled; }
    
    public Boolean getAtmTransactionsEnabled() { return atmTransactionsEnabled; }
    public void setAtmTransactionsEnabled(Boolean atmTransactionsEnabled) { this.atmTransactionsEnabled = atmTransactionsEnabled; }
    
    public Boolean getPosTransactionsEnabled() { return posTransactionsEnabled; }
    public void setPosTransactionsEnabled(Boolean posTransactionsEnabled) { this.posTransactionsEnabled = posTransactionsEnabled; }
}
