package org.example.abcbanking.dto;

import jakarta.validation.constraints.*;
import java.math.BigDecimal;

public class EmiCalculationRequest {
    
    @NotNull(message = "Principal amount is required")
    @DecimalMin(value = "1000.0", message = "Minimum amount is 1000")
    @DecimalMax(value = "********.0", message = "Maximum amount is 10,000,000")
    private BigDecimal principalAmount;
    
    @NotNull(message = "Interest rate is required")
    @DecimalMin(value = "1.0", message = "Minimum interest rate is 1%")
    @DecimalMax(value = "50.0", message = "Maximum interest rate is 50%")
    private BigDecimal interestRate;
    
    @NotNull(message = "Tenure is required")
    @Min(value = 6, message = "Minimum tenure is 6 months")
    @Max(value = 360, message = "Maximum tenure is 360 months")
    private Integer tenureMonths;

    // Default constructor
    public EmiCalculationRequest() {}

    // Constructor
    public EmiCalculationRequest(BigDecimal principalAmount, BigDecimal interestRate, Integer tenureMonths) {
        this.principalAmount = principalAmount;
        this.interestRate = interestRate;
        this.tenureMonths = tenureMonths;
    }

    // Getters and Setters
    public BigDecimal getPrincipalAmount() {
        return principalAmount;
    }

    public void setPrincipalAmount(BigDecimal principalAmount) {
        this.principalAmount = principalAmount;
    }

    public BigDecimal getInterestRate() {
        return interestRate;
    }

    public void setInterestRate(BigDecimal interestRate) {
        this.interestRate = interestRate;
    }

    public Integer getTenureMonths() {
        return tenureMonths;
    }

    public void setTenureMonths(Integer tenureMonths) {
        this.tenureMonths = tenureMonths;
    }
}
