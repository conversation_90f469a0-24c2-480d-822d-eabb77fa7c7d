package org.example.abcbanking.dto;

import jakarta.validation.constraints.*;

public class LoanRejectionRequest {
    
    @NotBlank(message = "Rejection reason is required")
    @Size(min = 10, max = 500, message = "Rejection reason must be between 10 and 500 characters")
    private String reason;
    
    @Size(max = 1000, message = "Additional comments cannot exceed 1000 characters")
    private String additionalComments;

    // Default constructor
    public LoanRejectionRequest() {}

    // Constructor
    public LoanRejectionRequest(String reason) {
        this.reason = reason;
    }

    // Getters and Setters
    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getAdditionalComments() {
        return additionalComments;
    }

    public void setAdditionalComments(String additionalComments) {
        this.additionalComments = additionalComments;
    }
}
