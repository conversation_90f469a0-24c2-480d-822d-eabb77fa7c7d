package org.example.abcbanking.dto;

import org.example.abcbanking.model.LoanType;
import jakarta.validation.constraints.*;

import java.math.BigDecimal;

public class LoanApplicationRequest {
    
    @NotNull(message = "Loan type is required")
    private LoanType type;
    
    @NotNull(message = "Principal amount is required")
    @DecimalMin(value = "1000.0", message = "Minimum loan amount is 1000")
    @DecimalMax(value = "********.0", message = "Maximum loan amount is 10,000,000")
    private BigDecimal principalAmount;
    
    @NotNull(message = "Tenure is required")
    @Min(value = 6, message = "Minimum tenure is 6 months")
    @Max(value = 360, message = "Maximum tenure is 360 months")
    private Integer tenureMonths;
    
    @NotBlank(message = "Purpose is required")
    @Size(min = 10, max = 500, message = "Purpose must be between 10 and 500 characters")
    private String purpose;
    
    private Long accountId; // Optional - account to credit loan amount
    
    // Employment details
    @Size(max = 100, message = "Employer name cannot exceed 100 characters")
    private String employerName;
    
    @DecimalMin(value = "0.0", message = "Monthly income must be positive")
    private BigDecimal monthlyIncome;
    
    @Size(max = 100, message = "Occupation cannot exceed 100 characters")
    private String occupation;
    
    // Additional documents/information
    @Size(max = 1000, message = "Additional information cannot exceed 1000 characters")
    private String additionalInfo;
    
    private Boolean agreeToTerms = false;

    // Default constructor
    public LoanApplicationRequest() {}

    // Getters and Setters
    public LoanType getType() {
        return type;
    }

    public void setType(LoanType type) {
        this.type = type;
    }

    public BigDecimal getPrincipalAmount() {
        return principalAmount;
    }

    public void setPrincipalAmount(BigDecimal principalAmount) {
        this.principalAmount = principalAmount;
    }

    public Integer getTenureMonths() {
        return tenureMonths;
    }

    public void setTenureMonths(Integer tenureMonths) {
        this.tenureMonths = tenureMonths;
    }

    public String getPurpose() {
        return purpose;
    }

    public void setPurpose(String purpose) {
        this.purpose = purpose;
    }

    public Long getAccountId() {
        return accountId;
    }

    public void setAccountId(Long accountId) {
        this.accountId = accountId;
    }

    public String getEmployerName() {
        return employerName;
    }

    public void setEmployerName(String employerName) {
        this.employerName = employerName;
    }

    public BigDecimal getMonthlyIncome() {
        return monthlyIncome;
    }

    public void setMonthlyIncome(BigDecimal monthlyIncome) {
        this.monthlyIncome = monthlyIncome;
    }

    public String getOccupation() {
        return occupation;
    }

    public void setOccupation(String occupation) {
        this.occupation = occupation;
    }

    public String getAdditionalInfo() {
        return additionalInfo;
    }

    public void setAdditionalInfo(String additionalInfo) {
        this.additionalInfo = additionalInfo;
    }

    public Boolean getAgreeToTerms() {
        return agreeToTerms;
    }

    public void setAgreeToTerms(Boolean agreeToTerms) {
        this.agreeToTerms = agreeToTerms;
    }
}
