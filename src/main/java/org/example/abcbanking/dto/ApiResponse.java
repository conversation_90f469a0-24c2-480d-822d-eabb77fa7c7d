package org.example.abcbanking.dto;

import java.time.LocalDateTime;

public class ApiResponse {
    private String message;
    private boolean success;
    private LocalDateTime timestamp;
    private Object data;
    private String errorCode;
    
    // Constructors
    public ApiResponse() {
        this.timestamp = LocalDateTime.now();
    }
    
    public ApiResponse(String message, boolean success) {
        this();
        this.message = message;
        this.success = success;
    }
    
    public ApiResponse(String message, boolean success, Object data) {
        this(message, success);
        this.data = data;
    }
    
    public ApiResponse(String message, boolean success, String errorCode) {
        this(message, success);
        this.errorCode = errorCode;
    }
    
    // Static factory methods for common responses
    public static ApiResponse success(String message) {
        return new ApiResponse(message, true);
    }
    
    public static ApiResponse success(String message, Object data) {
        return new ApiResponse(message, true, data);
    }
    
    public static ApiResponse error(String message) {
        return new ApiResponse(message, false);
    }
    
    public static ApiResponse error(String message, String errorCode) {
        return new ApiResponse(message, false, errorCode);
    }
    
    // Getters and Setters
    public String getMessage() {
        return message;
    }
    
    public void setMessage(String message) {
        this.message = message;
    }
    
    public boolean isSuccess() {
        return success;
    }
    
    public void setSuccess(boolean success) {
        this.success = success;
    }
    
    public LocalDateTime getTimestamp() {
        return timestamp;
    }
    
    public void setTimestamp(LocalDateTime timestamp) {
        this.timestamp = timestamp;
    }
    
    public Object getData() {
        return data;
    }
    
    public void setData(Object data) {
        this.data = data;
    }
    
    public String getErrorCode() {
        return errorCode;
    }
    
    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }
}
