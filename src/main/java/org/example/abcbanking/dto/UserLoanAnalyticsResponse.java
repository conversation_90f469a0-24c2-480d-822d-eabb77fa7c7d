package org.example.abcbanking.dto;

import java.math.BigDecimal;

public class UserLoanAnalyticsResponse {
    private BigDecimal totalLoanAmount;
    private BigDecimal totalOutstandingAmount;
    private BigDecimal creditScore;
    private int totalLoans;
    private int activeLoans;
    private int closedLoans;
    private int pendingLoans;
    private int rejectedLoans;
    private int defaultedLoans;
    private BigDecimal monthlyEmiAmount;
    private BigDecimal nextEmiAmount;
    private String nextEmiDate;

    // Default constructor
    public UserLoanAnalyticsResponse() {}

    // Getters and Setters
    public BigDecimal getTotalLoanAmount() {
        return totalLoanAmount;
    }

    public void setTotalLoanAmount(BigDecimal totalLoanAmount) {
        this.totalLoanAmount = totalLoanAmount;
    }

    public BigDecimal getTotalOutstandingAmount() {
        return totalOutstandingAmount;
    }

    public void setTotalOutstandingAmount(BigDecimal totalOutstandingAmount) {
        this.totalOutstandingAmount = totalOutstandingAmount;
    }

    public BigDecimal getCreditScore() {
        return creditScore;
    }

    public void setCreditScore(BigDecimal creditScore) {
        this.creditScore = creditScore;
    }

    public int getTotalLoans() {
        return totalLoans;
    }

    public void setTotalLoans(int totalLoans) {
        this.totalLoans = totalLoans;
    }

    public int getActiveLoans() {
        return activeLoans;
    }

    public void setActiveLoans(int activeLoans) {
        this.activeLoans = activeLoans;
    }

    public int getClosedLoans() {
        return closedLoans;
    }

    public void setClosedLoans(int closedLoans) {
        this.closedLoans = closedLoans;
    }

    public int getPendingLoans() {
        return pendingLoans;
    }

    public void setPendingLoans(int pendingLoans) {
        this.pendingLoans = pendingLoans;
    }

    public int getRejectedLoans() {
        return rejectedLoans;
    }

    public void setRejectedLoans(int rejectedLoans) {
        this.rejectedLoans = rejectedLoans;
    }

    public int getDefaultedLoans() {
        return defaultedLoans;
    }

    public void setDefaultedLoans(int defaultedLoans) {
        this.defaultedLoans = defaultedLoans;
    }

    public BigDecimal getMonthlyEmiAmount() {
        return monthlyEmiAmount;
    }

    public void setMonthlyEmiAmount(BigDecimal monthlyEmiAmount) {
        this.monthlyEmiAmount = monthlyEmiAmount;
    }

    public BigDecimal getNextEmiAmount() {
        return nextEmiAmount;
    }

    public void setNextEmiAmount(BigDecimal nextEmiAmount) {
        this.nextEmiAmount = nextEmiAmount;
    }

    public String getNextEmiDate() {
        return nextEmiDate;
    }

    public void setNextEmiDate(String nextEmiDate) {
        this.nextEmiDate = nextEmiDate;
    }
}
