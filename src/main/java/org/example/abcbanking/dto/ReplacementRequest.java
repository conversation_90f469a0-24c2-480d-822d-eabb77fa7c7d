package org.example.abcbanking.dto;

import jakarta.validation.constraints.NotBlank;

public class ReplacementRequest {
    
    @NotBlank(message = "Reason is required")
    private String reason;
    
    // Constructors
    public ReplacementRequest() {}
    
    public ReplacementRequest(String reason) {
        this.reason = reason;
    }
    
    // Getters and Setters
    public String getReason() {
        return reason;
    }
    
    public void setReason(String reason) {
        this.reason = reason;
    }
}
