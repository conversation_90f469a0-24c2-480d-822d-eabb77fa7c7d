package org.example.abcbanking.dto;

import org.example.abcbanking.model.LoanType;
import java.math.BigDecimal;

public class LoanEligibilityResponse {
    private boolean eligible;
    private BigDecimal maxEligibleAmount;
    private BigDecimal creditScore;
    private BigDecimal requestedAmount;
    private LoanType loanType;
    private String message;
    private String[] eligibilityCriteria;

    // Default constructor
    public LoanEligibilityResponse() {}

    // Getters and Setters
    public boolean isEligible() {
        return eligible;
    }

    public void setEligible(boolean eligible) {
        this.eligible = eligible;
    }

    public BigDecimal getMaxEligibleAmount() {
        return maxEligibleAmount;
    }

    public void setMaxEligibleAmount(BigDecimal maxEligibleAmount) {
        this.maxEligibleAmount = maxEligibleAmount;
    }

    public BigDecimal getCreditScore() {
        return creditScore;
    }

    public void setCreditScore(BigDecimal creditScore) {
        this.creditScore = creditScore;
    }

    public BigDecimal getRequestedAmount() {
        return requestedAmount;
    }

    public void setRequestedAmount(BigDecimal requestedAmount) {
        this.requestedAmount = requestedAmount;
    }

    public LoanType getLoanType() {
        return loanType;
    }

    public void setLoanType(LoanType loanType) {
        this.loanType = loanType;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String[] getEligibilityCriteria() {
        return eligibilityCriteria;
    }

    public void setEligibilityCriteria(String[] eligibilityCriteria) {
        this.eligibilityCriteria = eligibilityCriteria;
    }
}
