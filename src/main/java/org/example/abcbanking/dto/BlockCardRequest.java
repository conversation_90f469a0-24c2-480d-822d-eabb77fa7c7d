package org.example.abcbanking.dto;

import jakarta.validation.constraints.NotBlank;

public class BlockCardRequest {
    
    @NotBlank(message = "Reason is required")
    private String reason;
    
    private String blockedBy;
    
    // Constructors
    public BlockCardRequest() {}
    
    public BlockCardRequest(String reason, String blockedBy) {
        this.reason = reason;
        this.blockedBy = blockedBy;
    }
    
    // Getters and Setters
    public String getReason() {
        return reason;
    }
    
    public void setReason(String reason) {
        this.reason = reason;
    }
    
    public String getBlockedBy() {
        return blockedBy;
    }
    
    public void setBlockedBy(String blockedBy) {
        this.blockedBy = blockedBy;
    }
}
