package org.example.abcbanking.dto;

import org.example.abcbanking.model.Transaction;
import org.example.abcbanking.model.User;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

public class AdminDashboardResponse {
    
    // System Overview
    private long totalCustomers;
    private long totalAccounts;
    private long totalTransactions;
    private long totalCards;
    private long totalLoans;
    private long totalBranches;
    
    // Financial Overview
    private BigDecimal totalDeposits;
    private BigDecimal totalLoansAmount;
    private BigDecimal totalAssets;
    private BigDecimal totalLiabilities;
    private BigDecimal netWorth;
    
    // Today's Statistics
    private long todayTransactions;
    private BigDecimal todayTransactionVolume;
    private long todayNewCustomers;
    private long todayNewAccounts;
    
    // Account Statistics
    private long activeAccounts;
    private long inactiveAccounts;
    private long frozenAccounts;
    private long closedAccounts;
    
    // Card Statistics
    private long activeCards;
    private long blockedCards;
    private long expiredCards;
    private long pendingCards;
    
    // Loan Statistics
    private long pendingLoans;
    private long approvedLoans;
    private long rejectedLoans;
    private long activeLoans;
    private long defaultedLoans;
    private BigDecimal totalOutstandingAmount;
    
    // Transaction Statistics
    private long successfulTransactions;
    private long failedTransactions;
    private long pendingTransactions;
    private BigDecimal averageTransactionAmount;
    
    // Growth Metrics
    private double customerGrowthRate;
    private double accountGrowthRate;
    private double transactionGrowthRate;
    private double revenueGrowthRate;
    
    // System Health
    private String systemStatus;
    private double systemUptime;
    private long activeUserSessions;
    private LocalDateTime lastUpdated;
    
    // Recent Activity
    private List<Transaction> recentTransactions;
    private List<User> recentCustomers;
    
    // Alert Counts
    private long securityAlerts;
    private long complianceAlerts;
    private long systemAlerts;
    private long fraudAlerts;
    
    // Performance Metrics
    private double averageResponseTime;
    private long totalApiCalls;
    private double errorRate;
    
    // Branch Performance
    private String topPerformingBranch;
    private BigDecimal topBranchRevenue;
    private String leastPerformingBranch;
    
    // Default constructor
    public AdminDashboardResponse() {}
    
    // Getters and Setters
    public long getTotalCustomers() {
        return totalCustomers;
    }
    
    public void setTotalCustomers(long totalCustomers) {
        this.totalCustomers = totalCustomers;
    }
    
    public long getTotalAccounts() {
        return totalAccounts;
    }
    
    public void setTotalAccounts(long totalAccounts) {
        this.totalAccounts = totalAccounts;
    }
    
    public long getTotalTransactions() {
        return totalTransactions;
    }
    
    public void setTotalTransactions(long totalTransactions) {
        this.totalTransactions = totalTransactions;
    }
    
    public long getTotalCards() {
        return totalCards;
    }
    
    public void setTotalCards(long totalCards) {
        this.totalCards = totalCards;
    }
    
    public long getTotalLoans() {
        return totalLoans;
    }
    
    public void setTotalLoans(long totalLoans) {
        this.totalLoans = totalLoans;
    }
    
    public long getTotalBranches() {
        return totalBranches;
    }
    
    public void setTotalBranches(long totalBranches) {
        this.totalBranches = totalBranches;
    }
    
    public BigDecimal getTotalDeposits() {
        return totalDeposits;
    }
    
    public void setTotalDeposits(BigDecimal totalDeposits) {
        this.totalDeposits = totalDeposits;
    }
    
    public BigDecimal getTotalLoansAmount() {
        return totalLoansAmount;
    }
    
    public void setTotalLoansAmount(BigDecimal totalLoansAmount) {
        this.totalLoansAmount = totalLoansAmount;
    }
    
    public BigDecimal getTotalAssets() {
        return totalAssets;
    }
    
    public void setTotalAssets(BigDecimal totalAssets) {
        this.totalAssets = totalAssets;
    }
    
    public BigDecimal getTotalLiabilities() {
        return totalLiabilities;
    }
    
    public void setTotalLiabilities(BigDecimal totalLiabilities) {
        this.totalLiabilities = totalLiabilities;
    }
    
    public BigDecimal getNetWorth() {
        return netWorth;
    }
    
    public void setNetWorth(BigDecimal netWorth) {
        this.netWorth = netWorth;
    }
    
    public long getTodayTransactions() {
        return todayTransactions;
    }
    
    public void setTodayTransactions(long todayTransactions) {
        this.todayTransactions = todayTransactions;
    }
    
    public BigDecimal getTodayTransactionVolume() {
        return todayTransactionVolume;
    }
    
    public void setTodayTransactionVolume(BigDecimal todayTransactionVolume) {
        this.todayTransactionVolume = todayTransactionVolume;
    }
    
    public long getTodayNewCustomers() {
        return todayNewCustomers;
    }
    
    public void setTodayNewCustomers(long todayNewCustomers) {
        this.todayNewCustomers = todayNewCustomers;
    }
    
    public long getTodayNewAccounts() {
        return todayNewAccounts;
    }
    
    public void setTodayNewAccounts(long todayNewAccounts) {
        this.todayNewAccounts = todayNewAccounts;
    }
    
    public long getActiveAccounts() {
        return activeAccounts;
    }
    
    public void setActiveAccounts(long activeAccounts) {
        this.activeAccounts = activeAccounts;
    }
    
    public long getInactiveAccounts() {
        return inactiveAccounts;
    }
    
    public void setInactiveAccounts(long inactiveAccounts) {
        this.inactiveAccounts = inactiveAccounts;
    }
    
    public long getFrozenAccounts() {
        return frozenAccounts;
    }
    
    public void setFrozenAccounts(long frozenAccounts) {
        this.frozenAccounts = frozenAccounts;
    }
    
    public long getClosedAccounts() {
        return closedAccounts;
    }
    
    public void setClosedAccounts(long closedAccounts) {
        this.closedAccounts = closedAccounts;
    }
    
    public long getActiveCards() {
        return activeCards;
    }
    
    public void setActiveCards(long activeCards) {
        this.activeCards = activeCards;
    }
    
    public long getBlockedCards() {
        return blockedCards;
    }
    
    public void setBlockedCards(long blockedCards) {
        this.blockedCards = blockedCards;
    }
    
    public long getExpiredCards() {
        return expiredCards;
    }
    
    public void setExpiredCards(long expiredCards) {
        this.expiredCards = expiredCards;
    }
    
    public long getPendingCards() {
        return pendingCards;
    }
    
    public void setPendingCards(long pendingCards) {
        this.pendingCards = pendingCards;
    }
    
    public long getPendingLoans() {
        return pendingLoans;
    }
    
    public void setPendingLoans(long pendingLoans) {
        this.pendingLoans = pendingLoans;
    }
    
    public long getApprovedLoans() {
        return approvedLoans;
    }
    
    public void setApprovedLoans(long approvedLoans) {
        this.approvedLoans = approvedLoans;
    }
    
    public long getRejectedLoans() {
        return rejectedLoans;
    }
    
    public void setRejectedLoans(long rejectedLoans) {
        this.rejectedLoans = rejectedLoans;
    }
    
    public long getActiveLoans() {
        return activeLoans;
    }
    
    public void setActiveLoans(long activeLoans) {
        this.activeLoans = activeLoans;
    }
    
    public long getDefaultedLoans() {
        return defaultedLoans;
    }
    
    public void setDefaultedLoans(long defaultedLoans) {
        this.defaultedLoans = defaultedLoans;
    }
    
    public BigDecimal getTotalOutstandingAmount() {
        return totalOutstandingAmount;
    }
    
    public void setTotalOutstandingAmount(BigDecimal totalOutstandingAmount) {
        this.totalOutstandingAmount = totalOutstandingAmount;
    }
    
    public String getSystemStatus() {
        return systemStatus;
    }
    
    public void setSystemStatus(String systemStatus) {
        this.systemStatus = systemStatus;
    }
    
    public LocalDateTime getLastUpdated() {
        return lastUpdated;
    }
    
    public void setLastUpdated(LocalDateTime lastUpdated) {
        this.lastUpdated = lastUpdated;
    }
    
    public List<Transaction> getRecentTransactions() {
        return recentTransactions;
    }
    
    public void setRecentTransactions(List<Transaction> recentTransactions) {
        this.recentTransactions = recentTransactions;
    }
    
    public List<User> getRecentCustomers() {
        return recentCustomers;
    }
    
    public void setRecentCustomers(List<User> recentCustomers) {
        this.recentCustomers = recentCustomers;
    }
}
