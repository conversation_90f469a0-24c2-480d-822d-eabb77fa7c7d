package org.example.abcbanking.dto;

import org.example.abcbanking.model.CardType;

import jakarta.validation.constraints.*;
import java.math.BigDecimal;

public class CreateCardRequest {
    
    @NotBlank(message = "Card holder name is required")
    @Size(max = 100, message = "Card holder name must not exceed 100 characters")
    private String cardHolderName;
    
    @NotNull(message = "Card type is required")
    private CardType cardType;
    
    @NotNull(message = "Account ID is required")
    private Long accountId;
    
    @DecimalMin(value = "1000.0", message = "Daily limit must be at least 1000")
    @DecimalMax(value = "1000000.0", message = "Daily limit must not exceed 1000000")
    private BigDecimal dailyLimit;
    
    @DecimalMin(value = "5000.0", message = "Monthly limit must be at least 5000")
    @DecimalMax(value = "5000000.0", message = "Monthly limit must not exceed 5000000")
    private BigDecimal monthlyLimit;
    
    @DecimalMin(value = "0.0", message = "Credit limit must be non-negative")
    @DecimalMax(value = "********.0", message = "Credit limit must not exceed ********")
    private BigDecimal creditLimit;
    
    private Boolean contactlessEnabled = true;
    private Boolean onlineTransactionsEnabled = true;
    private Boolean internationalTransactionsEnabled = false;
    private Boolean atmTransactionsEnabled = true;
    private Boolean posTransactionsEnabled = true;
    
    @Size(max = 1000, message = "Card features must not exceed 1000 characters")
    private String cardFeatures;
    
    // Constructors
    public CreateCardRequest() {}
    
    public CreateCardRequest(String cardHolderName, CardType cardType, Long accountId) {
        this.cardHolderName = cardHolderName;
        this.cardType = cardType;
        this.accountId = accountId;
    }
    
    // Getters and Setters
    public String getCardHolderName() { return cardHolderName; }
    public void setCardHolderName(String cardHolderName) { this.cardHolderName = cardHolderName; }
    
    public CardType getCardType() { return cardType; }
    public void setCardType(CardType cardType) { this.cardType = cardType; }
    
    public Long getAccountId() { return accountId; }
    public void setAccountId(Long accountId) { this.accountId = accountId; }
    
    public BigDecimal getDailyLimit() { return dailyLimit; }
    public void setDailyLimit(BigDecimal dailyLimit) { this.dailyLimit = dailyLimit; }
    
    public BigDecimal getMonthlyLimit() { return monthlyLimit; }
    public void setMonthlyLimit(BigDecimal monthlyLimit) { this.monthlyLimit = monthlyLimit; }
    
    public BigDecimal getCreditLimit() { return creditLimit; }
    public void setCreditLimit(BigDecimal creditLimit) { this.creditLimit = creditLimit; }
    
    public Boolean getContactlessEnabled() { return contactlessEnabled; }
    public void setContactlessEnabled(Boolean contactlessEnabled) { this.contactlessEnabled = contactlessEnabled; }
    
    public Boolean getOnlineTransactionsEnabled() { return onlineTransactionsEnabled; }
    public void setOnlineTransactionsEnabled(Boolean onlineTransactionsEnabled) { this.onlineTransactionsEnabled = onlineTransactionsEnabled; }
    
    public Boolean getInternationalTransactionsEnabled() { return internationalTransactionsEnabled; }
    public void setInternationalTransactionsEnabled(Boolean internationalTransactionsEnabled) { this.internationalTransactionsEnabled = internationalTransactionsEnabled; }
    
    public Boolean getAtmTransactionsEnabled() { return atmTransactionsEnabled; }
    public void setAtmTransactionsEnabled(Boolean atmTransactionsEnabled) { this.atmTransactionsEnabled = atmTransactionsEnabled; }
    
    public Boolean getPosTransactionsEnabled() { return posTransactionsEnabled; }
    public void setPosTransactionsEnabled(Boolean posTransactionsEnabled) { this.posTransactionsEnabled = posTransactionsEnabled; }
    
    public String getCardFeatures() { return cardFeatures; }
    public void setCardFeatures(String cardFeatures) { this.cardFeatures = cardFeatures; }
}
