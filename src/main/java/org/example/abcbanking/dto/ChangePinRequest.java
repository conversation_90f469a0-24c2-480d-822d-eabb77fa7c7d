package org.example.abcbanking.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;

public class ChangePinRequest {
    
    @NotBlank(message = "Old PIN is required")
    private String oldPin;
    
    @NotBlank(message = "New PIN is required")
    @Size(min = 4, max = 6, message = "PIN must be 4-6 digits")
    @Pattern(regexp = "\\d+", message = "PIN must contain only digits")
    private String newPin;
    
    // Constructors
    public ChangePinRequest() {}
    
    public ChangePinRequest(String oldPin, String newPin) {
        this.oldPin = oldPin;
        this.newPin = newPin;
    }
    
    // Getters and Setters
    public String getOldPin() {
        return oldPin;
    }
    
    public void setOldPin(String oldPin) {
        this.oldPin = oldPin;
    }
    
    public String getNewPin() {
        return newPin;
    }
    
    public void setNewPin(String newPin) {
        this.newPin = newPin;
    }
}
