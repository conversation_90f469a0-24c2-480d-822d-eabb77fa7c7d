package org.example.abcbanking.dto;

import java.math.BigDecimal;

public class AdminLoanAnalyticsResponse {
    private int totalLoans;
    private int loansInPeriod;
    private BigDecimal totalDisbursedAmount;
    private int overdueEmisCount;
    private int pendingLoans;
    private int activeLoans;
    private int defaultedLoans;
    private int approvedLoans;
    private int rejectedLoans;
    private int closedLoans;
    private BigDecimal totalOutstandingAmount;
    private BigDecimal averageLoanAmount;
    private BigDecimal totalInterestEarned;
    private double approvalRate;
    private double defaultRate;

    // Default constructor
    public AdminLoanAnalyticsResponse() {}

    // Getters and Setters
    public int getTotalLoans() {
        return totalLoans;
    }

    public void setTotalLoans(int totalLoans) {
        this.totalLoans = totalLoans;
    }

    public int getLoansInPeriod() {
        return loansInPeriod;
    }

    public void setLoansInPeriod(int loansInPeriod) {
        this.loansInPeriod = loansInPeriod;
    }

    public BigDecimal getTotalDisbursedAmount() {
        return totalDisbursedAmount;
    }

    public void setTotalDisbursedAmount(BigDecimal totalDisbursedAmount) {
        this.totalDisbursedAmount = totalDisbursedAmount;
    }

    public int getOverdueEmisCount() {
        return overdueEmisCount;
    }

    public void setOverdueEmisCount(int overdueEmisCount) {
        this.overdueEmisCount = overdueEmisCount;
    }

    public int getPendingLoans() {
        return pendingLoans;
    }

    public void setPendingLoans(int pendingLoans) {
        this.pendingLoans = pendingLoans;
    }

    public int getActiveLoans() {
        return activeLoans;
    }

    public void setActiveLoans(int activeLoans) {
        this.activeLoans = activeLoans;
    }

    public int getDefaultedLoans() {
        return defaultedLoans;
    }

    public void setDefaultedLoans(int defaultedLoans) {
        this.defaultedLoans = defaultedLoans;
    }

    public int getApprovedLoans() {
        return approvedLoans;
    }

    public void setApprovedLoans(int approvedLoans) {
        this.approvedLoans = approvedLoans;
    }

    public int getRejectedLoans() {
        return rejectedLoans;
    }

    public void setRejectedLoans(int rejectedLoans) {
        this.rejectedLoans = rejectedLoans;
    }

    public int getClosedLoans() {
        return closedLoans;
    }

    public void setClosedLoans(int closedLoans) {
        this.closedLoans = closedLoans;
    }

    public BigDecimal getTotalOutstandingAmount() {
        return totalOutstandingAmount;
    }

    public void setTotalOutstandingAmount(BigDecimal totalOutstandingAmount) {
        this.totalOutstandingAmount = totalOutstandingAmount;
    }

    public BigDecimal getAverageLoanAmount() {
        return averageLoanAmount;
    }

    public void setAverageLoanAmount(BigDecimal averageLoanAmount) {
        this.averageLoanAmount = averageLoanAmount;
    }

    public BigDecimal getTotalInterestEarned() {
        return totalInterestEarned;
    }

    public void setTotalInterestEarned(BigDecimal totalInterestEarned) {
        this.totalInterestEarned = totalInterestEarned;
    }

    public double getApprovalRate() {
        return approvalRate;
    }

    public void setApprovalRate(double approvalRate) {
        this.approvalRate = approvalRate;
    }

    public double getDefaultRate() {
        return defaultRate;
    }

    public void setDefaultRate(double defaultRate) {
        this.defaultRate = defaultRate;
    }
}
