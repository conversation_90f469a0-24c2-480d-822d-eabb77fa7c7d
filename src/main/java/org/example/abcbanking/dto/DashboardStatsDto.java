package org.example.abcbanking.dto;

import java.math.BigDecimal;

public class DashboardStatsDto {
    private int totalAccounts;
    private int totalCustomers;
    private BigDecimal totalBalance;
    private int totalTransactions;
    private int dailyTransactions;
    private double monthlyGrowth;
    private int activeUsers;
    private BigDecimal todayVolume;
    private int pendingTransactions;
    private int newAccountsThisMonth;

    // Default constructor
    public DashboardStatsDto() {}

    // Getters and Setters
    public int getTotalAccounts() {
        return totalAccounts;
    }

    public void setTotalAccounts(int totalAccounts) {
        this.totalAccounts = totalAccounts;
    }

    public int getTotalCustomers() {
        return totalCustomers;
    }

    public void setTotalCustomers(int totalCustomers) {
        this.totalCustomers = totalCustomers;
    }

    public BigDecimal getTotalBalance() {
        return totalBalance;
    }

    public void setTotalBalance(BigDecimal totalBalance) {
        this.totalBalance = totalBalance;
    }

    public int getTotalTransactions() {
        return totalTransactions;
    }

    public void setTotalTransactions(int totalTransactions) {
        this.totalTransactions = totalTransactions;
    }

    public int getDailyTransactions() {
        return dailyTransactions;
    }

    public void setDailyTransactions(int dailyTransactions) {
        this.dailyTransactions = dailyTransactions;
    }

    public double getMonthlyGrowth() {
        return monthlyGrowth;
    }

    public void setMonthlyGrowth(double monthlyGrowth) {
        this.monthlyGrowth = monthlyGrowth;
    }

    public int getActiveUsers() {
        return activeUsers;
    }

    public void setActiveUsers(int activeUsers) {
        this.activeUsers = activeUsers;
    }

    public BigDecimal getTodayVolume() {
        return todayVolume;
    }

    public void setTodayVolume(BigDecimal todayVolume) {
        this.todayVolume = todayVolume;
    }

    public int getPendingTransactions() {
        return pendingTransactions;
    }

    public void setPendingTransactions(int pendingTransactions) {
        this.pendingTransactions = pendingTransactions;
    }

    public int getNewAccountsThisMonth() {
        return newAccountsThisMonth;
    }

    public void setNewAccountsThisMonth(int newAccountsThisMonth) {
        this.newAccountsThisMonth = newAccountsThisMonth;
    }
}
