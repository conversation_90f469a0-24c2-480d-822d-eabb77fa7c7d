package org.example.abcbanking.dto;

import jakarta.validation.constraints.*;
import java.math.BigDecimal;

public class EmiPaymentRequest {
    
    @NotNull(message = "Payment amount is required")
    @DecimalMin(value = "1.0", message = "Payment amount must be positive")
    private BigDecimal amount;
    
    @NotBlank(message = "Payment method is required")
    @Size(max = 50, message = "Payment method cannot exceed 50 characters")
    private String paymentMethod;
    
    @Size(max = 200, message = "Remarks cannot exceed 200 characters")
    private String remarks;

    // Default constructor
    public EmiPaymentRequest() {}

    // Constructor
    public EmiPaymentRequest(BigDecimal amount, String paymentMethod) {
        this.amount = amount;
        this.paymentMethod = paymentMethod;
    }

    // Getters and Setters
    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getPaymentMethod() {
        return paymentMethod;
    }

    public void setPaymentMethod(String paymentMethod) {
        this.paymentMethod = paymentMethod;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }
}
