package org.example.abcbanking.dto;

public class CardStatsDto {
    
    private Long totalCards;
    private Long activeCards;
    private Long blockedCards;
    private Long expiringCards;
    private Long creditCards;
    private Long debitCards;
    
    // Constructors
    public CardStatsDto() {}
    
    public CardStatsDto(Long totalCards, Long activeCards, Long blockedCards, Long expiringCards) {
        this.totalCards = totalCards;
        this.activeCards = activeCards;
        this.blockedCards = blockedCards;
        this.expiringCards = expiringCards;
    }
    
    // Getters and Setters
    public Long getTotalCards() {
        return totalCards;
    }
    
    public void setTotalCards(Long totalCards) {
        this.totalCards = totalCards;
    }
    
    public Long getActiveCards() {
        return activeCards;
    }
    
    public void setActiveCards(Long activeCards) {
        this.activeCards = activeCards;
    }
    
    public Long getBlockedCards() {
        return blockedCards;
    }
    
    public void setBlockedCards(Long blockedCards) {
        this.blockedCards = blockedCards;
    }
    
    public Long getExpiringCards() {
        return expiringCards;
    }
    
    public void setExpiringCards(Long expiringCards) {
        this.expiringCards = expiringCards;
    }
    
    public Long getCreditCards() {
        return creditCards;
    }
    
    public void setCreditCards(Long creditCards) {
        this.creditCards = creditCards;
    }
    
    public Long getDebitCards() {
        return debitCards;
    }
    
    public void setDebitCards(Long debitCards) {
        this.debitCards = debitCards;
    }
}
