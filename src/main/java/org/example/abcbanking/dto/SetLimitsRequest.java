package org.example.abcbanking.dto;

import jakarta.validation.constraints.DecimalMin;

import java.math.BigDecimal;

public class SetLimitsRequest {
    
    @DecimalMin(value = "1000.0", message = "Daily limit must be at least ₹1,000")
    private BigDecimal dailyLimit;
    
    @DecimalMin(value = "5000.0", message = "Monthly limit must be at least ₹5,000")
    private BigDecimal monthlyLimit;
    
    @DecimalMin(value = "0.0", message = "Credit limit must be non-negative")
    private BigDecimal creditLimit;
    
    // Constructors
    public SetLimitsRequest() {}
    
    public SetLimitsRequest(BigDecimal dailyLimit, BigDecimal monthlyLimit, BigDecimal creditLimit) {
        this.dailyLimit = dailyLimit;
        this.monthlyLimit = monthlyLimit;
        this.creditLimit = creditLimit;
    }
    
    // Getters and Setters
    public BigDecimal getDailyLimit() {
        return dailyLimit;
    }
    
    public void setDailyLimit(BigDecimal dailyLimit) {
        this.dailyLimit = dailyLimit;
    }
    
    public BigDecimal getMonthlyLimit() {
        return monthlyLimit;
    }
    
    public void setMonthlyLimit(BigDecimal monthlyLimit) {
        this.monthlyLimit = monthlyLimit;
    }
    
    public BigDecimal getCreditLimit() {
        return creditLimit;
    }
    
    public void setCreditLimit(BigDecimal creditLimit) {
        this.creditLimit = creditLimit;
    }
}
