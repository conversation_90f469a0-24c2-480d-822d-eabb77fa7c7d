package org.example.abcbanking.dto;

import jakarta.validation.constraints.NotBlank;

public class ReportCardRequest {
    
    @NotBlank(message = "Reason is required")
    private String reason;
    
    // Constructors
    public ReportCardRequest() {}
    
    public ReportCardRequest(String reason) {
        this.reason = reason;
    }
    
    // Getters and Setters
    public String getReason() {
        return reason;
    }
    
    public void setReason(String reason) {
        this.reason = reason;
    }
}
