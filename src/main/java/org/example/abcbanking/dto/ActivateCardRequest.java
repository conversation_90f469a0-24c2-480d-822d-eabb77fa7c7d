package org.example.abcbanking.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;

public class ActivateCardRequest {
    
    @NotBlank(message = "PIN is required")
    @Size(min = 4, max = 6, message = "PIN must be 4-6 digits")
    @Pattern(regexp = "\\d+", message = "PIN must contain only digits")
    private String pin;
    
    // Constructors
    public ActivateCardRequest() {}
    
    public ActivateCardRequest(String pin) {
        this.pin = pin;
    }
    
    // Getters and Setters
    public String getPin() {
        return pin;
    }
    
    public void setPin(String pin) {
        this.pin = pin;
    }
}
