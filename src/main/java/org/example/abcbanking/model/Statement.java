package org.example.abcbanking.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Entity
@Table(name = "statements")
public class Statement extends BaseEntity {
    
    @NotBlank
    @Size(max = 50)
    @Column(name = "statement_id", unique = true, nullable = false)
    private String statementId;
    
    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private StatementType type;
    
    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private StatementPeriod period;
    
    @NotNull
    @Column(name = "from_date", nullable = false)
    private LocalDate fromDate;
    
    @NotNull
    @Column(name = "to_date", nullable = false)
    private LocalDate toDate;
    
    @NotNull
    @Column(name = "generation_date", nullable = false)
    private LocalDateTime generationDate;
    
    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private StatementStatus status = StatementStatus.GENERATING;
    
    @Column(name = "opening_balance", precision = 19, scale = 2)
    private BigDecimal openingBalance = BigDecimal.ZERO;
    
    @Column(name = "closing_balance", precision = 19, scale = 2)
    private BigDecimal closingBalance = BigDecimal.ZERO;
    
    @Column(name = "total_credits", precision = 19, scale = 2)
    private BigDecimal totalCredits = BigDecimal.ZERO;
    
    @Column(name = "total_debits", precision = 19, scale = 2)
    private BigDecimal totalDebits = BigDecimal.ZERO;
    
    @Column(name = "transaction_count")
    private Integer transactionCount = 0;
    
    @Column(name = "credit_count")
    private Integer creditCount = 0;
    
    @Column(name = "debit_count")
    private Integer debitCount = 0;
    
    @Column(name = "average_balance", precision = 19, scale = 2)
    private BigDecimal averageBalance = BigDecimal.ZERO;
    
    @Column(name = "minimum_balance", precision = 19, scale = 2)
    private BigDecimal minimumBalance = BigDecimal.ZERO;
    
    @Column(name = "maximum_balance", precision = 19, scale = 2)
    private BigDecimal maximumBalance = BigDecimal.ZERO;
    
    @Column(name = "interest_earned", precision = 10, scale = 2)
    private BigDecimal interestEarned = BigDecimal.ZERO;
    
    @Column(name = "charges_deducted", precision = 10, scale = 2)
    private BigDecimal chargesDeducted = BigDecimal.ZERO;
    
    @Column(name = "file_path")
    private String filePath;
    
    @Column(name = "file_name")
    private String fileName;
    
    @Column(name = "file_size")
    private Long fileSize;
    
    @Column(name = "file_format")
    private String fileFormat = "PDF";
    
    @Column(name = "download_count")
    private Integer downloadCount = 0;
    
    @Column(name = "last_downloaded_at")
    private LocalDateTime lastDownloadedAt;
    
    @Column(name = "email_sent")
    private Boolean emailSent = false;
    
    @Column(name = "email_sent_at")
    private LocalDateTime emailSentAt;
    
    @Column(name = "password_protected")
    private Boolean passwordProtected = false;
    
    @Column(name = "password_hint")
    private String passwordHint;
    
    @Column(name = "expires_at")
    private LocalDateTime expiresAt;
    
    @Column(name = "checksum")
    private String checksum;
    
    @Column(name = "digital_signature")
    private String digitalSignature;
    
    @Column(name = "metadata", length = 1000)
    private String metadata; // JSON string for additional data
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "account_id", nullable = false)
    private Account account;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "generated_by")
    private User generatedBy;
    
    // Constructors
    public Statement() {}
    
    public Statement(String statementId, StatementType type, StatementPeriod period, 
                    LocalDate fromDate, LocalDate toDate, Account account) {
        this.statementId = statementId;
        this.type = type;
        this.period = period;
        this.fromDate = fromDate;
        this.toDate = toDate;
        this.account = account;
        this.generationDate = LocalDateTime.now();
    }
    
    // Getters and Setters
    public String getStatementId() { return statementId; }
    public void setStatementId(String statementId) { this.statementId = statementId; }
    
    public StatementType getType() { return type; }
    public void setType(StatementType type) { this.type = type; }
    
    public StatementPeriod getPeriod() { return period; }
    public void setPeriod(StatementPeriod period) { this.period = period; }
    
    public LocalDate getFromDate() { return fromDate; }
    public void setFromDate(LocalDate fromDate) { this.fromDate = fromDate; }
    
    public LocalDate getToDate() { return toDate; }
    public void setToDate(LocalDate toDate) { this.toDate = toDate; }
    
    public LocalDateTime getGenerationDate() { return generationDate; }
    public void setGenerationDate(LocalDateTime generationDate) { this.generationDate = generationDate; }
    
    public StatementStatus getStatus() { return status; }
    public void setStatus(StatementStatus status) { this.status = status; }
    
    public BigDecimal getOpeningBalance() { return openingBalance; }
    public void setOpeningBalance(BigDecimal openingBalance) { this.openingBalance = openingBalance; }
    
    public BigDecimal getClosingBalance() { return closingBalance; }
    public void setClosingBalance(BigDecimal closingBalance) { this.closingBalance = closingBalance; }
    
    public BigDecimal getTotalCredits() { return totalCredits; }
    public void setTotalCredits(BigDecimal totalCredits) { this.totalCredits = totalCredits; }
    
    public BigDecimal getTotalDebits() { return totalDebits; }
    public void setTotalDebits(BigDecimal totalDebits) { this.totalDebits = totalDebits; }
    
    public Integer getTransactionCount() { return transactionCount; }
    public void setTransactionCount(Integer transactionCount) { this.transactionCount = transactionCount; }
    
    public Integer getCreditCount() { return creditCount; }
    public void setCreditCount(Integer creditCount) { this.creditCount = creditCount; }
    
    public Integer getDebitCount() { return debitCount; }
    public void setDebitCount(Integer debitCount) { this.debitCount = debitCount; }
    
    public BigDecimal getAverageBalance() { return averageBalance; }
    public void setAverageBalance(BigDecimal averageBalance) { this.averageBalance = averageBalance; }
    
    public BigDecimal getMinimumBalance() { return minimumBalance; }
    public void setMinimumBalance(BigDecimal minimumBalance) { this.minimumBalance = minimumBalance; }
    
    public BigDecimal getMaximumBalance() { return maximumBalance; }
    public void setMaximumBalance(BigDecimal maximumBalance) { this.maximumBalance = maximumBalance; }
    
    public BigDecimal getInterestEarned() { return interestEarned; }
    public void setInterestEarned(BigDecimal interestEarned) { this.interestEarned = interestEarned; }
    
    public BigDecimal getChargesDeducted() { return chargesDeducted; }
    public void setChargesDeducted(BigDecimal chargesDeducted) { this.chargesDeducted = chargesDeducted; }
    
    public String getFilePath() { return filePath; }
    public void setFilePath(String filePath) { this.filePath = filePath; }
    
    public String getFileName() { return fileName; }
    public void setFileName(String fileName) { this.fileName = fileName; }
    
    public Long getFileSize() { return fileSize; }
    public void setFileSize(Long fileSize) { this.fileSize = fileSize; }
    
    public String getFileFormat() { return fileFormat; }
    public void setFileFormat(String fileFormat) { this.fileFormat = fileFormat; }
    
    public Integer getDownloadCount() { return downloadCount; }
    public void setDownloadCount(Integer downloadCount) { this.downloadCount = downloadCount; }
    
    public LocalDateTime getLastDownloadedAt() { return lastDownloadedAt; }
    public void setLastDownloadedAt(LocalDateTime lastDownloadedAt) { this.lastDownloadedAt = lastDownloadedAt; }
    
    public Boolean getEmailSent() { return emailSent; }
    public void setEmailSent(Boolean emailSent) { this.emailSent = emailSent; }
    
    public LocalDateTime getEmailSentAt() { return emailSentAt; }
    public void setEmailSentAt(LocalDateTime emailSentAt) { this.emailSentAt = emailSentAt; }
    
    public Boolean getPasswordProtected() { return passwordProtected; }
    public void setPasswordProtected(Boolean passwordProtected) { this.passwordProtected = passwordProtected; }
    
    public String getPasswordHint() { return passwordHint; }
    public void setPasswordHint(String passwordHint) { this.passwordHint = passwordHint; }
    
    public LocalDateTime getExpiresAt() { return expiresAt; }
    public void setExpiresAt(LocalDateTime expiresAt) { this.expiresAt = expiresAt; }
    
    public String getChecksum() { return checksum; }
    public void setChecksum(String checksum) { this.checksum = checksum; }
    
    public String getDigitalSignature() { return digitalSignature; }
    public void setDigitalSignature(String digitalSignature) { this.digitalSignature = digitalSignature; }
    
    public String getMetadata() { return metadata; }
    public void setMetadata(String metadata) { this.metadata = metadata; }
    
    public Account getAccount() { return account; }
    public void setAccount(Account account) { this.account = account; }
    
    public User getGeneratedBy() { return generatedBy; }
    public void setGeneratedBy(User generatedBy) { this.generatedBy = generatedBy; }
    
    // Helper methods
    public void markAsDownloaded() {
        this.downloadCount++;
        this.lastDownloadedAt = LocalDateTime.now();
    }
    
    public void markAsEmailSent() {
        this.emailSent = true;
        this.emailSentAt = LocalDateTime.now();
    }
    
    public boolean isExpired() {
        return this.expiresAt != null && LocalDateTime.now().isAfter(this.expiresAt);
    }
    
    public String getFormattedPeriod() {
        return fromDate.toString() + " to " + toDate.toString();
    }
    
    public String getFormattedFileSize() {
        if (fileSize == null) return "Unknown";
        
        if (fileSize < 1024) return fileSize + " B";
        if (fileSize < 1024 * 1024) return String.format("%.1f KB", fileSize / 1024.0);
        return String.format("%.1f MB", fileSize / (1024.0 * 1024.0));
    }
}
