package org.example.abcbanking.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.*;

import java.time.LocalDateTime;

@Entity
@Table(name = "audit_logs")
public class AuditLog {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @NotNull
    @Column(name = "timestamp", nullable = false)
    private LocalDateTime timestamp = LocalDateTime.now();
    
    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private AuditAction action;
    
    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private AuditEntityType entityType;
    
    @Column(name = "entity_id")
    private String entityId;
    
    @Column(name = "user_id")
    private Long userId;
    
    @Size(max = 100)
    @Column(name = "username")
    private String username;
    
    @Size(max = 45)
    @Column(name = "ip_address")
    private String ipAddress;
    
    @Size(max = 500)
    @Column(name = "user_agent")
    private String userAgent;
    
    @Size(max = 100)
    @Column(name = "session_id")
    private String sessionId;
    
    @Size(max = 200)
    @Column(name = "description")
    private String description;
    
    @Column(name = "old_values", length = 5000)
    private String oldValues; // JSON string of old values
    
    @Column(name = "new_values", length = 5000)
    private String newValues; // JSON string of new values
    
    @Column(name = "changed_fields", length = 1000)
    private String changedFields; // Comma-separated list of changed fields
    
    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private AuditResult result = AuditResult.SUCCESS;
    
    @Column(name = "error_message")
    private String errorMessage;
    
    @Column(name = "request_id")
    private String requestId;
    
    @Column(name = "correlation_id")
    private String correlationId;
    
    @Column(name = "module")
    private String module; // AUTHENTICATION, TRANSACTION, ACCOUNT, etc.
    
    @Column(name = "sub_module")
    private String subModule;
    
    @Column(name = "risk_score")
    private Integer riskScore;
    
    @Column(name = "compliance_flag")
    private Boolean complianceFlag = false;
    
    @Column(name = "retention_period_days")
    private Integer retentionPeriodDays = 2555; // 7 years default
    
    @Column(name = "metadata", length = 2000)
    private String metadata; // JSON string for additional data
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "performed_by")
    private User performedBy;
    
    // Constructors
    public AuditLog() {}
    
    public AuditLog(AuditAction action, AuditEntityType entityType, String entityId, User performedBy) {
        this.action = action;
        this.entityType = entityType;
        this.entityId = entityId;
        this.performedBy = performedBy;
        this.userId = performedBy != null ? performedBy.getId() : null;
        this.username = performedBy != null ? performedBy.getUsername() : null;
    }
    
    // Getters and Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    
    public LocalDateTime getTimestamp() { return timestamp; }
    public void setTimestamp(LocalDateTime timestamp) { this.timestamp = timestamp; }
    
    public AuditAction getAction() { return action; }
    public void setAction(AuditAction action) { this.action = action; }
    
    public AuditEntityType getEntityType() { return entityType; }
    public void setEntityType(AuditEntityType entityType) { this.entityType = entityType; }
    
    public String getEntityId() { return entityId; }
    public void setEntityId(String entityId) { this.entityId = entityId; }
    
    public Long getUserId() { return userId; }
    public void setUserId(Long userId) { this.userId = userId; }
    
    public String getUsername() { return username; }
    public void setUsername(String username) { this.username = username; }
    
    public String getIpAddress() { return ipAddress; }
    public void setIpAddress(String ipAddress) { this.ipAddress = ipAddress; }
    
    public String getUserAgent() { return userAgent; }
    public void setUserAgent(String userAgent) { this.userAgent = userAgent; }
    
    public String getSessionId() { return sessionId; }
    public void setSessionId(String sessionId) { this.sessionId = sessionId; }
    
    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }
    
    public String getOldValues() { return oldValues; }
    public void setOldValues(String oldValues) { this.oldValues = oldValues; }
    
    public String getNewValues() { return newValues; }
    public void setNewValues(String newValues) { this.newValues = newValues; }
    
    public String getChangedFields() { return changedFields; }
    public void setChangedFields(String changedFields) { this.changedFields = changedFields; }
    
    public AuditResult getResult() { return result; }
    public void setResult(AuditResult result) { this.result = result; }
    
    public String getErrorMessage() { return errorMessage; }
    public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
    
    public String getRequestId() { return requestId; }
    public void setRequestId(String requestId) { this.requestId = requestId; }
    
    public String getCorrelationId() { return correlationId; }
    public void setCorrelationId(String correlationId) { this.correlationId = correlationId; }
    
    public String getModule() { return module; }
    public void setModule(String module) { this.module = module; }
    
    public String getSubModule() { return subModule; }
    public void setSubModule(String subModule) { this.subModule = subModule; }
    
    public Integer getRiskScore() { return riskScore; }
    public void setRiskScore(Integer riskScore) { this.riskScore = riskScore; }
    
    public Boolean getComplianceFlag() { return complianceFlag; }
    public void setComplianceFlag(Boolean complianceFlag) { this.complianceFlag = complianceFlag; }
    
    public Integer getRetentionPeriodDays() { return retentionPeriodDays; }
    public void setRetentionPeriodDays(Integer retentionPeriodDays) { this.retentionPeriodDays = retentionPeriodDays; }
    
    public String getMetadata() { return metadata; }
    public void setMetadata(String metadata) { this.metadata = metadata; }
    
    public User getPerformedBy() { return performedBy; }
    public void setPerformedBy(User performedBy) { this.performedBy = performedBy; }
    
    // Helper methods
    public static AuditLog createLoginAudit(User user, String ipAddress, boolean success) {
        AuditLog audit = new AuditLog();
        audit.setAction(AuditAction.LOGIN);
        audit.setEntityType(AuditEntityType.USER);
        audit.setEntityId(user.getId().toString());
        audit.setPerformedBy(user);
        audit.setUserId(user.getId());
        audit.setUsername(user.getUsername());
        audit.setIpAddress(ipAddress);
        audit.setResult(success ? AuditResult.SUCCESS : AuditResult.FAILURE);
        audit.setModule("AUTHENTICATION");
        audit.setDescription(success ? "User logged in successfully" : "User login failed");
        return audit;
    }
    
    public static AuditLog createTransactionAudit(Transaction transaction, User user, String ipAddress) {
        AuditLog audit = new AuditLog();
        audit.setAction(AuditAction.CREATE);
        audit.setEntityType(AuditEntityType.TRANSACTION);
        audit.setEntityId(transaction.getId().toString());
        audit.setPerformedBy(user);
        audit.setUserId(user.getId());
        audit.setUsername(user.getUsername());
        audit.setIpAddress(ipAddress);
        audit.setResult(AuditResult.SUCCESS);
        audit.setModule("TRANSACTION");
        audit.setDescription("Transaction created: " + transaction.getType());
        return audit;
    }
}
