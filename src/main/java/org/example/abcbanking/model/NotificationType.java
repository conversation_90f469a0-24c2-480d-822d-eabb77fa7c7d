package org.example.abcbanking.model;

public enum NotificationType {
    TRANSACTION_ALERT,
    SECURITY_ALERT,
    ACCOUNT_UPDATE,
    LOAN_REMINDER,
    PAYMENT_DUE,
    CARD_ALERT,
    BALANCE_LOW,
    BALANCE_HIGH,
    LOGI<PERSON>_ALERT,
    PASS<PERSON>OR<PERSON>_CHANGE,
    <PERSON><PERSON><PERSON><PERSON>_UPDATE,
    PR<PERSON><PERSON><PERSON>AL,
    SY<PERSON>EM_MAINTENANCE,
    WELCOME,
    VERIFICAT<PERSON>,
    FRAUD_ALERT,
    <PERSON><PERSON><PERSON>_EXCEEDED,
    ACCOUNT_LOCKED,
    CARD_BLOCKED,
    LOAN_APPROVED,
    LOAN_REJECTED,
    EMI_DUE,
    EMI_OVERDUE,
    STATEMENT_READY,
    <PERSON>Y<PERSON>_REMINDER,
    DOCUMENT_REQUIRED,
    GENERAL
}
