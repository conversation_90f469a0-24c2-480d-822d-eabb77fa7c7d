package org.example.abcbanking.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Entity
@Table(name = "accounts")
public class Account extends BaseEntity {
    
    @NotBlank
    @Size(max = 20)
    @Column(name = "account_number", unique = true, nullable = false)
    private String accountNumber;

    @NotBlank
    @Size(max = 100)
    @Column(name = "account_holder_name", nullable = false)
    private String accountHolderName;

    @Size(max = 15)
    @Column(name = "contact_number")
    private String contactNumber;

    @Size(max = 100)
    @Column(name = "email_address")
    private String emailAddress;

    @Size(max = 500)
    @Column(name = "address")
    private String address;
    
    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private AccountType type;
    
    @NotNull
    @DecimalMin("0.0")
    @Column(precision = 19, scale = 2, nullable = false)
    private BigDecimal balance = BigDecimal.ZERO;

    @Size(max = 3)
    @Column(name = "currency", length = 3)
    private String currency = "INR";

    @DecimalMin("0.0")
    @Column(name = "credit_limit", precision = 19, scale = 2)
    private BigDecimal creditLimit = BigDecimal.ZERO;
    
    @DecimalMin("0.0")
    @Column(name = "overdraft_limit", precision = 19, scale = 2)
    private BigDecimal overdraftLimit = BigDecimal.ZERO;
    
    @DecimalMin("0.0")
    @Column(name = "available_balance", precision = 19, scale = 2)
    private BigDecimal availableBalance = BigDecimal.ZERO;
    
    @DecimalMin("0.0")
    @Column(name = "minimum_balance", precision = 19, scale = 2)
    private BigDecimal minimumBalance = BigDecimal.ZERO;
    
    @DecimalMin("0.0")
    @Column(name = "interest_rate", precision = 5, scale = 2)
    private BigDecimal interestRate = BigDecimal.ZERO;
    
    @Column(name = "interest_credited_date")
    private LocalDate interestCreditedDate;
    
    @Column(name = "last_transaction_date")
    private LocalDateTime lastTransactionDate;
    
    @Column(name = "opening_date")
    private LocalDate openingDate;
    
    @Column(name = "maturity_date")
    private LocalDate maturityDate;
    
    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private AccountStatus status = AccountStatus.PENDING_APPROVAL;
    
    @Column(name = "is_joint_account")
    private Boolean isJointAccount = false;
    
    @Column(name = "joint_holders", length = 500)
    private String jointHolders; // JSON array of joint holder IDs
    
    @Column(name = "nominee_name")
    private String nomineeName;
    
    @Column(name = "nominee_relation")
    private String nomineeRelation;
    
    @Column(name = "nominee_contact")
    private String nomineeContact;
    
    @Column(name = "guardian_name")
    private String guardianName;
    
    @Column(name = "guardian_relation")
    private String guardianRelation;
    
    @Column(name = "guardian_contact")
    private String guardianContact;
    
    @Column(name = "account_purpose")
    private String accountPurpose;
    
    @Column(name = "source_of_funds")
    private String sourceOfFunds;
    
    @Column(name = "expected_monthly_transactions")
    private Integer expectedMonthlyTransactions;
    
    @Column(name = "expected_monthly_amount", precision = 19, scale = 2)
    private BigDecimal expectedMonthlyAmount;
    
    @Column(name = "risk_level", length = 20)
    private String riskLevel; // "LOW", "MEDIUM", "HIGH"
    
    @Column(name = "kyc_status", length = 20)
    private String kycStatus; // "PENDING", "COMPLETED", "REJECTED"
    
    @Column(name = "kyc_completed_date")
    private LocalDate kycCompletedDate;
    
    @Column(name = "account_features", length = 1000)
    private String accountFeatures; // JSON string of account features
    
    @Column(name = "restrictions", length = 1000)
    private String restrictions; // JSON string of account restrictions
    
    @Column(name = "freeze_reason")
    private String freezeReason;
    
    @Column(name = "freeze_date")
    private LocalDateTime freezeDate;
    
    @Column(name = "freeze_by")
    private String freezeBy;
    
    @Column(name = "closure_reason")
    private String closureReason;
    
    @Column(name = "closure_date")
    private LocalDateTime closureDate;
    
    @Column(name = "closure_by")
    private String closureBy;
    
    @Column(name = "dormant_date")
    private LocalDate dormantDate;
    
    @Column(name = "reactivation_date")
    private LocalDate reactivationDate;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    private User user;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "branch_id", nullable = false)
    private Branch branch;
    
    @OneToMany(mappedBy = "fromAccount", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Transaction> outgoingTransactions = new ArrayList<>();

    @OneToMany(mappedBy = "toAccount", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Transaction> incomingTransactions = new ArrayList<>();
    
    @OneToOne(mappedBy = "loanAccount", fetch = FetchType.LAZY)
    private Loan loan;

    @OneToMany(mappedBy = "account", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Card> cards = new ArrayList<>();

    @OneToMany(mappedBy = "account", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Statement> statements = new ArrayList<>();

    @OneToMany(mappedBy = "account", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Notification> notifications = new ArrayList<>();

    // Constructors
    public Account() {}
    
    public Account(String accountNumber, AccountType type, User user, Branch branch) {
        this.accountNumber = accountNumber;
        this.type = type;
        this.user = user;
        this.branch = branch;
        this.openingDate = LocalDate.now();
    }
    
    // Getters and Setters
    public String getAccountNumber() { return accountNumber; }
    public void setAccountNumber(String accountNumber) { this.accountNumber = accountNumber; }

    public String getAccountHolderName() { return accountHolderName; }
    public void setAccountHolderName(String accountHolderName) { this.accountHolderName = accountHolderName; }

    public String getContactNumber() { return contactNumber; }
    public void setContactNumber(String contactNumber) { this.contactNumber = contactNumber; }

    public String getEmailAddress() { return emailAddress; }
    public void setEmailAddress(String emailAddress) { this.emailAddress = emailAddress; }

    public String getAddress() { return address; }
    public void setAddress(String address) { this.address = address; }
    
    public AccountType getType() { return type; }
    public void setType(AccountType type) { this.type = type; }
    
    public BigDecimal getBalance() { return balance; }
    public void setBalance(BigDecimal balance) { this.balance = balance; }

    public String getCurrency() { return currency; }
    public void setCurrency(String currency) { this.currency = currency; }

    public BigDecimal getCreditLimit() { return creditLimit; }
    public void setCreditLimit(BigDecimal creditLimit) { this.creditLimit = creditLimit; }
    
    public BigDecimal getOverdraftLimit() { return overdraftLimit; }
    public void setOverdraftLimit(BigDecimal overdraftLimit) { this.overdraftLimit = overdraftLimit; }
    
    public BigDecimal getAvailableBalance() { return availableBalance; }
    public void setAvailableBalance(BigDecimal availableBalance) { this.availableBalance = availableBalance; }
    
    public BigDecimal getMinimumBalance() { return minimumBalance; }
    public void setMinimumBalance(BigDecimal minimumBalance) { this.minimumBalance = minimumBalance; }
    
    public BigDecimal getInterestRate() { return interestRate; }
    public void setInterestRate(BigDecimal interestRate) { this.interestRate = interestRate; }
    
    public LocalDate getInterestCreditedDate() { return interestCreditedDate; }
    public void setInterestCreditedDate(LocalDate interestCreditedDate) { this.interestCreditedDate = interestCreditedDate; }
    
    public LocalDateTime getLastTransactionDate() { return lastTransactionDate; }
    public void setLastTransactionDate(LocalDateTime lastTransactionDate) { this.lastTransactionDate = lastTransactionDate; }
    
    public LocalDate getOpeningDate() { return openingDate; }
    public void setOpeningDate(LocalDate openingDate) { this.openingDate = openingDate; }
    
    public LocalDate getMaturityDate() { return maturityDate; }
    public void setMaturityDate(LocalDate maturityDate) { this.maturityDate = maturityDate; }
    
    public AccountStatus getStatus() { return status; }
    public void setStatus(AccountStatus status) { this.status = status; }
    
    public Boolean getIsJointAccount() { return isJointAccount; }
    public void setIsJointAccount(Boolean isJointAccount) { this.isJointAccount = isJointAccount; }
    
    public String getJointHolders() { return jointHolders; }
    public void setJointHolders(String jointHolders) { this.jointHolders = jointHolders; }
    
    public String getNomineeName() { return nomineeName; }
    public void setNomineeName(String nomineeName) { this.nomineeName = nomineeName; }
    
    public String getNomineeRelation() { return nomineeRelation; }
    public void setNomineeRelation(String nomineeRelation) { this.nomineeRelation = nomineeRelation; }
    
    public String getNomineeContact() { return nomineeContact; }
    public void setNomineeContact(String nomineeContact) { this.nomineeContact = nomineeContact; }
    
    public String getGuardianName() { return guardianName; }
    public void setGuardianName(String guardianName) { this.guardianName = guardianName; }
    
    public String getGuardianRelation() { return guardianRelation; }
    public void setGuardianRelation(String guardianRelation) { this.guardianRelation = guardianRelation; }
    
    public String getGuardianContact() { return guardianContact; }
    public void setGuardianContact(String guardianContact) { this.guardianContact = guardianContact; }
    
    public String getAccountPurpose() { return accountPurpose; }
    public void setAccountPurpose(String accountPurpose) { this.accountPurpose = accountPurpose; }
    
    public String getSourceOfFunds() { return sourceOfFunds; }
    public void setSourceOfFunds(String sourceOfFunds) { this.sourceOfFunds = sourceOfFunds; }
    
    public Integer getExpectedMonthlyTransactions() { return expectedMonthlyTransactions; }
    public void setExpectedMonthlyTransactions(Integer expectedMonthlyTransactions) { this.expectedMonthlyTransactions = expectedMonthlyTransactions; }
    
    public BigDecimal getExpectedMonthlyAmount() { return expectedMonthlyAmount; }
    public void setExpectedMonthlyAmount(BigDecimal expectedMonthlyAmount) { this.expectedMonthlyAmount = expectedMonthlyAmount; }
    
    public String getRiskLevel() { return riskLevel; }
    public void setRiskLevel(String riskLevel) { this.riskLevel = riskLevel; }
    
    public String getKycStatus() { return kycStatus; }
    public void setKycStatus(String kycStatus) { this.kycStatus = kycStatus; }
    
    public LocalDate getKycCompletedDate() { return kycCompletedDate; }
    public void setKycCompletedDate(LocalDate kycCompletedDate) { this.kycCompletedDate = kycCompletedDate; }
    
    public String getAccountFeatures() { return accountFeatures; }
    public void setAccountFeatures(String accountFeatures) { this.accountFeatures = accountFeatures; }
    
    public String getRestrictions() { return restrictions; }
    public void setRestrictions(String restrictions) { this.restrictions = restrictions; }
    
    public String getFreezeReason() { return freezeReason; }
    public void setFreezeReason(String freezeReason) { this.freezeReason = freezeReason; }
    
    public LocalDateTime getFreezeDate() { return freezeDate; }
    public void setFreezeDate(LocalDateTime freezeDate) { this.freezeDate = freezeDate; }
    
    public String getFreezeBy() { return freezeBy; }
    public void setFreezeBy(String freezeBy) { this.freezeBy = freezeBy; }
    
    public String getClosureReason() { return closureReason; }
    public void setClosureReason(String closureReason) { this.closureReason = closureReason; }
    
    public LocalDateTime getClosureDate() { return closureDate; }
    public void setClosureDate(LocalDateTime closureDate) { this.closureDate = closureDate; }
    
    public String getClosureBy() { return closureBy; }
    public void setClosureBy(String closureBy) { this.closureBy = closureBy; }
    
    public LocalDate getDormantDate() { return dormantDate; }
    public void setDormantDate(LocalDate dormantDate) { this.dormantDate = dormantDate; }
    
    public LocalDate getReactivationDate() { return reactivationDate; }
    public void setReactivationDate(LocalDate reactivationDate) { this.reactivationDate = reactivationDate; }
    
    public User getUser() { return user; }
    public void setUser(User user) { this.user = user; }
    
    public Branch getBranch() { return branch; }
    public void setBranch(Branch branch) { this.branch = branch; }
    
    public List<Transaction> getOutgoingTransactions() { return outgoingTransactions; }
    public void setOutgoingTransactions(List<Transaction> outgoingTransactions) { this.outgoingTransactions = outgoingTransactions; }

    public List<Transaction> getIncomingTransactions() { return incomingTransactions; }
    public void setIncomingTransactions(List<Transaction> incomingTransactions) { this.incomingTransactions = incomingTransactions; }

    // Helper method to get all transactions for this account
    public List<Transaction> getAllTransactions() {
        List<Transaction> allTransactions = new ArrayList<>();
        allTransactions.addAll(outgoingTransactions);
        allTransactions.addAll(incomingTransactions);
        return allTransactions;
    }
    
    public Loan getLoan() { return loan; }
    public void setLoan(Loan loan) { this.loan = loan; }

    public List<Card> getCards() { return cards; }
    public void setCards(List<Card> cards) { this.cards = cards; }

    public List<Statement> getStatements() { return statements; }
    public void setStatements(List<Statement> statements) { this.statements = statements; }

    public List<Notification> getNotifications() { return notifications; }
    public void setNotifications(List<Notification> notifications) { this.notifications = notifications; }

    // Helper methods
    public void credit(BigDecimal amount) {
        this.balance = this.balance.add(amount);
        this.availableBalance = this.availableBalance.add(amount);
        this.lastTransactionDate = LocalDateTime.now();
    }
    
    public void debit(BigDecimal amount) {
        this.balance = this.balance.subtract(amount);
        this.availableBalance = this.availableBalance.subtract(amount);
        this.lastTransactionDate = LocalDateTime.now();
    }
    
    public boolean hasSufficientBalance(BigDecimal amount) {
        return this.availableBalance.compareTo(amount) >= 0;
    }
    
    public boolean isActive() {
        return this.status == AccountStatus.ACTIVE;
    }
    
    public boolean isFrozen() {
        return this.status == AccountStatus.FROZEN;
    }
    
    public boolean isClosed() {
        return this.status == AccountStatus.CLOSED;
    }
    
    public boolean isDormant() {
        return this.status == AccountStatus.DORMANT;
    }
    
    @Override
    public String toString() {
        return "Account{" +
                "id=" + getId() +
                ", accountNumber='" + accountNumber + '\'' +
                ", type=" + type +
                ", balance=" + balance +
                ", status=" + status +
                ", user=" + (user != null ? user.getUsername() : "null") +
                '}';
    }
} 