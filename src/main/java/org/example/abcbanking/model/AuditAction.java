package org.example.abcbanking.model;

public enum AuditAction {
    CREATE,
    READ,
    UPDATE,
    DELETE,
    LOGIN,
    LOG<PERSON>UT,
    LOGIN_FAILED,
    PASSWORD_CHANGE,
    <PERSON>SS<PERSON>ORD_RESET,
    ACCOUNT_LOCKED,
    <PERSON>O<PERSON><PERSON>_UNLOCKED,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    PA<PERSON><PERSON>NT,
    <PERSON><PERSON>_BLOCK,
    <PERSON><PERSON>_UNBLOCK,
    LOAN_APPLICATION,
    LOAN_APPROVAL,
    LOAN_REJECTION,
    KYC_UPDATE,
    PROFILE_UPDATE,
    SETTINGS_CHANGE,
    EXPORT,
    IMPORT,
    <PERSON><PERSON><PERSON>UP,
    RESTORE,
    SYSTEM_ACCESS,
    ADMIN_ACTION,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_CHECK,
    FRAUD_DETECTION,
    RISK_ASSESSMENT
}
