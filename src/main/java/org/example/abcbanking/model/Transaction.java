package org.example.abcbanking.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Entity
@Table(name = "transactions")
public class Transaction extends BaseEntity {
    
    @NotBlank
    @Size(max = 50)
    @Column(name = "transaction_id", unique = true, nullable = false)
    private String transactionId;
    
    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private TransactionType type;
    
    @NotNull
    @DecimalMin("0.01")
    @Column(precision = 19, scale = 2, nullable = false)
    private BigDecimal amount;
    
    @Column(name = "transaction_fee", precision = 10, scale = 2)
    private BigDecimal transactionFee = BigDecimal.ZERO;
    
    @Column(name = "tax_amount", precision = 10, scale = 2)
    private BigDecimal taxAmount = BigDecimal.ZERO;
    
    @Column(name = "total_amount", precision = 19, scale = 2)
    private BigDecimal totalAmount;

    @Size(max = 3)
    @Column(name = "currency", length = 3)
    private String currency = "INR";

    @Size(max = 500)
    private String description;
    
    @Size(max = 100)
    @Column(name = "reference_number")
    private String referenceNumber;
    
    @Size(max = 100)
    @Column(name = "external_reference")
    private String externalReference;
    
    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private TransactionStatus status = TransactionStatus.PENDING;
    
    @Column(name = "transaction_date")
    private LocalDateTime transactionDate;
    
    @Column(name = "value_date")
    private LocalDateTime valueDate;
    
    @Column(name = "processing_date")
    private LocalDateTime processingDate;
    
    @Column(name = "settlement_date")
    private LocalDateTime settlementDate;

    @Column(name = "completed_at")
    private LocalDateTime completedAt;

    @Column(name = "failure_reason", length = 500)
    private String failureReason;

    @Column(name = "reversal_date")
    private LocalDateTime reversalDate;
    
    @Column(name = "reversal_reason")
    private String reversalReason;
    
    @Column(name = "reversed_by")
    private String reversedBy;
    
    @Column(name = "is_reversed")
    private Boolean isReversed = false;
    
    @Column(name = "original_transaction_id")
    private String originalTransactionId;
    
    @Column(name = "channel", length = 50)
    private String channel; // "BRANCH", "ATM", "ONLINE", "MOBILE", "UPI", "NEFT", "RTGS", "IMPS"
    
    @Column(name = "device_id")
    private String deviceId;
    
    @Column(name = "ip_address")
    private String ipAddress;
    
    @Column(name = "user_agent")
    private String userAgent;
    
    @Column(name = "location", length = 200)
    private String location;
    
    @Column(name = "latitude", precision = 10, scale = 8)
    private BigDecimal latitude;
    
    @Column(name = "longitude", precision = 11, scale = 8)
    private BigDecimal longitude;
    
    @Column(name = "risk_score")
    private Integer riskScore = 0;
    
    @Column(name = "is_suspicious")
    private Boolean isSuspicious = false;
    
    @Column(name = "suspicious_reason")
    private String suspiciousReason;
    
    @Column(name = "compliance_status", length = 20)
    private String complianceStatus; // "PASS", "FAIL", "PENDING", "REVIEW"
    
    @Column(name = "aml_check_status", length = 20)
    private String amlCheckStatus; // "PASS", "FAIL", "PENDING", "REVIEW"
    
    @Column(name = "kyc_verification_status", length = 20)
    private String kycVerificationStatus; // "PASS", "FAIL", "PENDING", "REVIEW"
    
    @Column(name = "additional_data", length = 2000)
    private String additionalData; // JSON string for additional transaction data
    
    @Column(name = "remarks")
    private String remarks;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "from_account_id")
    private Account fromAccount;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "to_account_id")
    private Account toAccount;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "performed_by")
    private User performedBy;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "approved_by")
    private User approvedBy;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "card_id")
    private Card card;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "branch_id")
    private Branch branch;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "loan_id")
    private Loan loan;
    
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "emi_id")
    private LoanEMI emi;
    
    // Constructors
    public Transaction() {}
    
    public Transaction(String transactionId, TransactionType type, BigDecimal amount, Account fromAccount) {
        this.transactionId = transactionId;
        this.type = type;
        this.amount = amount;
        this.fromAccount = fromAccount;
        this.transactionDate = LocalDateTime.now();
        this.totalAmount = amount.add(transactionFee).add(taxAmount);
    }
    
    public Transaction(String transactionId, TransactionType type, BigDecimal amount, 
                      Account fromAccount, Account toAccount) {
        this.transactionId = transactionId;
        this.type = type;
        this.amount = amount;
        this.fromAccount = fromAccount;
        this.toAccount = toAccount;
        this.transactionDate = LocalDateTime.now();
        this.totalAmount = amount.add(transactionFee).add(taxAmount);
    }
    
    // Getters and Setters
    public String getTransactionId() { return transactionId; }
    public void setTransactionId(String transactionId) { this.transactionId = transactionId; }
    
    public TransactionType getType() { return type; }
    public void setType(TransactionType type) { this.type = type; }
    
    public BigDecimal getAmount() { return amount; }
    public void setAmount(BigDecimal amount) { this.amount = amount; }
    
    public BigDecimal getTransactionFee() { return transactionFee; }
    public void setTransactionFee(BigDecimal transactionFee) { this.transactionFee = transactionFee; }
    
    public BigDecimal getTaxAmount() { return taxAmount; }
    public void setTaxAmount(BigDecimal taxAmount) { this.taxAmount = taxAmount; }
    
    public BigDecimal getTotalAmount() { return totalAmount; }
    public void setTotalAmount(BigDecimal totalAmount) { this.totalAmount = totalAmount; }

    public String getCurrency() { return currency; }
    public void setCurrency(String currency) { this.currency = currency; }

    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }
    
    public String getReferenceNumber() { return referenceNumber; }
    public void setReferenceNumber(String referenceNumber) { this.referenceNumber = referenceNumber; }
    
    public String getExternalReference() { return externalReference; }
    public void setExternalReference(String externalReference) { this.externalReference = externalReference; }
    
    public TransactionStatus getStatus() { return status; }
    public void setStatus(TransactionStatus status) { this.status = status; }
    
    public LocalDateTime getTransactionDate() { return transactionDate; }
    public void setTransactionDate(LocalDateTime transactionDate) { this.transactionDate = transactionDate; }
    
    public LocalDateTime getValueDate() { return valueDate; }
    public void setValueDate(LocalDateTime valueDate) { this.valueDate = valueDate; }
    
    public LocalDateTime getProcessingDate() { return processingDate; }
    public void setProcessingDate(LocalDateTime processingDate) { this.processingDate = processingDate; }
    
    public LocalDateTime getSettlementDate() { return settlementDate; }
    public void setSettlementDate(LocalDateTime settlementDate) { this.settlementDate = settlementDate; }

    public LocalDateTime getCompletedAt() { return completedAt; }
    public void setCompletedAt(LocalDateTime completedAt) { this.completedAt = completedAt; }

    public String getFailureReason() { return failureReason; }
    public void setFailureReason(String failureReason) { this.failureReason = failureReason; }

    public LocalDateTime getReversalDate() { return reversalDate; }
    public void setReversalDate(LocalDateTime reversalDate) { this.reversalDate = reversalDate; }
    
    public String getReversalReason() { return reversalReason; }
    public void setReversalReason(String reversalReason) { this.reversalReason = reversalReason; }
    
    public String getReversedBy() { return reversedBy; }
    public void setReversedBy(String reversedBy) { this.reversedBy = reversedBy; }
    
    public Boolean getIsReversed() { return isReversed; }
    public void setIsReversed(Boolean isReversed) { this.isReversed = isReversed; }
    
    public String getOriginalTransactionId() { return originalTransactionId; }
    public void setOriginalTransactionId(String originalTransactionId) { this.originalTransactionId = originalTransactionId; }
    
    public String getChannel() { return channel; }
    public void setChannel(String channel) { this.channel = channel; }
    
    public String getDeviceId() { return deviceId; }
    public void setDeviceId(String deviceId) { this.deviceId = deviceId; }
    
    public String getIpAddress() { return ipAddress; }
    public void setIpAddress(String ipAddress) { this.ipAddress = ipAddress; }
    
    public String getUserAgent() { return userAgent; }
    public void setUserAgent(String userAgent) { this.userAgent = userAgent; }
    
    public String getLocation() { return location; }
    public void setLocation(String location) { this.location = location; }
    
    public BigDecimal getLatitude() { return latitude; }
    public void setLatitude(BigDecimal latitude) { this.latitude = latitude; }
    
    public BigDecimal getLongitude() { return longitude; }
    public void setLongitude(BigDecimal longitude) { this.longitude = longitude; }
    
    public Integer getRiskScore() { return riskScore; }
    public void setRiskScore(Integer riskScore) { this.riskScore = riskScore; }
    
    public Boolean getIsSuspicious() { return isSuspicious; }
    public void setIsSuspicious(Boolean isSuspicious) { this.isSuspicious = isSuspicious; }
    
    public String getSuspiciousReason() { return suspiciousReason; }
    public void setSuspiciousReason(String suspiciousReason) { this.suspiciousReason = suspiciousReason; }
    
    public String getComplianceStatus() { return complianceStatus; }
    public void setComplianceStatus(String complianceStatus) { this.complianceStatus = complianceStatus; }
    
    public String getAmlCheckStatus() { return amlCheckStatus; }
    public void setAmlCheckStatus(String amlCheckStatus) { this.amlCheckStatus = amlCheckStatus; }
    
    public String getKycVerificationStatus() { return kycVerificationStatus; }
    public void setKycVerificationStatus(String kycVerificationStatus) { this.kycVerificationStatus = kycVerificationStatus; }
    
    public String getAdditionalData() { return additionalData; }
    public void setAdditionalData(String additionalData) { this.additionalData = additionalData; }
    
    public String getRemarks() { return remarks; }
    public void setRemarks(String remarks) { this.remarks = remarks; }
    
    public Account getFromAccount() { return fromAccount; }
    public void setFromAccount(Account fromAccount) { this.fromAccount = fromAccount; }
    
    public Account getToAccount() { return toAccount; }
    public void setToAccount(Account toAccount) { this.toAccount = toAccount; }
    
    public User getPerformedBy() { return performedBy; }
    public void setPerformedBy(User performedBy) { this.performedBy = performedBy; }
    
    public User getApprovedBy() { return approvedBy; }
    public void setApprovedBy(User approvedBy) { this.approvedBy = approvedBy; }

    public Card getCard() { return card; }
    public void setCard(Card card) { this.card = card; }

    public Branch getBranch() { return branch; }
    public void setBranch(Branch branch) { this.branch = branch; }
    
    public Loan getLoan() { return loan; }
    public void setLoan(Loan loan) { this.loan = loan; }
    
    public LoanEMI getEmi() { return emi; }
    public void setEmi(LoanEMI emi) { this.emi = emi; }
    
    // Helper methods
    public void calculateTotalAmount() {
        this.totalAmount = this.amount.add(this.transactionFee).add(this.taxAmount);
    }
    
    public boolean isCompleted() {
        return this.status == TransactionStatus.COMPLETED;
    }
    
    public boolean isFailed() {
        return this.status == TransactionStatus.FAILED;
    }
    
    public boolean isPending() {
        return this.status == TransactionStatus.PENDING;
    }
    
    public boolean isReversed() {
        return this.isReversed;
    }
    
    public boolean isTransfer() {
        return this.type == TransactionType.TRANSFER || 
               this.type == TransactionType.NEFT_TRANSFER || 
               this.type == TransactionType.RTGS_TRANSFER || 
               this.type == TransactionType.IMPS_TRANSFER ||
               this.type == TransactionType.UPI_TRANSFER;
    }
    
    public boolean isDeposit() {
        return this.type == TransactionType.DEPOSIT || 
               this.type == TransactionType.CASH_DEPOSIT || 
               this.type == TransactionType.CHEQUE_DEPOSIT;
    }
    
    public boolean isWithdrawal() {
        return this.type == TransactionType.WITHDRAWAL || 
               this.type == TransactionType.CASH_WITHDRAWAL;
    }
    
    public void markAsCompleted() {
        this.status = TransactionStatus.COMPLETED;
        this.processingDate = LocalDateTime.now();
    }
    
    public void markAsFailed(String reason) {
        this.status = TransactionStatus.FAILED;
        this.remarks = reason;
    }
    
    public void markAsSuspicious(String reason) {
        this.isSuspicious = true;
        this.suspiciousReason = reason;
    }
    
    @Override
    public String toString() {
        return "Transaction{" +
                "id=" + getId() +
                ", transactionId='" + transactionId + '\'' +
                ", type=" + type +
                ", amount=" + amount +
                ", status=" + status +
                ", fromAccount=" + (fromAccount != null ? fromAccount.getAccountNumber() : "null") +
                ", toAccount=" + (toAccount != null ? toAccount.getAccountNumber() : "null") +
                '}';
    }
} 