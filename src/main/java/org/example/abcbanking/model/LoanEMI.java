package org.example.abcbanking.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Entity
@Table(name = "loan_emis")
public class LoanEMI extends BaseEntity {
    
    @NotNull
    @Positive
    @Column(name = "emi_number", nullable = false)
    private Integer emiNumber;
    
    @NotNull
    @DecimalMin("0.01")
    @Column(name = "emi_amount", precision = 19, scale = 2, nullable = false)
    private BigDecimal emiAmount;
    
    @Column(name = "principal_component", precision = 19, scale = 2)
    private BigDecimal principalComponent = BigDecimal.ZERO;
    
    @Column(name = "interest_component", precision = 19, scale = 2)
    private BigDecimal interestComponent = BigDecimal.ZERO;
    
    @Column(name = "opening_balance", precision = 19, scale = 2)
    private BigDecimal openingBalance = BigDecimal.ZERO;
    
    @Column(name = "closing_balance", precision = 19, scale = 2)
    private BigDecimal closingBalance = BigDecimal.ZERO;
    
    @Column(name = "due_date", nullable = false)
    private LocalDate dueDate;
    
    @Column(name = "paid_date")
    private LocalDate paidDate;
    
    @Column(name = "paid_amount", precision = 19, scale = 2)
    private BigDecimal paidAmount = BigDecimal.ZERO;
    
    @Column(name = "late_fee", precision = 10, scale = 2)
    private BigDecimal lateFee = BigDecimal.ZERO;
    
    @Column(name = "overdue_days")
    private Integer overdueDays = 0;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "emi_status", nullable = false)
    private EMIStatus status = EMIStatus.PENDING;
    
    @Column(name = "payment_method", length = 50)
    private String paymentMethod; // "AUTO_DEBIT", "MANUAL", "CHEQUE", "ONLINE", "UPI"
    
    @Column(name = "transaction_id")
    private String transactionId;
    
    @Column(name = "remarks")
    private String remarks;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "loan_id", nullable = false)
    private Loan loan;
    
    @OneToOne(mappedBy = "emi", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private Transaction paymentTransaction;
    
    // Constructors
    public LoanEMI() {}

    public LoanEMI(Integer emiNumber, BigDecimal emiAmount, LocalDate dueDate, Loan loan) {
        this.emiNumber = emiNumber;
        this.emiAmount = emiAmount;
        this.dueDate = dueDate;
        this.loan = loan;
    }
    
    // Getters and Setters
    public Integer getEmiNumber() { return emiNumber; }
    public void setEmiNumber(Integer emiNumber) { this.emiNumber = emiNumber; }
    
    public BigDecimal getEmiAmount() { return emiAmount; }
    public void setEmiAmount(BigDecimal emiAmount) { this.emiAmount = emiAmount; }
    
    public BigDecimal getPrincipalComponent() { return principalComponent; }
    public void setPrincipalComponent(BigDecimal principalComponent) { this.principalComponent = principalComponent; }
    
    public BigDecimal getInterestComponent() { return interestComponent; }
    public void setInterestComponent(BigDecimal interestComponent) { this.interestComponent = interestComponent; }
    
    public BigDecimal getOpeningBalance() { return openingBalance; }
    public void setOpeningBalance(BigDecimal openingBalance) { this.openingBalance = openingBalance; }
    
    public BigDecimal getClosingBalance() { return closingBalance; }
    public void setClosingBalance(BigDecimal closingBalance) { this.closingBalance = closingBalance; }
    
    public LocalDate getDueDate() { return dueDate; }
    public void setDueDate(LocalDate dueDate) { this.dueDate = dueDate; }
    
    public LocalDate getPaidDate() { return paidDate; }
    public void setPaidDate(LocalDate paidDate) { this.paidDate = paidDate; }
    
    public BigDecimal getPaidAmount() { return paidAmount; }
    public void setPaidAmount(BigDecimal paidAmount) { this.paidAmount = paidAmount; }
    
    public BigDecimal getLateFee() { return lateFee; }
    public void setLateFee(BigDecimal lateFee) { this.lateFee = lateFee; }
    
    public Integer getOverdueDays() { return overdueDays; }
    public void setOverdueDays(Integer overdueDays) { this.overdueDays = overdueDays; }
    
    public EMIStatus getStatus() { return status; }
    public void setStatus(EMIStatus status) { this.status = status; }
    
    public String getPaymentMethod() { return paymentMethod; }
    public void setPaymentMethod(String paymentMethod) { this.paymentMethod = paymentMethod; }
    
    public String getTransactionId() { return transactionId; }
    public void setTransactionId(String transactionId) { this.transactionId = transactionId; }
    
    public String getRemarks() { return remarks; }
    public void setRemarks(String remarks) { this.remarks = remarks; }
    
    public Loan getLoan() { return loan; }
    public void setLoan(Loan loan) { this.loan = loan; }
    
    public Transaction getPaymentTransaction() { return paymentTransaction; }
    public void setPaymentTransaction(Transaction paymentTransaction) { this.paymentTransaction = paymentTransaction; }
    
    // Helper methods
    public boolean isPaid() {
        return this.status == EMIStatus.PAID;
    }

    public boolean isOverdue() {
        return this.overdueDays > 0;
    }

    public boolean isPending() {
        return this.status == EMIStatus.PENDING;
    }

    public void markAsPaid(BigDecimal amount, String paymentMethod, String transactionId) {
        this.status = EMIStatus.PAID;
        this.paidAmount = amount;
        this.paymentMethod = paymentMethod;
        this.transactionId = transactionId;
        this.paidDate = LocalDate.now();
    }

    public void calculateOverdueDays() {
        if (this.dueDate != null && this.status == EMIStatus.PENDING) {
            LocalDate today = LocalDate.now();
            if (today.isAfter(this.dueDate)) {
                this.overdueDays = (int) this.dueDate.until(today).getDays();
            }
        }
    }
    
    public void calculateLateFee(BigDecimal lateFeeRate) {
        if (this.overdueDays > 0) {
            this.lateFee = this.emiAmount.multiply(lateFeeRate)
                    .multiply(BigDecimal.valueOf(this.overdueDays))
                    .divide(BigDecimal.valueOf(365), 2, BigDecimal.ROUND_HALF_UP);
        }
    }
    
    @Override
    public String toString() {
        return "LoanEMI{" +
                "id=" + getId() +
                ", emiNumber=" + emiNumber +
                ", emiAmount=" + emiAmount +
                ", dueDate=" + dueDate +
                ", status=" + status +
                ", loan=" + (loan != null ? loan.getLoanNumber() : "null") +
                '}';
    }
} 