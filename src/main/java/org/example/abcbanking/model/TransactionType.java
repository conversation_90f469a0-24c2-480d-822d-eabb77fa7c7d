package org.example.abcbanking.model;

public enum TransactionType {
    DEPOSIT,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    CREDIT,
    <PERSON><PERSON>T,
    LOAN_DISBURSEMENT,
    LOAN_REPAYMENT,
    INTEREST_CREDIT,
    INTEREST_DEBIT,
    FEE_DEBIT,
    FEE_CREDIT,
    EMI_PAYMENT,
    CARD_PAYMENT,
    ONLINE_PAYMENT,
    MO<PERSON><PERSON>_PAYMENT,
    UPI_TRANSFER,
    NEFT_TRANSFER,
    RTGS_TRANSFER,
    IMPS_TRANSFER,
    CASH_DEPOSIT,
    CASH_WITHDRAWAL,
    CHEQUE_DEPOSIT,
    CHEQUE_PAYMENT,
    STANDING_INSTRUCTION,
    RECURRING_PAYMENT,
    <PERSON><PERSON>UN<PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    R<PERSON><PERSON><PERSON><PERSON>
} 