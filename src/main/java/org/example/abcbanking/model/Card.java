package org.example.abcbanking.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Entity
@Table(name = "cards")
public class Card extends BaseEntity {
    
    @NotBlank
    @Size(max = 20)
    @Column(name = "card_number", unique = true, nullable = false)
    private String cardNumber;
    
    @NotBlank
    @Size(max = 100)
    @Column(name = "card_holder_name", nullable = false)
    private String cardHolderName;
    
    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "card_type", nullable = false)
    private CardType cardType;
    
    @NotNull
    @Column(name = "expiry_date", nullable = false)
    private LocalDate expiryDate;
    
    @NotBlank
    @Size(max = 255)
    @Column(name = "cvv_hash", nullable = false)
    private String cvvHash;
    
    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private CardStatus status = CardStatus.ACTIVE;
    
    @DecimalMin("0.0")
    @Column(name = "daily_limit", precision = 15, scale = 2)
    private BigDecimal dailyLimit = new BigDecimal("1000.00");
    
    @DecimalMin("0.0")
    @Column(name = "monthly_limit", precision = 15, scale = 2)
    private BigDecimal monthlyLimit = new BigDecimal("10000.00");
    
    @DecimalMin("0.0")
    @Column(name = "credit_limit", precision = 15, scale = 2)
    private BigDecimal creditLimit = BigDecimal.ZERO;
    
    @DecimalMin("0.0")
    @Column(name = "available_credit", precision = 15, scale = 2)
    private BigDecimal availableCredit = BigDecimal.ZERO;
    
    @DecimalMin("0.0")
    @Column(name = "outstanding_balance", precision = 15, scale = 2)
    private BigDecimal outstandingBalance = BigDecimal.ZERO;
    
    @Column(name = "is_blocked")
    private Boolean isBlocked = false;
    
    @Column(name = "block_reason")
    private String blockReason;
    
    @Column(name = "blocked_date")
    private LocalDateTime blockedDate;
    
    @Column(name = "blocked_by")
    private String blockedBy;
    
    @Column(name = "pin_hash")
    private String pinHash;
    
    @Column(name = "pin_attempts")
    private Integer pinAttempts = 0;
    
    @Column(name = "pin_locked_until")
    private LocalDateTime pinLockedUntil;
    
    @Column(name = "last_used_date")
    private LocalDateTime lastUsedDate;
    
    @Column(name = "activation_date")
    private LocalDate activationDate;
    
    @Column(name = "replacement_reason")
    private String replacementReason;
    
    @Column(name = "replaced_card_id")
    private Long replacedCardId;
    
    @Column(name = "contactless_enabled")
    private Boolean contactlessEnabled = true;
    
    @Column(name = "online_transactions_enabled")
    private Boolean onlineTransactionsEnabled = true;
    
    @Column(name = "international_transactions_enabled")
    private Boolean internationalTransactionsEnabled = false;
    
    @Column(name = "atm_transactions_enabled")
    private Boolean atmTransactionsEnabled = true;
    
    @Column(name = "pos_transactions_enabled")
    private Boolean posTransactionsEnabled = true;
    
    @Column(name = "card_features", length = 1000)
    private String cardFeatures; // JSON string of card features
    
    @Column(name = "rewards_points")
    private Long rewardsPoints = 0L;
    
    @Column(name = "cashback_earned", precision = 10, scale = 2)
    private BigDecimal cashbackEarned = BigDecimal.ZERO;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "account_id", nullable = false)
    private Account account;
    
    @OneToMany(mappedBy = "card", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Transaction> cardTransactions = new ArrayList<>();
    
    // Constructors
    public Card() {}
    
    public Card(String cardNumber, String cardHolderName, CardType cardType, LocalDate expiryDate, Account account) {
        this.cardNumber = cardNumber;
        this.cardHolderName = cardHolderName;
        this.cardType = cardType;
        this.expiryDate = expiryDate;
        this.account = account;
    }
    
    // Getters and Setters
    public String getCardNumber() { return cardNumber; }
    public void setCardNumber(String cardNumber) { this.cardNumber = cardNumber; }
    
    public String getCardHolderName() { return cardHolderName; }
    public void setCardHolderName(String cardHolderName) { this.cardHolderName = cardHolderName; }
    
    public CardType getCardType() { return cardType; }
    public void setCardType(CardType cardType) { this.cardType = cardType; }
    
    public LocalDate getExpiryDate() { return expiryDate; }
    public void setExpiryDate(LocalDate expiryDate) { this.expiryDate = expiryDate; }
    
    public String getCvvHash() { return cvvHash; }
    public void setCvvHash(String cvvHash) { this.cvvHash = cvvHash; }
    
    public CardStatus getStatus() { return status; }
    public void setStatus(CardStatus status) { this.status = status; }
    
    public BigDecimal getDailyLimit() { return dailyLimit; }
    public void setDailyLimit(BigDecimal dailyLimit) { this.dailyLimit = dailyLimit; }
    
    public BigDecimal getMonthlyLimit() { return monthlyLimit; }
    public void setMonthlyLimit(BigDecimal monthlyLimit) { this.monthlyLimit = monthlyLimit; }
    
    public BigDecimal getCreditLimit() { return creditLimit; }
    public void setCreditLimit(BigDecimal creditLimit) { this.creditLimit = creditLimit; }
    
    public BigDecimal getAvailableCredit() { return availableCredit; }
    public void setAvailableCredit(BigDecimal availableCredit) { this.availableCredit = availableCredit; }
    
    public BigDecimal getOutstandingBalance() { return outstandingBalance; }
    public void setOutstandingBalance(BigDecimal outstandingBalance) { this.outstandingBalance = outstandingBalance; }
    
    public Boolean getIsBlocked() { return isBlocked; }
    public void setIsBlocked(Boolean isBlocked) { this.isBlocked = isBlocked; }
    
    public String getBlockReason() { return blockReason; }
    public void setBlockReason(String blockReason) { this.blockReason = blockReason; }
    
    public LocalDateTime getBlockedDate() { return blockedDate; }
    public void setBlockedDate(LocalDateTime blockedDate) { this.blockedDate = blockedDate; }
    
    public String getBlockedBy() { return blockedBy; }
    public void setBlockedBy(String blockedBy) { this.blockedBy = blockedBy; }
    
    public String getPinHash() { return pinHash; }
    public void setPinHash(String pinHash) { this.pinHash = pinHash; }
    
    public Integer getPinAttempts() { return pinAttempts; }
    public void setPinAttempts(Integer pinAttempts) { this.pinAttempts = pinAttempts; }
    
    public LocalDateTime getPinLockedUntil() { return pinLockedUntil; }
    public void setPinLockedUntil(LocalDateTime pinLockedUntil) { this.pinLockedUntil = pinLockedUntil; }
    
    public LocalDateTime getLastUsedDate() { return lastUsedDate; }
    public void setLastUsedDate(LocalDateTime lastUsedDate) { this.lastUsedDate = lastUsedDate; }
    
    public LocalDate getActivationDate() { return activationDate; }
    public void setActivationDate(LocalDate activationDate) { this.activationDate = activationDate; }
    
    public String getReplacementReason() { return replacementReason; }
    public void setReplacementReason(String replacementReason) { this.replacementReason = replacementReason; }
    
    public Long getReplacedCardId() { return replacedCardId; }
    public void setReplacedCardId(Long replacedCardId) { this.replacedCardId = replacedCardId; }
    
    public Boolean getContactlessEnabled() { return contactlessEnabled; }
    public void setContactlessEnabled(Boolean contactlessEnabled) { this.contactlessEnabled = contactlessEnabled; }
    
    public Boolean getOnlineTransactionsEnabled() { return onlineTransactionsEnabled; }
    public void setOnlineTransactionsEnabled(Boolean onlineTransactionsEnabled) { this.onlineTransactionsEnabled = onlineTransactionsEnabled; }
    
    public Boolean getInternationalTransactionsEnabled() { return internationalTransactionsEnabled; }
    public void setInternationalTransactionsEnabled(Boolean internationalTransactionsEnabled) { this.internationalTransactionsEnabled = internationalTransactionsEnabled; }
    
    public Boolean getAtmTransactionsEnabled() { return atmTransactionsEnabled; }
    public void setAtmTransactionsEnabled(Boolean atmTransactionsEnabled) { this.atmTransactionsEnabled = atmTransactionsEnabled; }
    
    public Boolean getPosTransactionsEnabled() { return posTransactionsEnabled; }
    public void setPosTransactionsEnabled(Boolean posTransactionsEnabled) { this.posTransactionsEnabled = posTransactionsEnabled; }
    
    public String getCardFeatures() { return cardFeatures; }
    public void setCardFeatures(String cardFeatures) { this.cardFeatures = cardFeatures; }
    
    public Long getRewardsPoints() { return rewardsPoints; }
    public void setRewardsPoints(Long rewardsPoints) { this.rewardsPoints = rewardsPoints; }
    
    public BigDecimal getCashbackEarned() { return cashbackEarned; }
    public void setCashbackEarned(BigDecimal cashbackEarned) { this.cashbackEarned = cashbackEarned; }
    
    public Account getAccount() { return account; }
    public void setAccount(Account account) { this.account = account; }
    
    public List<Transaction> getCardTransactions() { return cardTransactions; }
    public void setCardTransactions(List<Transaction> cardTransactions) { this.cardTransactions = cardTransactions; }
    
    // Helper methods
    public boolean isActive() {
        return this.status == CardStatus.ACTIVE && !this.isBlocked;
    }
    
    public boolean isExpired() {
        return this.expiryDate.isBefore(LocalDate.now());
    }
    
    public boolean isPinLocked() {
        return this.pinLockedUntil != null && LocalDateTime.now().isBefore(this.pinLockedUntil);
    }
    
    public void blockCard(String reason, String blockedBy) {
        this.isBlocked = true;
        this.blockReason = reason;
        this.blockedDate = LocalDateTime.now();
        this.blockedBy = blockedBy;
        this.status = CardStatus.BLOCKED;
    }
    
    public void unblockCard() {
        this.isBlocked = false;
        this.blockReason = null;
        this.blockedDate = null;
        this.blockedBy = null;
        this.status = CardStatus.ACTIVE;
    }
    
    public String getMaskedCardNumber() {
        if (cardNumber == null || cardNumber.length() < 4) {
            return cardNumber;
        }
        return cardNumber.substring(0, 4) + " **** **** " + cardNumber.substring(cardNumber.length() - 4);
    }
}
