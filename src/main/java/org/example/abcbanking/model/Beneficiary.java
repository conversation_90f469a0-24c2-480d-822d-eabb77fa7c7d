package org.example.abcbanking.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.*;

import java.time.LocalDateTime;

@Entity
@Table(name = "beneficiaries")
public class Beneficiary extends BaseEntity {
    
    @NotBlank
    @Size(max = 100)
    @Column(name = "beneficiary_name", nullable = false)
    private String beneficiaryName;
    
    @NotBlank
    @Size(max = 20)
    @Column(name = "account_number", nullable = false)
    private String accountNumber;
    
    @Size(max = 11)
    @Column(name = "ifsc_code")
    private String ifscCode;
    
    @Size(max = 100)
    @Column(name = "bank_name")
    private String bankName;
    
    @Size(max = 100)
    @Column(name = "branch_name")
    private String branchName;
    
    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private BeneficiaryType type;
    
    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private BeneficiaryStatus status = BeneficiaryStatus.ACTIVE;
    
    @Size(max = 50)
    @Column(name = "nickname")
    private String nickname;
    
    @Size(max = 15)
    @Column(name = "mobile_number")
    private String mobileNumber;
    
    @Email
    @Size(max = 100)
    @Column(name = "email")
    private String email;
    
    @Size(max = 200)
    @Column(name = "address")
    private String address;
    
    @Column(name = "is_verified")
    private Boolean isVerified = false;
    
    @Column(name = "verification_date")
    private LocalDateTime verificationDate;
    
    @Column(name = "verification_method")
    private String verificationMethod; // PENNY_DROP, MANUAL, AUTO
    
    @Column(name = "last_used_date")
    private LocalDateTime lastUsedDate;
    
    @Column(name = "usage_count")
    private Long usageCount = 0L;
    
    @Column(name = "is_favorite")
    private Boolean isFavorite = false;
    
    @Column(name = "transfer_limit", precision = 15, scale = 2)
    private java.math.BigDecimal transferLimit;
    
    @Column(name = "daily_limit", precision = 15, scale = 2)
    private java.math.BigDecimal dailyLimit;
    
    @Column(name = "monthly_limit", precision = 15, scale = 2)
    private java.math.BigDecimal monthlyLimit;
    
    @Column(name = "relationship")
    private String relationship; // FAMILY, FRIEND, BUSINESS, VENDOR, etc.
    
    @Column(name = "purpose")
    private String purpose; // SALARY, RENT, INVESTMENT, PERSONAL, etc.
    
    @Column(name = "notes")
    private String notes;
    
    @Column(name = "metadata", length = 1000)
    private String metadata; // JSON string for additional data
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    private User user;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "added_by")
    private User addedBy;
    
    // Constructors
    public Beneficiary() {}
    
    public Beneficiary(String beneficiaryName, String accountNumber, BeneficiaryType type, User user) {
        this.beneficiaryName = beneficiaryName;
        this.accountNumber = accountNumber;
        this.type = type;
        this.user = user;
    }
    
    // Getters and Setters
    public String getBeneficiaryName() { return beneficiaryName; }
    public void setBeneficiaryName(String beneficiaryName) { this.beneficiaryName = beneficiaryName; }
    
    public String getAccountNumber() { return accountNumber; }
    public void setAccountNumber(String accountNumber) { this.accountNumber = accountNumber; }
    
    public String getIfscCode() { return ifscCode; }
    public void setIfscCode(String ifscCode) { this.ifscCode = ifscCode; }
    
    public String getBankName() { return bankName; }
    public void setBankName(String bankName) { this.bankName = bankName; }
    
    public String getBranchName() { return branchName; }
    public void setBranchName(String branchName) { this.branchName = branchName; }
    
    public BeneficiaryType getType() { return type; }
    public void setType(BeneficiaryType type) { this.type = type; }
    
    public BeneficiaryStatus getStatus() { return status; }
    public void setStatus(BeneficiaryStatus status) { this.status = status; }
    
    public String getNickname() { return nickname; }
    public void setNickname(String nickname) { this.nickname = nickname; }
    
    public String getMobileNumber() { return mobileNumber; }
    public void setMobileNumber(String mobileNumber) { this.mobileNumber = mobileNumber; }
    
    public String getEmail() { return email; }
    public void setEmail(String email) { this.email = email; }
    
    public String getAddress() { return address; }
    public void setAddress(String address) { this.address = address; }
    
    public Boolean getIsVerified() { return isVerified; }
    public void setIsVerified(Boolean isVerified) { this.isVerified = isVerified; }
    
    public LocalDateTime getVerificationDate() { return verificationDate; }
    public void setVerificationDate(LocalDateTime verificationDate) { this.verificationDate = verificationDate; }
    
    public String getVerificationMethod() { return verificationMethod; }
    public void setVerificationMethod(String verificationMethod) { this.verificationMethod = verificationMethod; }
    
    public LocalDateTime getLastUsedDate() { return lastUsedDate; }
    public void setLastUsedDate(LocalDateTime lastUsedDate) { this.lastUsedDate = lastUsedDate; }
    
    public Long getUsageCount() { return usageCount; }
    public void setUsageCount(Long usageCount) { this.usageCount = usageCount; }
    
    public Boolean getIsFavorite() { return isFavorite; }
    public void setIsFavorite(Boolean isFavorite) { this.isFavorite = isFavorite; }
    
    public java.math.BigDecimal getTransferLimit() { return transferLimit; }
    public void setTransferLimit(java.math.BigDecimal transferLimit) { this.transferLimit = transferLimit; }
    
    public java.math.BigDecimal getDailyLimit() { return dailyLimit; }
    public void setDailyLimit(java.math.BigDecimal dailyLimit) { this.dailyLimit = dailyLimit; }
    
    public java.math.BigDecimal getMonthlyLimit() { return monthlyLimit; }
    public void setMonthlyLimit(java.math.BigDecimal monthlyLimit) { this.monthlyLimit = monthlyLimit; }
    
    public String getRelationship() { return relationship; }
    public void setRelationship(String relationship) { this.relationship = relationship; }
    
    public String getPurpose() { return purpose; }
    public void setPurpose(String purpose) { this.purpose = purpose; }
    
    public String getNotes() { return notes; }
    public void setNotes(String notes) { this.notes = notes; }
    
    public String getMetadata() { return metadata; }
    public void setMetadata(String metadata) { this.metadata = metadata; }
    
    public User getUser() { return user; }
    public void setUser(User user) { this.user = user; }
    
    public User getAddedBy() { return addedBy; }
    public void setAddedBy(User addedBy) { this.addedBy = addedBy; }
    
    // Helper methods
    public void markAsUsed() {
        this.lastUsedDate = LocalDateTime.now();
        this.usageCount++;
    }
    
    public void verify(String method) {
        this.isVerified = true;
        this.verificationDate = LocalDateTime.now();
        this.verificationMethod = method;
        this.status = BeneficiaryStatus.VERIFIED;
    }
    
    public String getMaskedAccountNumber() {
        if (accountNumber == null || accountNumber.length() < 4) {
            return accountNumber;
        }
        return "****" + accountNumber.substring(accountNumber.length() - 4);
    }
    
    public String getDisplayName() {
        return nickname != null && !nickname.trim().isEmpty() ? nickname : beneficiaryName;
    }
}
