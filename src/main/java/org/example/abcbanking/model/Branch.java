package org.example.abcbanking.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;

import java.math.BigDecimal;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;

@Entity
@Table(name = "branches")
public class Branch extends BaseEntity {
    
    @NotBlank
    @Size(max = 20)
    @Column(name = "branch_code", unique = true, nullable = false)
    private String branchCode;
    
    @NotBlank
    @Size(max = 100)
    @Column(nullable = false)
    private String name;
    
    @NotBlank
    @Size(max = 200)
    @Column(nullable = false)
    private String address;
    
    @Size(max = 100)
    private String city;
    
    @Size(max = 100)
    private String state;
    
    @Size(max = 20)
    @Pattern(regexp = "^[0-9]{6}$", message = "PIN code must be 6 digits")
    private String pinCode;
    
    @Size(max = 100)
    private String country = "India";
    
    @Size(max = 20)
    @Pattern(regexp = "^[0-9]{10,15}$", message = "Phone number must be 10-15 digits")
    private String phoneNumber;
    
    @Email
    @Size(max = 100)
    private String email;
    
    @Size(max = 20)
    private String fax;
    
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private BranchStatus status = BranchStatus.ACTIVE;
    
    @Column(name = "opening_time")
    private LocalTime openingTime;
    
    @Column(name = "closing_time")
    private LocalTime closingTime;
    
    @Column(name = "working_days", length = 50)
    private String workingDays; // "MON,TUE,WED,THU,FRI,SAT"
    
    @Column(name = "is_atm_available")
    private Boolean isAtmAvailable = false;
    
    @Column(name = "atm_count")
    private Integer atmCount = 0;
    
    @Column(name = "is_24x7")
    private Boolean is24x7 = false;
    
    @Column(name = "branch_manager_id")
    private Long branchManagerId;
    
    @Column(name = "total_accounts")
    private Long totalAccounts = 0L;
    
    @Column(name = "total_customers")
    private Long totalCustomers = 0L;
    
    @Column(name = "total_loans")
    private Long totalLoans = 0L;
    
    @Column(name = "total_deposits", precision = 19, scale = 2)
    private BigDecimal totalDeposits = BigDecimal.ZERO;
    
    @Column(name = "total_advances", precision = 19, scale = 2)
    private BigDecimal totalAdvances = BigDecimal.ZERO;
    
    @Column(name = "branch_rating", precision = 3, scale = 2)
    private BigDecimal branchRating = BigDecimal.ZERO;
    
    @Column(name = "latitude", precision = 10, scale = 8)
    private BigDecimal latitude;
    
    @Column(name = "longitude", precision = 11, scale = 8)
    private BigDecimal longitude;
    
    @Column(name = "ifsc_code", length = 11)
    private String ifscCode;
    
    @Column(name = "micr_code", length = 9)
    private String micrCode;
    
    @Column(name = "branch_type", length = 50)
    private String branchType; // "MAIN", "SUB", "EXTENSION", "SPECIALIZED"
    
    @Column(name = "specializations", length = 500)
    private String specializations; // JSON string of specializations
    
    @OneToMany(mappedBy = "branch", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<User> users = new ArrayList<>();
    
    @OneToMany(mappedBy = "branch", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Account> accounts = new ArrayList<>();
    
    @OneToMany(mappedBy = "branch", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Loan> loans = new ArrayList<>();
    
    // Constructors
    public Branch() {}
    
    public Branch(String branchCode, String name, String address) {
        this.branchCode = branchCode;
        this.name = name;
        this.address = address;
    }
    
    // Getters and Setters
    public String getBranchCode() { return branchCode; }
    public void setBranchCode(String branchCode) { this.branchCode = branchCode; }
    
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }
    
    public String getAddress() { return address; }
    public void setAddress(String address) { this.address = address; }
    
    public String getCity() { return city; }
    public void setCity(String city) { this.city = city; }
    
    public String getState() { return state; }
    public void setState(String state) { this.state = state; }
    
    public String getPinCode() { return pinCode; }
    public void setPinCode(String pinCode) { this.pinCode = pinCode; }
    
    public String getCountry() { return country; }
    public void setCountry(String country) { this.country = country; }
    
    public String getPhoneNumber() { return phoneNumber; }
    public void setPhoneNumber(String phoneNumber) { this.phoneNumber = phoneNumber; }
    
    public String getEmail() { return email; }
    public void setEmail(String email) { this.email = email; }
    
    public String getFax() { return fax; }
    public void setFax(String fax) { this.fax = fax; }
    
    public BranchStatus getStatus() { return status; }
    public void setStatus(BranchStatus status) { this.status = status; }
    
    public LocalTime getOpeningTime() { return openingTime; }
    public void setOpeningTime(LocalTime openingTime) { this.openingTime = openingTime; }
    
    public LocalTime getClosingTime() { return closingTime; }
    public void setClosingTime(LocalTime closingTime) { this.closingTime = closingTime; }
    
    public String getWorkingDays() { return workingDays; }
    public void setWorkingDays(String workingDays) { this.workingDays = workingDays; }
    
    public Boolean getIsAtmAvailable() { return isAtmAvailable; }
    public void setIsAtmAvailable(Boolean isAtmAvailable) { this.isAtmAvailable = isAtmAvailable; }
    
    public Integer getAtmCount() { return atmCount; }
    public void setAtmCount(Integer atmCount) { this.atmCount = atmCount; }
    
    public Boolean getIs24x7() { return is24x7; }
    public void setIs24x7(Boolean is24x7) { this.is24x7 = is24x7; }
    
    public Long getBranchManagerId() { return branchManagerId; }
    public void setBranchManagerId(Long branchManagerId) { this.branchManagerId = branchManagerId; }
    
    public Long getTotalAccounts() { return totalAccounts; }
    public void setTotalAccounts(Long totalAccounts) { this.totalAccounts = totalAccounts; }
    
    public Long getTotalCustomers() { return totalCustomers; }
    public void setTotalCustomers(Long totalCustomers) { this.totalCustomers = totalCustomers; }
    
    public Long getTotalLoans() { return totalLoans; }
    public void setTotalLoans(Long totalLoans) { this.totalLoans = totalLoans; }
    
    public BigDecimal getTotalDeposits() { return totalDeposits; }
    public void setTotalDeposits(BigDecimal totalDeposits) { this.totalDeposits = totalDeposits; }
    
    public BigDecimal getTotalAdvances() { return totalAdvances; }
    public void setTotalAdvances(BigDecimal totalAdvances) { this.totalAdvances = totalAdvances; }
    
    public BigDecimal getBranchRating() { return branchRating; }
    public void setBranchRating(BigDecimal branchRating) { this.branchRating = branchRating; }
    
    public BigDecimal getLatitude() { return latitude; }
    public void setLatitude(BigDecimal latitude) { this.latitude = latitude; }
    
    public BigDecimal getLongitude() { return longitude; }
    public void setLongitude(BigDecimal longitude) { this.longitude = longitude; }
    
    public String getIfscCode() { return ifscCode; }
    public void setIfscCode(String ifscCode) { this.ifscCode = ifscCode; }
    
    public String getMicrCode() { return micrCode; }
    public void setMicrCode(String micrCode) { this.micrCode = micrCode; }
    
    public String getBranchType() { return branchType; }
    public void setBranchType(String branchType) { this.branchType = branchType; }
    
    public String getSpecializations() { return specializations; }
    public void setSpecializations(String specializations) { this.specializations = specializations; }
    
    public List<User> getUsers() { return users; }
    public void setUsers(List<User> users) { this.users = users; }
    
    public List<Account> getAccounts() { return accounts; }
    public void setAccounts(List<Account> accounts) { this.accounts = accounts; }
    
    public List<Loan> getLoans() { return loans; }
    public void setLoans(List<Loan> loans) { this.loans = loans; }
    
    @Override
    public String toString() {
        return "Branch{" +
                "id=" + getId() +
                ", branchCode='" + branchCode + '\'' +
                ", name='" + name + '\'' +
                ", address='" + address + '\'' +
                ", city='" + city + '\'' +
                ", status=" + status +
                '}';
    }
} 