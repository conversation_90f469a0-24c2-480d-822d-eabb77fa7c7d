package org.example.abcbanking.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;

@Entity
@Table(name = "roles")
public class Role extends BaseEntity {
    
    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(length = 50, unique = true, nullable = false)
    private ERole name;
    
    @Column(length = 200)
    private String description;
    
    @Column(name = "is_active")
    private Boolean isActive = true;
    
    @Column(name = "permissions", length = 1000)
    private String permissions; // JSON string of permissions
    
    // Constructors
    public Role() {}
    
    public Role(ERole name) {
        this.name = name;
    }
    
    public Role(ERole name, String description) {
        this.name = name;
        this.description = description;
    }
    
    // Getters and Setters
    public ERole getName() {
        return name;
    }
    
    public void setName(ERole name) {
        this.name = name;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public Boolean getIsActive() {
        return isActive;
    }
    
    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }
    
    public String getPermissions() {
        return permissions;
    }
    
    public void setPermissions(String permissions) {
        this.permissions = permissions;
    }
    
    @Override
    public String toString() {
        return "Role{" +
                "id=" + getId() +
                ", name=" + name +
                ", description='" + description + '\'' +
                ", isActive=" + isActive +
                '}';
    }
} 