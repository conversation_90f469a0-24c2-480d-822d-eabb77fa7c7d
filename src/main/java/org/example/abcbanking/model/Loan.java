package org.example.abcbanking.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import jakarta.validation.constraints.Size;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Entity
@Table(name = "loans")
public class Loan extends BaseEntity {
    
    @NotBlank
    @Size(max = 20)
    @Column(name = "loan_number", unique = true, nullable = false)
    private String loanNumber;
    
    @NotNull
    @DecimalMin("0.01")
    @Column(name = "principal_amount", precision = 19, scale = 2, nullable = false)
    private BigDecimal principalAmount;
    
    @NotNull
    @DecimalMin("0.0")
    @Column(name = "interest_rate", precision = 5, scale = 2, nullable = false)
    private BigDecimal interestRate;
    
    @NotNull
    @Positive
    @Column(name = "tenure_months", nullable = false)
    private Integer tenureMonths;
    
    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private LoanType type;
    
    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private LoanStatus status = LoanStatus.DRAFT;
    
    @Column(name = "disbursed_amount", precision = 19, scale = 2)
    private BigDecimal disbursedAmount = BigDecimal.ZERO;
    
    @Column(name = "outstanding_amount", precision = 19, scale = 2)
    private BigDecimal outstandingAmount = BigDecimal.ZERO;
    
    @Column(name = "total_interest_payable", precision = 19, scale = 2)
    private BigDecimal totalInterestPayable = BigDecimal.ZERO;
    
    @Column(name = "total_amount_payable", precision = 19, scale = 2)
    private BigDecimal totalAmountPayable = BigDecimal.ZERO;
    
    @Column(name = "emi_amount", precision = 19, scale = 2)
    private BigDecimal emiAmount = BigDecimal.ZERO;
    
    @Column(name = "processing_fee", precision = 10, scale = 2)
    private BigDecimal processingFee = BigDecimal.ZERO;
    
    @Column(name = "insurance_premium", precision = 10, scale = 2)
    private BigDecimal insurancePremium = BigDecimal.ZERO;
    
    @Column(name = "legal_charges", precision = 10, scale = 2)
    private BigDecimal legalCharges = BigDecimal.ZERO;
    
    @Column(name = "valuation_charges", precision = 10, scale = 2)
    private BigDecimal valuationCharges = BigDecimal.ZERO;
    
    @Column(name = "total_charges", precision = 10, scale = 2)
    private BigDecimal totalCharges = BigDecimal.ZERO;
    
    @Column(name = "application_date")
    private LocalDate applicationDate;
    
    @Column(name = "approval_date")
    private LocalDate approvalDate;
    
    @Column(name = "disbursement_date")
    private LocalDate disbursementDate;
    
    @Column(name = "first_emi_date")
    private LocalDate firstEmiDate;
    
    @Column(name = "last_emi_date")
    private LocalDate lastEmiDate;
    
    @Column(name = "maturity_date")
    private LocalDate maturityDate;
    
    @Column(name = "closure_date")
    private LocalDate closureDate;
    
    @Column(name = "prepayment_date")
    private LocalDate prepaymentDate;
    
    @Column(name = "prepayment_amount", precision = 19, scale = 2)
    private BigDecimal prepaymentAmount = BigDecimal.ZERO;
    
    @Column(name = "prepayment_charges", precision = 10, scale = 2)
    private BigDecimal prepaymentCharges = BigDecimal.ZERO;
    
    @Column(name = "overdue_amount", precision = 19, scale = 2)
    private BigDecimal overdueAmount = BigDecimal.ZERO;
    
    @Column(name = "overdue_days")
    private Integer overdueDays = 0;
    
    @Column(name = "late_fee_charges", precision = 10, scale = 2)
    private BigDecimal lateFeeCharges = BigDecimal.ZERO;
    
    @Column(name = "collateral_value", precision = 19, scale = 2)
    private BigDecimal collateralValue = BigDecimal.ZERO;
    
    @Column(name = "collateral_type", length = 100)
    private String collateralType;
    
    @Column(name = "collateral_details", length = 500)
    private String collateralDetails;
    
    @Column(name = "purpose", length = 200)
    private String purpose;
    
    @Column(name = "employment_type", length = 50)
    private String employmentType; // "SALARIED", "SELF_EMPLOYED", "BUSINESS", "RETIRED"
    
    @Column(name = "monthly_income", precision = 12, scale = 2)
    private BigDecimal monthlyIncome;
    
    @Column(name = "monthly_expenses", precision = 12, scale = 2)
    private BigDecimal monthlyExpenses;
    
    @Column(name = "debt_to_income_ratio", precision = 5, scale = 2)
    private BigDecimal debtToIncomeRatio;
    
    @Column(name = "credit_score")
    private Integer creditScore;
    
    @Column(name = "risk_rating", length = 20)
    private String riskRating; // "LOW", "MEDIUM", "HIGH", "VERY_HIGH"
    
    @Column(name = "loan_officer_id")
    private Long loanOfficerId;
    
    @Column(name = "branch_manager_id")
    private Long branchManagerId;
    
    @Column(name = "approval_authority", length = 100)
    private String approvalAuthority;
    
    @Column(name = "approval_level", length = 50)
    private String approvalLevel; // "BRANCH", "REGIONAL", "HEAD_OFFICE"
    
    @Column(name = "documents_required", length = 1000)
    private String documentsRequired; // JSON array of required documents
    
    @Column(name = "documents_submitted", length = 1000)
    private String documentsSubmitted; // JSON array of submitted documents
    
    @Column(name = "kyc_status", length = 20)
    private String kycStatus; // "PENDING", "COMPLETED", "REJECTED"
    
    @Column(name = "kyc_completed_date")
    private LocalDate kycCompletedDate;
    
    @Column(name = "insurance_policy_number")
    private String insurancePolicyNumber;
    
    @Column(name = "insurance_company")
    private String insuranceCompany;
    
    @Column(name = "insurance_expiry_date")
    private LocalDate insuranceExpiryDate;
    
    @Column(name = "guarantor_name")
    private String guarantorName;
    
    @Column(name = "guarantor_contact")
    private String guarantorContact;
    
    @Column(name = "guarantor_income", precision = 12, scale = 2)
    private BigDecimal guarantorIncome;
    
    @Column(name = "co_applicant_name")
    private String coApplicantName;
    
    @Column(name = "co_applicant_contact")
    private String coApplicantContact;
    
    @Column(name = "co_applicant_income", precision = 12, scale = 2)
    private BigDecimal coApplicantIncome;
    
    @Column(name = "remarks")
    private String remarks;
    
    @Column(name = "additional_data", length = 2000)
    private String additionalData; // JSON string for additional loan data
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    private User user;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "branch_id", nullable = false)
    private Branch branch;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "approved_by")
    private User approvedBy;
    
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "loan_account_id")
    private Account loanAccount;
    
    @OneToMany(mappedBy = "loan", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<LoanEMI> emiSchedule = new ArrayList<>();
    
    @OneToMany(mappedBy = "loan", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Transaction> loanTransactions = new ArrayList<>();
    
    // Constructors
    public Loan() {}
    
    public Loan(String loanNumber, BigDecimal principalAmount, BigDecimal interestRate, 
                Integer tenureMonths, LoanType type, User user, Branch branch) {
        this.loanNumber = loanNumber;
        this.principalAmount = principalAmount;
        this.interestRate = interestRate;
        this.tenureMonths = tenureMonths;
        this.type = type;
        this.user = user;
        this.branch = branch;
        this.applicationDate = LocalDate.now();
    }
    
    // Getters and Setters
    public String getLoanNumber() { return loanNumber; }
    public void setLoanNumber(String loanNumber) { this.loanNumber = loanNumber; }
    
    public BigDecimal getPrincipalAmount() { return principalAmount; }
    public void setPrincipalAmount(BigDecimal principalAmount) { this.principalAmount = principalAmount; }
    
    public BigDecimal getInterestRate() { return interestRate; }
    public void setInterestRate(BigDecimal interestRate) { this.interestRate = interestRate; }
    
    public Integer getTenureMonths() { return tenureMonths; }
    public void setTenureMonths(Integer tenureMonths) { this.tenureMonths = tenureMonths; }
    
    public LoanType getType() { return type; }
    public void setType(LoanType type) { this.type = type; }
    
    public LoanStatus getStatus() { return status; }
    public void setStatus(LoanStatus status) { this.status = status; }
    
    public BigDecimal getDisbursedAmount() { return disbursedAmount; }
    public void setDisbursedAmount(BigDecimal disbursedAmount) { this.disbursedAmount = disbursedAmount; }
    
    public BigDecimal getOutstandingAmount() { return outstandingAmount; }
    public void setOutstandingAmount(BigDecimal outstandingAmount) { this.outstandingAmount = outstandingAmount; }
    
    public BigDecimal getTotalInterestPayable() { return totalInterestPayable; }
    public void setTotalInterestPayable(BigDecimal totalInterestPayable) { this.totalInterestPayable = totalInterestPayable; }
    
    public BigDecimal getTotalAmountPayable() { return totalAmountPayable; }
    public void setTotalAmountPayable(BigDecimal totalAmountPayable) { this.totalAmountPayable = totalAmountPayable; }
    
    public BigDecimal getEmiAmount() { return emiAmount; }
    public void setEmiAmount(BigDecimal emiAmount) { this.emiAmount = emiAmount; }
    
    public BigDecimal getProcessingFee() { return processingFee; }
    public void setProcessingFee(BigDecimal processingFee) { this.processingFee = processingFee; }
    
    public BigDecimal getInsurancePremium() { return insurancePremium; }
    public void setInsurancePremium(BigDecimal insurancePremium) { this.insurancePremium = insurancePremium; }
    
    public BigDecimal getLegalCharges() { return legalCharges; }
    public void setLegalCharges(BigDecimal legalCharges) { this.legalCharges = legalCharges; }
    
    public BigDecimal getValuationCharges() { return valuationCharges; }
    public void setValuationCharges(BigDecimal valuationCharges) { this.valuationCharges = valuationCharges; }
    
    public BigDecimal getTotalCharges() { return totalCharges; }
    public void setTotalCharges(BigDecimal totalCharges) { this.totalCharges = totalCharges; }
    
    public LocalDate getApplicationDate() { return applicationDate; }
    public void setApplicationDate(LocalDate applicationDate) { this.applicationDate = applicationDate; }
    
    public LocalDate getApprovalDate() { return approvalDate; }
    public void setApprovalDate(LocalDate approvalDate) { this.approvalDate = approvalDate; }
    
    public LocalDate getDisbursementDate() { return disbursementDate; }
    public void setDisbursementDate(LocalDate disbursementDate) { this.disbursementDate = disbursementDate; }
    
    public LocalDate getFirstEmiDate() { return firstEmiDate; }
    public void setFirstEmiDate(LocalDate firstEmiDate) { this.firstEmiDate = firstEmiDate; }
    
    public LocalDate getLastEmiDate() { return lastEmiDate; }
    public void setLastEmiDate(LocalDate lastEmiDate) { this.lastEmiDate = lastEmiDate; }
    
    public LocalDate getMaturityDate() { return maturityDate; }
    public void setMaturityDate(LocalDate maturityDate) { this.maturityDate = maturityDate; }
    
    public LocalDate getClosureDate() { return closureDate; }
    public void setClosureDate(LocalDate closureDate) { this.closureDate = closureDate; }
    
    public LocalDate getPrepaymentDate() { return prepaymentDate; }
    public void setPrepaymentDate(LocalDate prepaymentDate) { this.prepaymentDate = prepaymentDate; }
    
    public BigDecimal getPrepaymentAmount() { return prepaymentAmount; }
    public void setPrepaymentAmount(BigDecimal prepaymentAmount) { this.prepaymentAmount = prepaymentAmount; }
    
    public BigDecimal getPrepaymentCharges() { return prepaymentCharges; }
    public void setPrepaymentCharges(BigDecimal prepaymentCharges) { this.prepaymentCharges = prepaymentCharges; }
    
    public BigDecimal getOverdueAmount() { return overdueAmount; }
    public void setOverdueAmount(BigDecimal overdueAmount) { this.overdueAmount = overdueAmount; }
    
    public Integer getOverdueDays() { return overdueDays; }
    public void setOverdueDays(Integer overdueDays) { this.overdueDays = overdueDays; }
    
    public BigDecimal getLateFeeCharges() { return lateFeeCharges; }
    public void setLateFeeCharges(BigDecimal lateFeeCharges) { this.lateFeeCharges = lateFeeCharges; }
    
    public BigDecimal getCollateralValue() { return collateralValue; }
    public void setCollateralValue(BigDecimal collateralValue) { this.collateralValue = collateralValue; }
    
    public String getCollateralType() { return collateralType; }
    public void setCollateralType(String collateralType) { this.collateralType = collateralType; }
    
    public String getCollateralDetails() { return collateralDetails; }
    public void setCollateralDetails(String collateralDetails) { this.collateralDetails = collateralDetails; }
    
    public String getPurpose() { return purpose; }
    public void setPurpose(String purpose) { this.purpose = purpose; }
    
    public String getEmploymentType() { return employmentType; }
    public void setEmploymentType(String employmentType) { this.employmentType = employmentType; }
    
    public BigDecimal getMonthlyIncome() { return monthlyIncome; }
    public void setMonthlyIncome(BigDecimal monthlyIncome) { this.monthlyIncome = monthlyIncome; }
    
    public BigDecimal getMonthlyExpenses() { return monthlyExpenses; }
    public void setMonthlyExpenses(BigDecimal monthlyExpenses) { this.monthlyExpenses = monthlyExpenses; }
    
    public BigDecimal getDebtToIncomeRatio() { return debtToIncomeRatio; }
    public void setDebtToIncomeRatio(BigDecimal debtToIncomeRatio) { this.debtToIncomeRatio = debtToIncomeRatio; }
    
    public Integer getCreditScore() { return creditScore; }
    public void setCreditScore(Integer creditScore) { this.creditScore = creditScore; }
    
    public String getRiskRating() { return riskRating; }
    public void setRiskRating(String riskRating) { this.riskRating = riskRating; }
    
    public Long getLoanOfficerId() { return loanOfficerId; }
    public void setLoanOfficerId(Long loanOfficerId) { this.loanOfficerId = loanOfficerId; }
    
    public Long getBranchManagerId() { return branchManagerId; }
    public void setBranchManagerId(Long branchManagerId) { this.branchManagerId = branchManagerId; }
    
    public String getApprovalAuthority() { return approvalAuthority; }
    public void setApprovalAuthority(String approvalAuthority) { this.approvalAuthority = approvalAuthority; }
    
    public String getApprovalLevel() { return approvalLevel; }
    public void setApprovalLevel(String approvalLevel) { this.approvalLevel = approvalLevel; }
    
    public String getDocumentsRequired() { return documentsRequired; }
    public void setDocumentsRequired(String documentsRequired) { this.documentsRequired = documentsRequired; }
    
    public String getDocumentsSubmitted() { return documentsSubmitted; }
    public void setDocumentsSubmitted(String documentsSubmitted) { this.documentsSubmitted = documentsSubmitted; }
    
    public String getKycStatus() { return kycStatus; }
    public void setKycStatus(String kycStatus) { this.kycStatus = kycStatus; }
    
    public LocalDate getKycCompletedDate() { return kycCompletedDate; }
    public void setKycCompletedDate(LocalDate kycCompletedDate) { this.kycCompletedDate = kycCompletedDate; }
    
    public String getInsurancePolicyNumber() { return insurancePolicyNumber; }
    public void setInsurancePolicyNumber(String insurancePolicyNumber) { this.insurancePolicyNumber = insurancePolicyNumber; }
    
    public String getInsuranceCompany() { return insuranceCompany; }
    public void setInsuranceCompany(String insuranceCompany) { this.insuranceCompany = insuranceCompany; }
    
    public LocalDate getInsuranceExpiryDate() { return insuranceExpiryDate; }
    public void setInsuranceExpiryDate(LocalDate insuranceExpiryDate) { this.insuranceExpiryDate = insuranceExpiryDate; }
    
    public String getGuarantorName() { return guarantorName; }
    public void setGuarantorName(String guarantorName) { this.guarantorName = guarantorName; }
    
    public String getGuarantorContact() { return guarantorContact; }
    public void setGuarantorContact(String guarantorContact) { this.guarantorContact = guarantorContact; }
    
    public BigDecimal getGuarantorIncome() { return guarantorIncome; }
    public void setGuarantorIncome(BigDecimal guarantorIncome) { this.guarantorIncome = guarantorIncome; }
    
    public String getCoApplicantName() { return coApplicantName; }
    public void setCoApplicantName(String coApplicantName) { this.coApplicantName = coApplicantName; }
    
    public String getCoApplicantContact() { return coApplicantContact; }
    public void setCoApplicantContact(String coApplicantContact) { this.coApplicantContact = coApplicantContact; }
    
    public BigDecimal getCoApplicantIncome() { return coApplicantIncome; }
    public void setCoApplicantIncome(BigDecimal coApplicantIncome) { this.coApplicantIncome = coApplicantIncome; }
    
    public String getRemarks() { return remarks; }
    public void setRemarks(String remarks) { this.remarks = remarks; }
    
    public String getAdditionalData() { return additionalData; }
    public void setAdditionalData(String additionalData) { this.additionalData = additionalData; }
    
    public User getUser() { return user; }
    public void setUser(User user) { this.user = user; }
    
    public Branch getBranch() { return branch; }
    public void setBranch(Branch branch) { this.branch = branch; }
    
    public User getApprovedBy() { return approvedBy; }
    public void setApprovedBy(User approvedBy) { this.approvedBy = approvedBy; }
    
    public Account getLoanAccount() { return loanAccount; }
    public void setLoanAccount(Account loanAccount) { this.loanAccount = loanAccount; }
    
    public List<LoanEMI> getEmiSchedule() { return emiSchedule; }
    public void setEmiSchedule(List<LoanEMI> emiSchedule) { this.emiSchedule = emiSchedule; }
    
    public List<Transaction> getLoanTransactions() { return loanTransactions; }
    public void setLoanTransactions(List<Transaction> loanTransactions) { this.loanTransactions = loanTransactions; }
    
    // Helper methods
    public void calculateTotalCharges() {
        this.totalCharges = this.processingFee.add(this.insurancePremium)
                .add(this.legalCharges).add(this.valuationCharges);
    }
    
    public void calculateTotalAmountPayable() {
        this.totalAmountPayable = this.principalAmount.add(this.totalInterestPayable)
                .add(this.totalCharges);
    }
    
    public boolean isActive() {
        return this.status == LoanStatus.ACTIVE;
    }
    
    public boolean isApproved() {
        return this.status == LoanStatus.APPROVED || this.status == LoanStatus.DISBURSED || 
               this.status == LoanStatus.ACTIVE;
    }
    
    public boolean isDisbursed() {
        return this.status == LoanStatus.DISBURSED || this.status == LoanStatus.ACTIVE;
    }
    
    public boolean isClosed() {
        return this.status == LoanStatus.CLOSED;
    }
    
    public boolean isDefaulted() {
        return this.status == LoanStatus.DEFAULTED;
    }
    
    public boolean isOverdue() {
        return this.overdueDays > 0;
    }
    
    public void calculateEMI() {
        if (this.principalAmount != null && this.interestRate != null && this.tenureMonths != null) {
            double principal = this.principalAmount.doubleValue();
            double rate = this.interestRate.doubleValue() / 100 / 12; // Monthly rate
            int tenure = this.tenureMonths;
            
            if (rate > 0) {
                double emi = principal * rate * Math.pow(1 + rate, tenure) / (Math.pow(1 + rate, tenure) - 1);
                this.emiAmount = BigDecimal.valueOf(emi).setScale(2, RoundingMode.HALF_UP);
            } else {
                this.emiAmount = this.principalAmount.divide(BigDecimal.valueOf(tenure), 2, RoundingMode.HALF_UP);
            }
        }
    }
    
    @Override
    public String toString() {
        return "Loan{" +
                "id=" + getId() +
                ", loanNumber='" + loanNumber + '\'' +
                ", type=" + type +
                ", principalAmount=" + principalAmount +
                ", status=" + status +
                ", user=" + (user != null ? user.getUsername() : "null") +
                '}';
    }
} 