package org.example.abcbanking.repository;

import org.example.abcbanking.model.Branch;
import org.example.abcbanking.model.BranchStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

@Repository
public interface BranchRepository extends JpaRepository<Branch, Long> {
    
    // Basic finders
    Optional<Branch> findByBranchCode(String branchCode);
    Optional<Branch> findByIfscCode(String ifscCode);
    Optional<Branch> findByMicrCode(String micrCode);
    
    // Existence checks
    boolean existsByBranchCode(String branchCode);
    boolean existsByIfscCode(String ifscCode);
    boolean existsByMicrCode(String micrCode);
    
    // Status-based queries
    List<Branch> findByStatus(BranchStatus status);
    Page<Branch> findByStatus(BranchStatus status, Pageable pageable);
    
    // Location-based queries
    List<Branch> findByCity(String city);
    List<Branch> findByState(String state);
    List<Branch> findByPinCode(String pinCode);
    List<Branch> findByCountry(String country);
    
    List<Branch> findByCityAndStatus(String city, BranchStatus status);
    List<Branch> findByStateAndStatus(String state, BranchStatus status);
    
    // Search queries
    @Query("SELECT b FROM Branch b WHERE " +
           "LOWER(b.name) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(b.branchCode) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(b.address) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(b.city) LIKE LOWER(CONCAT('%', :searchTerm, '%'))")
    Page<Branch> searchBranches(@Param("searchTerm") String searchTerm, Pageable pageable);
    
    // ATM-related queries
    List<Branch> findByIsAtmAvailable(Boolean isAtmAvailable);
    List<Branch> findByIsAtmAvailableAndStatus(Boolean isAtmAvailable, BranchStatus status);
    
    @Query("SELECT b FROM Branch b WHERE b.atmCount > 0")
    List<Branch> findBranchesWithATM();
    
    // 24x7 queries
    List<Branch> findByIs24x7(Boolean is24x7);
    List<Branch> findByIs24x7AndStatus(Boolean is24x7, BranchStatus status);
    
    // Performance-based queries
    @Query("SELECT b FROM Branch b WHERE b.totalAccounts >= :minAccounts")
    List<Branch> findByMinimumAccounts(@Param("minAccounts") Long minAccounts);
    
    @Query("SELECT b FROM Branch b WHERE b.totalCustomers >= :minCustomers")
    List<Branch> findByMinimumCustomers(@Param("minCustomers") Long minCustomers);
    
    @Query("SELECT b FROM Branch b WHERE b.totalDeposits >= :minDeposits")
    List<Branch> findByMinimumDeposits(@Param("minDeposits") BigDecimal minDeposits);
    
    @Query("SELECT b FROM Branch b WHERE b.branchRating >= :minRating")
    List<Branch> findByMinimumRating(@Param("minRating") BigDecimal minRating);
    
    // Geographic queries
    @Query("SELECT b FROM Branch b WHERE " +
           "b.latitude BETWEEN :minLat AND :maxLat AND " +
           "b.longitude BETWEEN :minLng AND :maxLng")
    List<Branch> findBranchesInGeographicBounds(
            @Param("minLat") BigDecimal minLat,
            @Param("maxLat") BigDecimal maxLat,
            @Param("minLng") BigDecimal minLng,
            @Param("maxLng") BigDecimal maxLng);
    
    // Complex queries
    @Query("SELECT b FROM Branch b WHERE " +
           "b.status = :status AND " +
           "b.isAtmAvailable = :hasATM AND " +
           "b.totalAccounts >= :minAccounts")
    List<Branch> findActiveBranchesWithATMAndMinAccounts(
            @Param("status") BranchStatus status,
            @Param("hasATM") Boolean hasATM,
            @Param("minAccounts") Long minAccounts);
    
    // Statistics queries
    @Query("SELECT COUNT(b) FROM Branch b WHERE b.status = :status")
    Long countByStatus(@Param("status") BranchStatus status);
    
    @Query("SELECT COUNT(b) FROM Branch b WHERE b.city = :city")
    Long countByCity(@Param("city") String city);
    
    @Query("SELECT COUNT(b) FROM Branch b WHERE b.isAtmAvailable = true")
    Long countBranchesWithATM();
    
    @Query("SELECT COUNT(b) FROM Branch b WHERE b.is24x7 = true")
    Long count24x7Branches();
    
    // Reporting queries
    @Query("SELECT b.city, COUNT(b) FROM Branch b GROUP BY b.city ORDER BY COUNT(b) DESC")
    List<Object[]> getBranchCountByCity();
    
    @Query("SELECT b.state, COUNT(b) FROM Branch b GROUP BY b.state ORDER BY COUNT(b) DESC")
    List<Object[]> getBranchCountByState();
    
    @Query("SELECT b.status, COUNT(b) FROM Branch b GROUP BY b.status")
    List<Object[]> getBranchCountByStatus();
    
    @Query("SELECT b.branchType, COUNT(b) FROM Branch b GROUP BY b.branchType")
    List<Object[]> getBranchCountByType();
    
    // Performance metrics
    @Query("SELECT b FROM Branch b ORDER BY b.totalDeposits DESC")
    Page<Branch> findTopBranchesByDeposits(Pageable pageable);
    
    @Query("SELECT b FROM Branch b ORDER BY b.totalCustomers DESC")
    Page<Branch> findTopBranchesByCustomers(Pageable pageable);
    
    @Query("SELECT b FROM Branch b ORDER BY b.branchRating DESC")
    Page<Branch> findTopBranchesByRating(Pageable pageable);
    
    // Manager queries
    @Query("SELECT b FROM Branch b WHERE b.branchManagerId = :managerId")
    List<Branch> findByBranchManagerId(@Param("managerId") Long managerId);
    
    // Specialization queries
    @Query("SELECT b FROM Branch b WHERE b.specializations LIKE %:specialization%")
    List<Branch> findBySpecialization(@Param("specialization") String specialization);
} 