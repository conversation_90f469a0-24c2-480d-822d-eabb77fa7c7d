package org.example.abcbanking.repository;

import org.example.abcbanking.model.ERole;
import org.example.abcbanking.model.Role;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface RoleRepository extends JpaRepository<Role, Long> {
    
    Optional<Role> findByName(ERole name);
    
    List<Role> findByIsActive(Boolean isActive);
    
    boolean existsByName(ERole name);
    
    List<Role> findByNameIn(List<ERole> names);
} 