package org.example.abcbanking.repository;

import org.example.abcbanking.model.User;
import org.example.abcbanking.model.UserStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface UserRepository extends JpaRepository<User, Long> {
    
    // Basic finders
    Optional<User> findByUsername(String username);
    Optional<User> findByEmail(String email);
    Optional<User> findByPhoneNumber(String phoneNumber);
    Optional<User> findByAadhaarNumber(String aadhaarNumber);
    Optional<User> findByPanNumber(String panNumber);
    
    // Existence checks
    boolean existsByUsername(String username);
    boolean existsByEmail(String email);
    boolean existsByPhoneNumber(String phoneNumber);
    boolean existsByAadhaarNumber(String aadhaarNumber);
    boolean existsByPanNumber(String panNumber);
    
    // Status-based queries
    List<User> findByStatus(UserStatus status);
    Page<User> findByStatus(UserStatus status, Pageable pageable);
    List<User> findByStatusIn(List<UserStatus> statuses);
    
    // Branch-based queries
    List<User> findByBranchId(Long branchId);
    Page<User> findByBranchId(Long branchId, Pageable pageable);
    List<User> findByBranchIdAndStatus(Long branchId, UserStatus status);
    
    // Role-based queries
    @Query("SELECT u FROM User u JOIN u.roles r WHERE r.name = :roleName")
    List<User> findByRoleName(@Param("roleName") String roleName);
    
    @Query("SELECT u FROM User u JOIN u.roles r WHERE r.name IN :roleNames")
    List<User> findByRoleNames(@Param("roleNames") List<String> roleNames);
    
    // Verification status queries
    List<User> findByIsEmailVerified(Boolean isEmailVerified);
    List<User> findByIsPhoneVerified(Boolean isPhoneVerified);
    List<User> findByIsKycVerified(Boolean isKycVerified);
    
    // Date-based queries
    List<User> findByCreatedAtBetween(LocalDateTime startDate, LocalDateTime endDate);
    List<User> findByLastLoginBetween(LocalDateTime startDate, LocalDateTime endDate);
    
    // Search queries
    @Query("SELECT u FROM User u WHERE " +
           "LOWER(u.firstName) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(u.lastName) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(u.email) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(u.phoneNumber) LIKE LOWER(CONCAT('%', :searchTerm, '%'))")
    Page<User> searchUsers(@Param("searchTerm") String searchTerm, Pageable pageable);

    // Alternative search method for service compatibility
    Page<User> findByUsernameContainingOrEmailContainingOrFirstNameContainingOrLastNameContaining(
            String username, String email, String firstName, String lastName, Pageable pageable);
    
    // Location-based queries
    List<User> findByCity(String city);
    List<User> findByState(String state);
    List<User> findByPinCode(String pinCode);
    
    // Account status queries
    List<User> findByFailedLoginAttemptsGreaterThan(Integer attempts);
    List<User> findByAccountLockedUntilBefore(LocalDateTime date);
    
    // Complex queries
    @Query("SELECT u FROM User u WHERE " +
           "u.status = :status AND " +
           "u.branch.id = :branchId AND " +
           "u.createdAt >= :startDate")
    List<User> findActiveUsersByBranchAndDateRange(
            @Param("status") UserStatus status,
            @Param("branchId") Long branchId,
            @Param("startDate") LocalDateTime startDate);
    
    // Statistics queries
    @Query("SELECT COUNT(u) FROM User u WHERE u.status = :status")
    Long countByStatus(@Param("status") UserStatus status);
    
    @Query("SELECT COUNT(u) FROM User u WHERE u.branch.id = :branchId")
    Long countByBranchId(@Param("branchId") Long branchId);
    
    @Query("SELECT COUNT(u) FROM User u WHERE u.createdAt >= :startDate")
    Long countNewUsersSince(@Param("startDate") LocalDateTime startDate);
    
    // Risk-based queries
    List<User> findByRiskProfile(String riskProfile);
    List<User> findByRiskProfileAndStatus(String riskProfile, UserStatus status);
    
    // Income-based queries
    @Query("SELECT u FROM User u WHERE u.monthlyIncome >= :minIncome")
    List<User> findByMinimumIncome(@Param("minIncome") java.math.BigDecimal minIncome);
    
    @Query("SELECT u FROM User u WHERE u.monthlyIncome BETWEEN :minIncome AND :maxIncome")
    List<User> findByIncomeRange(
            @Param("minIncome") java.math.BigDecimal minIncome,
            @Param("maxIncome") java.math.BigDecimal maxIncome);
    
    // Custom queries for reporting
    @Query("SELECT u.city, COUNT(u) FROM User u GROUP BY u.city ORDER BY COUNT(u) DESC")
    List<Object[]> getUserCountByCity();
    
    @Query("SELECT u.status, COUNT(u) FROM User u GROUP BY u.status")
    List<Object[]> getUserCountByStatus();
    
    @Query("SELECT u.riskProfile, COUNT(u) FROM User u GROUP BY u.riskProfile")
    List<Object[]> getUserCountByRiskProfile();
    
    // Performance queries
    @Query("SELECT u FROM User u WHERE u.lastLogin IS NULL OR u.lastLogin < :date")
    List<User> findInactiveUsers(@Param("date") LocalDateTime date);
    
    @Query("SELECT u FROM User u WHERE u.passwordExpiresAt IS NOT NULL AND u.passwordExpiresAt < :date")
    List<User> findUsersWithExpiredPasswords(@Param("date") LocalDateTime date);
} 