package org.example.abcbanking.repository;

import org.example.abcbanking.model.Notification;
import org.example.abcbanking.model.NotificationStatus;
import org.example.abcbanking.model.NotificationType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface NotificationRepository extends JpaRepository<Notification, Long> {
    
    // Basic user queries
    List<Notification> findByUserId(Long userId);
    Page<Notification> findByUserId(Long userId, Pageable pageable);
    
    // Read/Unread queries
    List<Notification> findByUserIdAndIsRead(Long userId, Boolean isRead);
    Page<Notification> findByUserIdAndIsRead(Long userId, Boolean isRead, Pageable pageable);
    
    @Query("SELECT n FROM Notification n WHERE n.user.id = :userId AND n.isRead = false AND n.isDeleted = false ORDER BY n.createdAt DESC")
    List<Notification> findUnreadNotificationsByUserId(@Param("userId") Long userId);
    
    @Query("SELECT COUNT(n) FROM Notification n WHERE n.user.id = :userId AND n.isRead = false AND n.isDeleted = false")
    Long countUnreadNotificationsByUserId(@Param("userId") Long userId);
    
    // Type-based queries
    List<Notification> findByType(NotificationType type);
    List<Notification> findByUserIdAndType(Long userId, NotificationType type);
    
    // Status-based queries
    List<Notification> findByStatus(NotificationStatus status);
    List<Notification> findByUserIdAndStatus(Long userId, NotificationStatus status);
    
    // Priority-based queries
    @Query("SELECT n FROM Notification n WHERE n.user.id = :userId AND n.priority = :priority AND n.isDeleted = false")
    List<Notification> findByUserIdAndPriority(@Param("userId") Long userId, @Param("priority") String priority);
    
    // Date-based queries
    @Query("SELECT n FROM Notification n WHERE n.user.id = :userId AND n.createdAt BETWEEN :startDate AND :endDate AND n.isDeleted = false")
    List<Notification> findByUserIdAndDateRange(@Param("userId") Long userId, 
                                               @Param("startDate") LocalDateTime startDate, 
                                               @Param("endDate") LocalDateTime endDate);
    
    // Reference-based queries
    @Query("SELECT n FROM Notification n WHERE n.referenceId = :referenceId AND n.referenceType = :referenceType AND n.isDeleted = false")
    List<Notification> findByReference(@Param("referenceId") String referenceId, @Param("referenceType") String referenceType);
    
    // Category-based queries
    @Query("SELECT n FROM Notification n WHERE n.user.id = :userId AND n.category = :category AND n.isDeleted = false ORDER BY n.createdAt DESC")
    List<Notification> findByUserIdAndCategory(@Param("userId") Long userId, @Param("category") String category);
    
    // Pending notifications for processing
    @Query("SELECT n FROM Notification n WHERE n.status = 'PENDING' AND (n.scheduledAt IS NULL OR n.scheduledAt <= :now) AND n.isDeleted = false")
    List<Notification> findPendingNotificationsToSend(@Param("now") LocalDateTime now);
    
    @Query("SELECT n FROM Notification n WHERE n.status = 'FAILED' AND n.deliveryAttempts < n.maxDeliveryAttempts AND n.isDeleted = false")
    List<Notification> findFailedNotificationsForRetry();
    
    // Channel-based queries
    @Query("SELECT n FROM Notification n WHERE n.channel = :channel AND n.status = :status AND n.isDeleted = false")
    List<Notification> findByChannelAndStatus(@Param("channel") String channel, @Param("status") NotificationStatus status);
    
    // Update operations
    @Modifying
    @Query("UPDATE Notification n SET n.isRead = true, n.readAt = :readAt WHERE n.id = :notificationId")
    void markAsRead(@Param("notificationId") Long notificationId, @Param("readAt") LocalDateTime readAt);
    
    @Modifying
    @Query("UPDATE Notification n SET n.isRead = true, n.readAt = :readAt WHERE n.user.id = :userId AND n.isRead = false")
    void markAllAsReadByUserId(@Param("userId") Long userId, @Param("readAt") LocalDateTime readAt);
    
    @Modifying
    @Query("UPDATE Notification n SET n.status = 'EXPIRED' WHERE n.expiresAt <= :now AND n.status != 'EXPIRED'")
    void markExpiredNotifications(@Param("now") LocalDateTime now);
    
    // Statistics queries
    @Query("SELECT n.type, COUNT(n) FROM Notification n WHERE n.user.id = :userId AND n.isDeleted = false GROUP BY n.type")
    List<Object[]> getNotificationCountsByType(@Param("userId") Long userId);
    
    @Query("SELECT n.status, COUNT(n) FROM Notification n WHERE n.user.id = :userId AND n.isDeleted = false GROUP BY n.status")
    List<Object[]> getNotificationCountsByStatus(@Param("userId") Long userId);
    
    @Query("SELECT DATE(n.createdAt), COUNT(n) FROM Notification n WHERE n.user.id = :userId AND n.createdAt >= :startDate AND n.isDeleted = false GROUP BY DATE(n.createdAt)")
    List<Object[]> getNotificationCountsByDate(@Param("userId") Long userId, @Param("startDate") LocalDateTime startDate);
    
    // Cleanup operations
    @Modifying
    @Query("DELETE FROM Notification n WHERE n.createdAt < :cutoffDate AND n.isRead = true")
    void deleteOldReadNotifications(@Param("cutoffDate") LocalDateTime cutoffDate);
    
    @Modifying
    @Query("UPDATE Notification n SET n.isDeleted = true WHERE n.createdAt < :cutoffDate AND n.isRead = true")
    void softDeleteOldReadNotifications(@Param("cutoffDate") LocalDateTime cutoffDate);
}
