package org.example.abcbanking.repository;

import org.example.abcbanking.model.EMIStatus;
import org.example.abcbanking.model.LoanEMI;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

@Repository
public interface LoanEMIRepository extends JpaRepository<LoanEMI, Long> {
    
    // Basic finders
    List<LoanEMI> findByLoanId(Long loanId);
    Optional<LoanEMI> findByLoanIdAndEmiNumber(Long loanId, Integer emiNumber);
    
    // Status-based queries
    List<LoanEMI> findByStatus(EMIStatus status);
    List<LoanEMI> findByLoanIdAndStatus(Long loanId, EMIStatus status);
    
    // Date-based queries
    List<LoanEMI> findByDueDate(LocalDate dueDate);
    List<LoanEMI> findByDueDateBetween(LocalDate startDate, LocalDate endDate);
    List<LoanEMI> findByPaidDate(LocalDate paidDate);
    List<LoanEMI> findByPaidDateBetween(LocalDate startDate, LocalDate endDate);
    
    // Overdue queries
    @Query("SELECT e FROM LoanEMI e WHERE e.overdueDays > 0")
    List<LoanEMI> findOverdueEMIs();
    
    @Query("SELECT e FROM LoanEMI e WHERE e.overdueDays >= :minOverdueDays")
    List<LoanEMI> findByMinimumOverdueDays(@Param("minOverdueDays") Integer minOverdueDays);
    
    @Query("SELECT e FROM LoanEMI e WHERE e.dueDate < :date AND e.status = 'PENDING'")
    List<LoanEMI> findOverdueEMIsByDate(@Param("date") LocalDate date);
    
    // Amount-based queries
    @Query("SELECT e FROM LoanEMI e WHERE e.emiAmount >= :minAmount")
    List<LoanEMI> findByMinimumEmiAmount(@Param("minAmount") BigDecimal minAmount);
    
    @Query("SELECT e FROM LoanEMI e WHERE e.emiAmount <= :maxAmount")
    List<LoanEMI> findByMaximumEmiAmount(@Param("maxAmount") BigDecimal maxAmount);
    
    @Query("SELECT e FROM LoanEMI e WHERE e.emiAmount BETWEEN :minAmount AND :maxAmount")
    List<LoanEMI> findByEmiAmountRange(
            @Param("minAmount") BigDecimal minAmount,
            @Param("maxAmount") BigDecimal maxAmount);
    
    // Payment method queries
    List<LoanEMI> findByPaymentMethod(String paymentMethod);
    List<LoanEMI> findByLoanIdAndPaymentMethod(Long loanId, String paymentMethod);
    
    // Transaction queries
    List<LoanEMI> findByTransactionId(String transactionId);
    
    // Complex queries
    @Query("SELECT e FROM LoanEMI e WHERE " +
           "e.loan.id = :loanId AND " +
           "e.status = :status AND " +
           "e.dueDate BETWEEN :startDate AND :endDate")
    List<LoanEMI> findByLoanStatusAndDateRange(
            @Param("loanId") Long loanId,
            @Param("status") EMIStatus status,
            @Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate);
    
    // Statistics queries
    @Query("SELECT COUNT(e) FROM LoanEMI e WHERE e.status = :status")
    Long countByStatus(@Param("status") EMIStatus status);
    
    @Query("SELECT COUNT(e) FROM LoanEMI e WHERE e.loan.id = :loanId")
    Long countByLoanId(@Param("loanId") Long loanId);
    
    @Query("SELECT COUNT(e) FROM LoanEMI e WHERE e.loan.id = :loanId AND e.status = :status")
    Long countByLoanIdAndStatus(@Param("loanId") Long loanId, @Param("status") EMIStatus status);
    
    @Query("SELECT COUNT(e) FROM LoanEMI e WHERE e.overdueDays > 0")
    Long countOverdueEMIs();
    
    // Amount statistics
    @Query("SELECT SUM(e.emiAmount) FROM LoanEMI e WHERE e.status = :status")
    BigDecimal getTotalEmiAmountByStatus(@Param("status") EMIStatus status);
    
    @Query("SELECT SUM(e.emiAmount) FROM LoanEMI e WHERE e.loan.id = :loanId")
    BigDecimal getTotalEmiAmountByLoan(@Param("loanId") Long loanId);
    
    @Query("SELECT SUM(e.emiAmount) FROM LoanEMI e WHERE e.loan.id = :loanId AND e.status = :status")
    BigDecimal getTotalEmiAmountByLoanAndStatus(@Param("loanId") Long loanId, @Param("status") EMIStatus status);
    
    @Query("SELECT SUM(e.paidAmount) FROM LoanEMI e WHERE e.loan.id = :loanId")
    BigDecimal getTotalPaidAmountByLoan(@Param("loanId") Long loanId);
    
    @Query("SELECT SUM(e.lateFee) FROM LoanEMI e WHERE e.loan.id = :loanId")
    BigDecimal getTotalLateFeeByLoan(@Param("loanId") Long loanId);
    
    // Reporting queries
    @Query("SELECT e.status, COUNT(e) FROM LoanEMI e GROUP BY e.status")
    List<Object[]> getEmiCountByStatus();
    
    @Query("SELECT e.paymentMethod, COUNT(e) FROM LoanEMI e GROUP BY e.paymentMethod")
    List<Object[]> getEmiCountByPaymentMethod();
    
    @Query("SELECT e.status, SUM(e.emiAmount) FROM LoanEMI e GROUP BY e.status")
    List<Object[]> getEmiAmountByStatus();
    
    // Monthly statistics
    @Query("SELECT YEAR(e.dueDate), MONTH(e.dueDate), COUNT(e), SUM(e.emiAmount) " +
           "FROM LoanEMI e " +
           "WHERE e.dueDate BETWEEN :startDate AND :endDate " +
           "GROUP BY YEAR(e.dueDate), MONTH(e.dueDate) " +
           "ORDER BY YEAR(e.dueDate), MONTH(e.dueDate)")
    List<Object[]> getMonthlyEmiStats(
            @Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate);
    
    // Upcoming EMIs
    @Query("SELECT e FROM LoanEMI e WHERE e.dueDate >= :startDate AND e.status = 'PENDING' ORDER BY e.dueDate")
    List<LoanEMI> findUpcomingEMIs(@Param("startDate") LocalDate startDate);
    
    // Today's due EMIs
    @Query("SELECT e FROM LoanEMI e WHERE e.dueDate = :date AND e.status = 'PENDING'")
    List<LoanEMI> findTodaysDueEMIs(@Param("date") LocalDate date);
    
    // This month's EMIs
    @Query("SELECT e FROM LoanEMI e WHERE " +
           "YEAR(e.dueDate) = YEAR(:date) AND " +
           "MONTH(e.dueDate) = MONTH(:date)")
    List<LoanEMI> findThisMonthsEMIs(@Param("date") LocalDate date);
    
    // Next month's EMIs
    @Query("SELECT e FROM LoanEMI e WHERE " +
           "YEAR(e.dueDate) = YEAR(:date) AND " +
           "MONTH(e.dueDate) = MONTH(:date) + 1")
    List<LoanEMI> findNextMonthsEMIs(@Param("date") LocalDate date);
    
    // EMI number queries
    @Query("SELECT MAX(e.emiNumber) FROM LoanEMI e WHERE e.loan.id = :loanId")
    Integer findMaxEmiNumberByLoan(@Param("loanId") Long loanId);
    
    @Query("SELECT e FROM LoanEMI e WHERE e.loan.id = :loanId ORDER BY e.emiNumber")
    List<LoanEMI> findByLoanIdOrderByEmiNumber(@Param("loanId") Long loanId);
    
    // Late fee queries
    @Query("SELECT e FROM LoanEMI e WHERE e.lateFee > 0")
    List<LoanEMI> findEMIsWithLateFee();
    
    @Query("SELECT e FROM LoanEMI e WHERE e.loan.id = :loanId AND e.lateFee > 0")
    List<LoanEMI> findEMIsWithLateFeeByLoan(@Param("loanId") Long loanId);
    
    // Principal and interest component queries
    @Query("SELECT SUM(e.principalComponent) FROM LoanEMI e WHERE e.loan.id = :loanId")
    BigDecimal getTotalPrincipalComponentByLoan(@Param("loanId") Long loanId);
    
    @Query("SELECT SUM(e.interestComponent) FROM LoanEMI e WHERE e.loan.id = :loanId")
    BigDecimal getTotalInterestComponentByLoan(@Param("loanId") Long loanId);
    
    // Balance queries
    @Query("SELECT SUM(e.openingBalance) FROM LoanEMI e WHERE e.loan.id = :loanId")
    BigDecimal getTotalOpeningBalanceByLoan(@Param("loanId") Long loanId);
    
    @Query("SELECT SUM(e.closingBalance) FROM LoanEMI e WHERE e.loan.id = :loanId")
    BigDecimal getTotalClosingBalanceByLoan(@Param("loanId") Long loanId);
    
    // Performance queries
    @Query("SELECT e FROM LoanEMI e WHERE e.status = 'PENDING' AND e.dueDate < :date")
    List<LoanEMI> findStalePendingEMIs(@Param("date") LocalDate date);
    
    @Query("SELECT e FROM LoanEMI e WHERE e.status = 'PAID' AND e.paidDate IS NULL")
    List<LoanEMI> findInconsistentPaidEMIs();

    // Additional methods for LoanService compatibility
    @Query("SELECT e FROM LoanEMI e WHERE e.dueDate < :currentDate AND e.status = 'PENDING'")
    List<LoanEMI> findOverdueEmis(@Param("currentDate") LocalDate currentDate);

    @Query("SELECT e FROM LoanEMI e WHERE e.dueDate BETWEEN :startDate AND :endDate AND e.status = 'PENDING'")
    List<LoanEMI> findUpcomingEmis(@Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

    @Query("SELECT e FROM LoanEMI e WHERE e.loan.user.id = :userId AND e.dueDate < CURRENT_DATE AND e.status = 'PENDING'")
    List<LoanEMI> findOverdueEmisByUserId(@Param("userId") Long userId);

    @Query("SELECT e FROM LoanEMI e WHERE e.status = 'DEFAULTED'")
    List<LoanEMI> findDefaultedEmis();
}