package org.example.abcbanking.repository;

import org.example.abcbanking.model.Transaction;
import org.example.abcbanking.model.TransactionStatus;
import org.example.abcbanking.model.TransactionType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface TransactionRepository extends JpaRepository<Transaction, Long> {
    
    // Basic finders
    Optional<Transaction> findByTransactionId(String transactionId);
    Optional<Transaction> findByTransactionIdAndStatus(String transactionId, TransactionStatus status);
    
    // Existence checks
    boolean existsByTransactionId(String transactionId);
    
    // Account-based queries
    List<Transaction> findByFromAccountId(Long fromAccountId);
    List<Transaction> findByToAccountId(Long toAccountId);
    List<Transaction> findByFromAccountIdOrToAccountId(Long fromAccountId, Long toAccountId);
    
    Page<Transaction> findByFromAccountId(Long fromAccountId, Pageable pageable);
    Page<Transaction> findByToAccountId(Long toAccountId, Pageable pageable);
    Page<Transaction> findByFromAccountIdOrToAccountId(Long fromAccountId, Long toAccountId, Pageable pageable);
    
    // Type-based queries
    List<Transaction> findByType(TransactionType type);
    Page<Transaction> findByType(TransactionType type, Pageable pageable);
    List<Transaction> findByTypeIn(List<TransactionType> types);
    
    // Status-based queries
    List<Transaction> findByStatus(TransactionStatus status);
    Page<Transaction> findByStatus(TransactionStatus status, Pageable pageable);
    List<Transaction> findByStatusIn(List<TransactionStatus> statuses);
    
    // Date-based queries
    List<Transaction> findByTransactionDate(LocalDateTime transactionDate);
    List<Transaction> findByTransactionDateBetween(LocalDateTime startDate, LocalDateTime endDate);
    List<Transaction> findByProcessingDateBetween(LocalDateTime startDate, LocalDateTime endDate);
    List<Transaction> findBySettlementDateBetween(LocalDateTime startDate, LocalDateTime endDate);
    
    // Amount-based queries
    @Query("SELECT t FROM Transaction t WHERE t.amount >= :minAmount")
    List<Transaction> findByMinimumAmount(@Param("minAmount") BigDecimal minAmount);
    
    @Query("SELECT t FROM Transaction t WHERE t.amount <= :maxAmount")
    List<Transaction> findByMaximumAmount(@Param("maxAmount") BigDecimal maxAmount);
    
    @Query("SELECT t FROM Transaction t WHERE t.amount BETWEEN :minAmount AND :maxAmount")
    List<Transaction> findByAmountRange(
            @Param("minAmount") BigDecimal minAmount,
            @Param("maxAmount") BigDecimal maxAmount);
    
    // Channel-based queries
    List<Transaction> findByChannel(String channel);
    List<Transaction> findByChannelAndStatus(String channel, TransactionStatus status);
    
    // Branch-based queries
    List<Transaction> findByBranchId(Long branchId);
    Page<Transaction> findByBranchId(Long branchId, Pageable pageable);
    List<Transaction> findByBranchIdAndStatus(Long branchId, TransactionStatus status);
    
    // User-based queries
    List<Transaction> findByPerformedById(Long performedById);
    List<Transaction> findByApprovedById(Long approvedById);
    
    // Complex queries
    @Query("SELECT t FROM Transaction t WHERE " +
           "t.fromAccount.id = :accountId AND " +
           "t.type = :type AND " +
           "t.status = :status")
    List<Transaction> findByAccountTypeAndStatus(
            @Param("accountId") Long accountId,
            @Param("type") TransactionType type,
            @Param("status") TransactionStatus status);
    
    @Query("SELECT t FROM Transaction t WHERE " +
           "t.branch.id = :branchId AND " +
           "t.type = :type AND " +
           "t.transactionDate BETWEEN :startDate AND :endDate")
    List<Transaction> findByBranchTypeAndDateRange(
            @Param("branchId") Long branchId,
            @Param("type") TransactionType type,
            @Param("startDate") LocalDateTime startDate,
            @Param("endDate") LocalDateTime endDate);
    
    // Search queries
    @Query("SELECT t FROM Transaction t WHERE " +
           "t.transactionId LIKE %:searchTerm% OR " +
           "t.referenceNumber LIKE %:searchTerm% OR " +
           "t.externalReference LIKE %:searchTerm% OR " +
           "t.description LIKE %:searchTerm%")
    Page<Transaction> searchTransactions(@Param("searchTerm") String searchTerm, Pageable pageable);
    
    // Reversal queries
    List<Transaction> findByIsReversed(Boolean isReversed);
    List<Transaction> findByOriginalTransactionId(String originalTransactionId);
    
    // Suspicious transaction queries
    List<Transaction> findByIsSuspicious(Boolean isSuspicious);
    List<Transaction> findByRiskScoreGreaterThan(Integer riskScore);
    
    // Compliance queries
    @Query("SELECT t FROM Transaction t WHERE t.complianceStatus = :complianceStatus")
    List<Transaction> findByComplianceStatus(@Param("complianceStatus") String complianceStatus);
    
    @Query("SELECT t FROM Transaction t WHERE t.amlCheckStatus = :amlStatus")
    List<Transaction> findByAmlCheckStatus(@Param("amlStatus") String amlStatus);
    
    @Query("SELECT t FROM Transaction t WHERE t.kycVerificationStatus = :kycStatus")
    List<Transaction> findByKycVerificationStatus(@Param("kycStatus") String kycStatus);
    
    // Statistics queries
    @Query("SELECT COUNT(t) FROM Transaction t WHERE t.status = :status")
    Long countByStatus(@Param("status") TransactionStatus status);
    
    @Query("SELECT COUNT(t) FROM Transaction t WHERE t.type = :type")
    Long countByType(@Param("type") TransactionType type);
    
    @Query("SELECT COUNT(t) FROM Transaction t WHERE t.branch.id = :branchId")
    Long countByBranchId(@Param("branchId") Long branchId);
    
    @Query("SELECT COUNT(t) FROM Transaction t WHERE t.transactionDate >= :startDate")
    Long countTransactionsSince(@Param("startDate") LocalDateTime startDate);
    
    // Amount statistics
    @Query("SELECT SUM(t.amount) FROM Transaction t WHERE t.status = :status")
    BigDecimal getTotalAmountByStatus(@Param("status") TransactionStatus status);
    
    @Query("SELECT SUM(t.amount) FROM Transaction t WHERE t.type = :type")
    BigDecimal getTotalAmountByType(@Param("type") TransactionType type);
    
    @Query("SELECT SUM(t.amount) FROM Transaction t WHERE t.branch.id = :branchId")
    BigDecimal getTotalAmountByBranch(@Param("branchId") Long branchId);
    
    @Query("SELECT SUM(t.amount) FROM Transaction t WHERE t.transactionDate BETWEEN :startDate AND :endDate")
    BigDecimal getTotalAmountByDateRange(
            @Param("startDate") LocalDateTime startDate,
            @Param("endDate") LocalDateTime endDate);
    
    @Query("SELECT AVG(t.amount) FROM Transaction t WHERE t.status = :status")
    BigDecimal getAverageAmountByStatus(@Param("status") TransactionStatus status);
    
    // Reporting queries
    @Query("SELECT t.type, COUNT(t) FROM Transaction t GROUP BY t.type")
    List<Object[]> getTransactionCountByType();
    
    @Query("SELECT t.status, COUNT(t) FROM Transaction t GROUP BY t.status")
    List<Object[]> getTransactionCountByStatus();
    
    @Query("SELECT t.channel, COUNT(t) FROM Transaction t GROUP BY t.channel")
    List<Object[]> getTransactionCountByChannel();
    
    @Query("SELECT t.type, SUM(t.amount) FROM Transaction t GROUP BY t.type")
    List<Object[]> getTransactionAmountByType();
    
    @Query("SELECT t.status, SUM(t.amount) FROM Transaction t GROUP BY t.status")
    List<Object[]> getTransactionAmountByStatus();
    
    // Daily/Monthly statistics
    @Query("SELECT DATE(t.transactionDate), COUNT(t), SUM(t.amount) " +
           "FROM Transaction t " +
           "WHERE t.transactionDate BETWEEN :startDate AND :endDate " +
           "GROUP BY DATE(t.transactionDate) " +
           "ORDER BY DATE(t.transactionDate)")
    List<Object[]> getDailyTransactionStats(
            @Param("startDate") LocalDateTime startDate,
            @Param("endDate") LocalDateTime endDate);
    
    @Query("SELECT YEAR(t.transactionDate), MONTH(t.transactionDate), COUNT(t), SUM(t.amount) " +
           "FROM Transaction t " +
           "WHERE t.transactionDate BETWEEN :startDate AND :endDate " +
           "GROUP BY YEAR(t.transactionDate), MONTH(t.transactionDate) " +
           "ORDER BY YEAR(t.transactionDate), MONTH(t.transactionDate)")
    List<Object[]> getMonthlyTransactionStats(
            @Param("startDate") LocalDateTime startDate,
            @Param("endDate") LocalDateTime endDate);
    
    // Top transactions
    @Query("SELECT t FROM Transaction t ORDER BY t.amount DESC")
    Page<Transaction> findTopTransactionsByAmount(Pageable pageable);
    
    @Query("SELECT t FROM Transaction t WHERE t.status = :status ORDER BY t.amount DESC")
    Page<Transaction> findTopTransactionsByAmountAndStatus(
            @Param("status") TransactionStatus status,
            Pageable pageable);
    
    // Failed transactions
    @Query("SELECT t FROM Transaction t WHERE t.status = 'FAILED'")
    List<Transaction> findFailedTransactions();
    
    @Query("SELECT t FROM Transaction t WHERE t.status = 'FAILED' AND t.transactionDate >= :startDate")
    List<Transaction> findFailedTransactionsSince(@Param("startDate") LocalDateTime startDate);
    
    // Pending transactions
    @Query("SELECT t FROM Transaction t WHERE t.status = 'PENDING'")
    List<Transaction> findPendingTransactions();
    
    @Query("SELECT t FROM Transaction t WHERE t.status = 'PENDING' AND t.transactionDate < :date")
    List<Transaction> findStalePendingTransactions(@Param("date") LocalDateTime date);
    
    // Reversed transactions
    @Query("SELECT t FROM Transaction t WHERE t.isReversed = true")
    List<Transaction> findReversedTransactions();
    
    @Query("SELECT t FROM Transaction t WHERE t.reversalDate BETWEEN :startDate AND :endDate")
    List<Transaction> findReversedTransactionsByDateRange(
            @Param("startDate") LocalDateTime startDate,
            @Param("endDate") LocalDateTime endDate);
    
    // Suspicious transactions
    @Query("SELECT t FROM Transaction t WHERE t.isSuspicious = true")
    List<Transaction> findSuspiciousTransactions();
    
    @Query("SELECT t FROM Transaction t WHERE t.riskScore >= :minRiskScore")
    List<Transaction> findHighRiskTransactions(@Param("minRiskScore") Integer minRiskScore);
    
    // Location-based queries
    @Query("SELECT t FROM Transaction t WHERE t.location LIKE %:location%")
    List<Transaction> findByLocation(@Param("location") String location);
    
    @Query("SELECT t FROM Transaction t WHERE " +
           "t.latitude BETWEEN :minLat AND :maxLat AND " +
           "t.longitude BETWEEN :minLng AND :maxLng")
    List<Transaction> findTransactionsInGeographicBounds(
            @Param("minLat") BigDecimal minLat,
            @Param("maxLat") BigDecimal maxLat,
            @Param("minLng") BigDecimal minLng,
            @Param("maxLng") BigDecimal maxLng);

    // Search methods for service compatibility
    Page<Transaction> findByTransactionIdContainingOrDescriptionContaining(
            String transactionId, String description, Pageable pageable);

    // Total amount calculation
    @Query("SELECT COALESCE(SUM(t.amount), 0) FROM Transaction t WHERE " +
           "(t.fromAccount.id = :accountId OR t.toAccount.id = :accountId) AND " +
           "t.status = 'COMPLETED'")
    BigDecimal getTotalAmountByAccountId(@Param("accountId") Long accountId);
} 