package org.example.abcbanking.repository;

import org.example.abcbanking.model.Card;
import org.example.abcbanking.model.CardStatus;
import org.example.abcbanking.model.CardType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

@Repository
public interface CardRepository extends JpaRepository<Card, Long> {
    
    // Basic queries
    Optional<Card> findByCardNumber(String cardNumber);
    List<Card> findByStatus(CardStatus status);
    List<Card> findByCardType(CardType cardType);
    
    // User-specific queries
    @Query("SELECT c FROM Card c WHERE c.account.user.id = :userId AND c.isDeleted = false")
    List<Card> findByUserId(@Param("userId") Long userId);
    
    @Query("SELECT c FROM Card c WHERE c.account.user.id = :userId AND c.isDeleted = false")
    Page<Card> findByUserIdAndNotDeleted(@Param("userId") Long userId, Pageable pageable);
    
    @Query("SELECT c FROM Card c WHERE c.account.user.id = :userId AND c.status = :status AND c.isDeleted = false")
    List<Card> findByUserIdAndStatus(@Param("userId") Long userId, @Param("status") CardStatus status);
    
    // Account-specific queries
    List<Card> findByAccountId(Long accountId);
    
    @Query("SELECT c FROM Card c WHERE c.account.id = :accountId AND c.isDeleted = false")
    List<Card> findByAccountIdAndNotDeleted(@Param("accountId") Long accountId);
    
    // Status-based queries
    @Query("SELECT c FROM Card c WHERE c.isBlocked = true AND c.isDeleted = false")
    List<Card> findBlockedCards();
    
    @Query("SELECT c FROM Card c WHERE c.status = 'ACTIVE' AND c.isDeleted = false")
    List<Card> findActiveCards();
    
    // Expiry-based queries
    @Query("SELECT c FROM Card c WHERE c.expiryDate <= :expiryDate AND c.status = 'ACTIVE' AND c.isDeleted = false")
    List<Card> findExpiringCards(@Param("expiryDate") LocalDate expiryDate);
    
    @Query("SELECT c FROM Card c WHERE c.expiryDate BETWEEN :startDate AND :endDate AND c.isDeleted = false")
    List<Card> findCardsExpiringBetween(@Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);
    
    // Count queries
    @Query("SELECT COUNT(c) FROM Card c WHERE c.cardType = :cardType AND c.status = 'ACTIVE' AND c.isDeleted = false")
    Long countActiveCardsByType(@Param("cardType") CardType cardType);
    
    @Query("SELECT COUNT(c) FROM Card c WHERE c.account.user.id = :userId AND c.status = 'ACTIVE' AND c.isDeleted = false")
    Long countActiveCardsByUserId(@Param("userId") Long userId);
    
    @Query("SELECT COUNT(c) FROM Card c WHERE c.account.user.id = :userId AND c.isDeleted = false")
    Long countCardsByUserId(@Param("userId") Long userId);
    
    // Search queries
    @Query("SELECT c FROM Card c WHERE c.cardHolderName LIKE %:searchTerm% OR c.cardNumber LIKE %:searchTerm% AND c.isDeleted = false")
    List<Card> searchCards(@Param("searchTerm") String searchTerm);
    
    @Query("SELECT c FROM Card c WHERE c.account.user.id = :userId AND (c.cardHolderName LIKE %:searchTerm% OR c.cardNumber LIKE %:searchTerm%) AND c.isDeleted = false")
    List<Card> searchCardsByUser(@Param("userId") Long userId, @Param("searchTerm") String searchTerm);
    
    // Date-based queries
    @Query("SELECT c FROM Card c WHERE c.activationDate BETWEEN :startDate AND :endDate AND c.isDeleted = false")
    List<Card> findCardsActivatedBetween(@Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);
    
    @Query("SELECT c FROM Card c WHERE c.createdAt >= :date AND c.isDeleted = false")
    List<Card> findCardsCreatedAfter(@Param("date") LocalDate date);
    
    // Replacement queries
    @Query("SELECT c FROM Card c WHERE c.replacedCardId = :cardId AND c.isDeleted = false")
    Optional<Card> findReplacementCard(@Param("cardId") Long cardId);
    
    @Query("SELECT c FROM Card c WHERE c.replacedCardId IS NOT NULL AND c.isDeleted = false")
    List<Card> findReplacementCards();
}
