package org.example.abcbanking.repository;

import org.example.abcbanking.model.Account;
import org.example.abcbanking.model.AccountStatus;
import org.example.abcbanking.model.AccountType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

@Repository
public interface AccountRepository extends JpaRepository<Account, Long> {
    
    // Basic finders
    Optional<Account> findByAccountNumber(String accountNumber);
    Optional<Account> findByAccountNumberAndStatus(String accountNumber, AccountStatus status);
    
    // Existence checks
    boolean existsByAccountNumber(String accountNumber);
    
    // User-based queries
    List<Account> findByUserId(Long userId);
    Page<Account> findByUserId(Long userId, Pageable pageable);
    List<Account> findByUserIdAndStatus(Long userId, AccountStatus status);
    List<Account> findByUserIdAndType(Long userId, AccountType type);
    
    // Branch-based queries
    List<Account> findByBranchId(Long branchId);
    Page<Account> findByBranchId(Long branchId, Pageable pageable);
    List<Account> findByBranchIdAndStatus(Long branchId, AccountStatus status);
    List<Account> findByBranchIdAndType(Long branchId, AccountType type);
    
    // Type-based queries
    List<Account> findByType(AccountType type);
    Page<Account> findByType(AccountType type, Pageable pageable);
    List<Account> findByTypeAndStatus(AccountType type, AccountStatus status);
    
    // Status-based queries
    List<Account> findByStatus(AccountStatus status);
    Page<Account> findByStatus(AccountStatus status, Pageable pageable);
    List<Account> findByStatusIn(List<AccountStatus> statuses);
    
    // Balance-based queries
    @Query("SELECT a FROM Account a WHERE a.balance >= :minBalance")
    List<Account> findByMinimumBalance(@Param("minBalance") BigDecimal minBalance);
    
    @Query("SELECT a FROM Account a WHERE a.balance <= :maxBalance")
    List<Account> findByMaximumBalance(@Param("maxBalance") BigDecimal maxBalance);
    
    @Query("SELECT a FROM Account a WHERE a.balance BETWEEN :minBalance AND :maxBalance")
    List<Account> findByBalanceRange(
            @Param("minBalance") BigDecimal minBalance,
            @Param("maxBalance") BigDecimal maxBalance);
    
    @Query("SELECT a FROM Account a WHERE a.availableBalance >= :minAvailableBalance")
    List<Account> findByMinimumAvailableBalance(@Param("minAvailableBalance") BigDecimal minAvailableBalance);
    
    // Date-based queries
    List<Account> findByOpeningDate(LocalDate openingDate);
    List<Account> findByOpeningDateBetween(LocalDate startDate, LocalDate endDate);
    List<Account> findByMaturityDate(LocalDate maturityDate);
    List<Account> findByMaturityDateBetween(LocalDate startDate, LocalDate endDate);
    
    // Search queries
    @Query("SELECT a FROM Account a WHERE " +
           "a.accountNumber LIKE %:searchTerm% OR " +
           "a.user.firstName LIKE %:searchTerm% OR " +
           "a.user.lastName LIKE %:searchTerm% OR " +
           "a.user.email LIKE %:searchTerm%")
    Page<Account> searchAccounts(@Param("searchTerm") String searchTerm, Pageable pageable);
    
    // Complex queries
    @Query("SELECT a FROM Account a WHERE " +
           "a.user.id = :userId AND " +
           "a.type = :type AND " +
           "a.status = :status")
    List<Account> findByUserTypeAndStatus(
            @Param("userId") Long userId,
            @Param("type") AccountType type,
            @Param("status") AccountStatus status);
    
    @Query("SELECT a FROM Account a WHERE " +
           "a.branch.id = :branchId AND " +
           "a.type = :type AND " +
           "a.status = :status")
    List<Account> findByBranchTypeAndStatus(
            @Param("branchId") Long branchId,
            @Param("type") AccountType type,
            @Param("status") AccountStatus status);
    
    // Joint account queries
    List<Account> findByIsJointAccount(Boolean isJointAccount);
    List<Account> findByIsJointAccountAndStatus(Boolean isJointAccount, AccountStatus status);
    
    // KYC status queries
    @Query("SELECT a FROM Account a WHERE a.kycStatus = :kycStatus")
    List<Account> findByKycStatus(@Param("kycStatus") String kycStatus);
    
    @Query("SELECT a FROM Account a WHERE a.kycStatus = 'PENDING'")
    List<Account> findPendingKycAccounts();
    
    // Risk-based queries
    @Query("SELECT a FROM Account a WHERE a.riskLevel = :riskLevel")
    List<Account> findByRiskLevel(@Param("riskLevel") String riskLevel);
    
    // Dormant account queries
    @Query("SELECT a FROM Account a WHERE a.status = 'DORMANT'")
    List<Account> findDormantAccounts();
    
    @Query("SELECT a FROM Account a WHERE a.dormantDate IS NOT NULL")
    List<Account> findAccountsWithDormantDate();
    
    // Statistics queries
    @Query("SELECT COUNT(a) FROM Account a WHERE a.status = :status")
    Long countByStatus(@Param("status") AccountStatus status);
    
    @Query("SELECT COUNT(a) FROM Account a WHERE a.type = :type")
    Long countByType(@Param("type") AccountType type);
    
    @Query("SELECT COUNT(a) FROM Account a WHERE a.user.id = :userId")
    Long countByUserId(@Param("userId") Long userId);
    
    @Query("SELECT COUNT(a) FROM Account a WHERE a.branch.id = :branchId")
    Long countByBranchId(@Param("branchId") Long branchId);
    
    @Query("SELECT COUNT(a) FROM Account a WHERE a.openingDate >= :startDate")
    Long countNewAccountsSince(@Param("startDate") LocalDate startDate);
    
    // Balance statistics
    @Query("SELECT SUM(a.balance) FROM Account a WHERE a.status = :status")
    BigDecimal getTotalBalanceByStatus(@Param("status") AccountStatus status);
    
    @Query("SELECT SUM(a.balance) FROM Account a WHERE a.type = :type")
    BigDecimal getTotalBalanceByType(@Param("type") AccountType type);
    
    @Query("SELECT SUM(a.balance) FROM Account a WHERE a.branch.id = :branchId")
    BigDecimal getTotalBalanceByBranch(@Param("branchId") Long branchId);
    
    @Query("SELECT AVG(a.balance) FROM Account a WHERE a.status = :status")
    BigDecimal getAverageBalanceByStatus(@Param("status") AccountStatus status);
    
    // Reporting queries
    @Query("SELECT a.type, COUNT(a) FROM Account a GROUP BY a.type")
    List<Object[]> getAccountCountByType();
    
    @Query("SELECT a.status, COUNT(a) FROM Account a GROUP BY a.status")
    List<Object[]> getAccountCountByStatus();
    
    @Query("SELECT a.riskLevel, COUNT(a) FROM Account a GROUP BY a.riskLevel")
    List<Object[]> getAccountCountByRiskLevel();
    
    @Query("SELECT a.kycStatus, COUNT(a) FROM Account a GROUP BY a.kycStatus")
    List<Object[]> getAccountCountByKycStatus();
    
    // Performance queries
    @Query("SELECT a FROM Account a WHERE a.lastTransactionDate IS NULL OR a.lastTransactionDate < :date")
    List<Account> findInactiveAccounts(@Param("date") java.time.LocalDateTime date);
    
    @Query("SELECT a FROM Account a WHERE a.balance < a.minimumBalance AND a.status = 'ACTIVE'")
    List<Account> findAccountsBelowMinimumBalance();
    
    // Top accounts queries
    @Query("SELECT a FROM Account a ORDER BY a.balance DESC")
    Page<Account> findTopAccountsByBalance(Pageable pageable);
    
    @Query("SELECT a FROM Account a WHERE a.status = :status ORDER BY a.balance DESC")
    Page<Account> findTopAccountsByBalanceAndStatus(
            @Param("status") AccountStatus status, 
            Pageable pageable);
    
    // Account features queries
    @Query("SELECT a FROM Account a WHERE a.accountFeatures LIKE %:feature%")
    List<Account> findByAccountFeature(@Param("feature") String feature);
    
    // Restrictions queries
    @Query("SELECT a FROM Account a WHERE a.restrictions IS NOT NULL AND a.restrictions != ''")
    List<Account> findAccountsWithRestrictions();
    
    // Freeze queries
    @Query("SELECT a FROM Account a WHERE a.status = 'FROZEN'")
    List<Account> findFrozenAccounts();
    
    @Query("SELECT a FROM Account a WHERE a.freezeDate IS NOT NULL")
    List<Account> findAccountsWithFreezeDate();
} 