package org.example.abcbanking.service.impl;

import org.example.abcbanking.model.*;
import org.example.abcbanking.repository.CardRepository;
import org.example.abcbanking.repository.AccountRepository;
import org.example.abcbanking.service.CardService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.Random;

@Service
@Transactional
public class CardServiceImpl implements CardService {
    
    private static final Logger logger = LoggerFactory.getLogger(CardServiceImpl.class);
    
    @Autowired
    private CardRepository cardRepository;
    
    @Autowired
    private AccountRepository accountRepository;
    
    @Autowired
    private PasswordEncoder passwordEncoder;
    
    @Override
    public Card createCard(Card card) {
        logger.info("Creating new card for account: {}", card.getAccount().getId());
        
        // Generate card number if not provided
        if (card.getCardNumber() == null || card.getCardNumber().isEmpty()) {
            card.setCardNumber(generateCardNumber(card.getCardType()));
        }
        
        // Set default values
        card.setStatus(CardStatus.PENDING_ACTIVATION);
        card.setActivationDate(LocalDate.now());
        card.setIsBlocked(false);
        card.setPinAttempts(0);
        
        // Set default limits based on card type
        setDefaultLimits(card);
        
        Card savedCard = cardRepository.save(card);
        logger.info("Card created with ID: {} and number: {}", savedCard.getId(), maskCardNumber(savedCard.getCardNumber()));
        
        return savedCard;
    }
    
    @Override
    public Card updateCard(Long id, Card card) {
        logger.info("Updating card with ID: {}", id);
        
        Card existingCard = cardRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Card not found with ID: " + id));
        
        // Update allowed fields
        existingCard.setCardHolderName(card.getCardHolderName());
        existingCard.setDailyLimit(card.getDailyLimit());
        existingCard.setMonthlyLimit(card.getMonthlyLimit());
        existingCard.setContactlessEnabled(card.getContactlessEnabled());
        existingCard.setOnlineTransactionsEnabled(card.getOnlineTransactionsEnabled());
        existingCard.setInternationalTransactionsEnabled(card.getInternationalTransactionsEnabled());
        existingCard.setAtmTransactionsEnabled(card.getAtmTransactionsEnabled());
        existingCard.setPosTransactionsEnabled(card.getPosTransactionsEnabled());
        
        return cardRepository.save(existingCard);
    }
    
    @Override
    public void deleteCard(Long id) {
        logger.info("Deleting card with ID: {}", id);
        
        Card card = cardRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Card not found with ID: " + id));
        
        if (card.getStatus() == CardStatus.ACTIVE) {
            throw new RuntimeException("Cannot delete active card. Please deactivate first.");
        }
        
        card.setIsDeleted(true);
        card.setStatus(CardStatus.CANCELLED);
        cardRepository.save(card);
        
        logger.info("Card deleted successfully: {}", id);
    }
    
    @Override
    public Optional<Card> getCardById(Long id) {
        return cardRepository.findById(id);
    }
    
    @Override
    public Optional<Card> getCardByNumber(String cardNumber) {
        return cardRepository.findByCardNumber(cardNumber);
    }
    
    @Override
    public List<Card> getAllCards() {
        return cardRepository.findAll();
    }
    
    @Override
    public Page<Card> getAllCards(Pageable pageable) {
        return cardRepository.findAll(pageable);
    }
    
    @Override
    public List<Card> getCardsByUserId(Long userId) {
        return cardRepository.findByUserId(userId);
    }
    
    @Override
    public List<Card> getCardsByAccountId(Long accountId) {
        return cardRepository.findByAccountId(accountId);
    }
    
    @Override
    public Page<Card> getCardsByUserId(Long userId, Pageable pageable) {
        return cardRepository.findByUserIdAndNotDeleted(userId, pageable);
    }
    
    @Override
    public void blockCard(Long cardId, String reason, String blockedBy) {
        logger.info("Blocking card with ID: {}", cardId);
        
        Card card = cardRepository.findById(cardId)
                .orElseThrow(() -> new RuntimeException("Card not found with ID: " + cardId));
        
        card.blockCard(reason, blockedBy);
        cardRepository.save(card);
        
        logger.info("Card blocked successfully: {}", cardId);
    }
    
    @Override
    public void unblockCard(Long cardId) {
        logger.info("Unblocking card with ID: {}", cardId);
        
        Card card = cardRepository.findById(cardId)
                .orElseThrow(() -> new RuntimeException("Card not found with ID: " + cardId));
        
        card.unblockCard();
        cardRepository.save(card);
        
        logger.info("Card unblocked successfully: {}", cardId);
    }
    
    @Override
    public void activateCard(Long cardId, String pin) {
        logger.info("Activating card with ID: {}", cardId);
        
        Card card = cardRepository.findById(cardId)
                .orElseThrow(() -> new RuntimeException("Card not found with ID: " + cardId));
        
        if (card.getStatus() != CardStatus.PENDING_ACTIVATION) {
            throw new RuntimeException("Card is not in pending activation status");
        }
        
        card.setStatus(CardStatus.ACTIVE);
        card.setActivationDate(LocalDate.now());
        card.setPinHash(passwordEncoder.encode(pin));
        
        cardRepository.save(card);
        logger.info("Card activated successfully: {}", cardId);
    }
    
    @Override
    public void deactivateCard(Long cardId) {
        logger.info("Deactivating card with ID: {}", cardId);
        
        Card card = cardRepository.findById(cardId)
                .orElseThrow(() -> new RuntimeException("Card not found with ID: " + cardId));
        
        card.setStatus(CardStatus.INACTIVE);
        cardRepository.save(card);
        
        logger.info("Card deactivated successfully: {}", cardId);
    }
    
    @Override
    public boolean validatePin(Long cardId, String pin) {
        Card card = cardRepository.findById(cardId)
                .orElseThrow(() -> new RuntimeException("Card not found with ID: " + cardId));
        
        if (card.isPinLocked()) {
            throw new RuntimeException("Card PIN is locked due to multiple failed attempts");
        }
        
        boolean isValid = passwordEncoder.matches(pin, card.getPinHash());
        
        if (!isValid) {
            card.setPinAttempts(card.getPinAttempts() + 1);
            if (card.getPinAttempts() >= 3) {
                card.setPinLockedUntil(LocalDateTime.now().plusHours(24));
            }
            cardRepository.save(card);
        } else {
            card.setPinAttempts(0);
            cardRepository.save(card);
        }
        
        return isValid;
    }
    
    @Override
    public void changePin(Long cardId, String oldPin, String newPin) {
        logger.info("Changing PIN for card with ID: {}", cardId);
        
        if (!validatePin(cardId, oldPin)) {
            throw new RuntimeException("Invalid current PIN");
        }
        
        Card card = cardRepository.findById(cardId)
                .orElseThrow(() -> new RuntimeException("Card not found with ID: " + cardId));
        
        card.setPinHash(passwordEncoder.encode(newPin));
        card.setPinAttempts(0);
        card.setPinLockedUntil(null);
        
        cardRepository.save(card);
        logger.info("PIN changed successfully for card: {}", cardId);
    }
    
    @Override
    public void resetPin(Long cardId, String newPin) {
        logger.info("Resetting PIN for card with ID: {}", cardId);
        
        Card card = cardRepository.findById(cardId)
                .orElseThrow(() -> new RuntimeException("Card not found with ID: " + cardId));
        
        card.setPinHash(passwordEncoder.encode(newPin));
        card.setPinAttempts(0);
        card.setPinLockedUntil(null);
        
        cardRepository.save(card);
        logger.info("PIN reset successfully for card: {}", cardId);
    }
    
    @Override
    public void lockPin(Long cardId) {
        Card card = cardRepository.findById(cardId)
                .orElseThrow(() -> new RuntimeException("Card not found with ID: " + cardId));
        
        card.setPinLockedUntil(LocalDateTime.now().plusHours(24));
        cardRepository.save(card);
    }
    
    @Override
    public void unlockPin(Long cardId) {
        Card card = cardRepository.findById(cardId)
                .orElseThrow(() -> new RuntimeException("Card not found with ID: " + cardId));
        
        card.setPinLockedUntil(null);
        card.setPinAttempts(0);
        cardRepository.save(card);
    }
    
    @Override
    public void setDailyLimit(Long cardId, BigDecimal limit) {
        Card card = cardRepository.findById(cardId)
                .orElseThrow(() -> new RuntimeException("Card not found with ID: " + cardId));
        
        card.setDailyLimit(limit);
        cardRepository.save(card);
    }
    
    @Override
    public void setMonthlyLimit(Long cardId, BigDecimal limit) {
        Card card = cardRepository.findById(cardId)
                .orElseThrow(() -> new RuntimeException("Card not found with ID: " + cardId));
        
        card.setMonthlyLimit(limit);
        cardRepository.save(card);
    }
    
    @Override
    public void setCreditLimit(Long cardId, BigDecimal limit) {
        Card card = cardRepository.findById(cardId)
                .orElseThrow(() -> new RuntimeException("Card not found with ID: " + cardId));
        
        if (card.getCardType() != CardType.CREDIT && card.getCardType() != CardType.BUSINESS_CREDIT) {
            throw new RuntimeException("Credit limit can only be set for credit cards");
        }
        
        card.setCreditLimit(limit);
        card.setAvailableCredit(limit.subtract(card.getOutstandingBalance()));
        cardRepository.save(card);
    }
    
    @Override
    public void enableContactless(Long cardId) {
        updateCardFeature(cardId, "contactlessEnabled", true);
    }
    
    @Override
    public void disableContactless(Long cardId) {
        updateCardFeature(cardId, "contactlessEnabled", false);
    }
    
    @Override
    public void enableOnlineTransactions(Long cardId) {
        updateCardFeature(cardId, "onlineTransactionsEnabled", true);
    }
    
    @Override
    public void disableOnlineTransactions(Long cardId) {
        updateCardFeature(cardId, "onlineTransactionsEnabled", false);
    }
    
    @Override
    public void enableInternationalTransactions(Long cardId) {
        updateCardFeature(cardId, "internationalTransactionsEnabled", true);
    }
    
    @Override
    public void disableInternationalTransactions(Long cardId) {
        updateCardFeature(cardId, "internationalTransactionsEnabled", false);
    }
    
    @Override
    public Card requestReplacement(Long cardId, String reason) {
        logger.info("Requesting replacement for card with ID: {}", cardId);
        
        Card oldCard = cardRepository.findById(cardId)
                .orElseThrow(() -> new RuntimeException("Card not found with ID: " + cardId));
        
        // Create new card
        Card newCard = new Card();
        newCard.setCardHolderName(oldCard.getCardHolderName());
        newCard.setCardType(oldCard.getCardType());
        newCard.setExpiryDate(LocalDate.now().plusYears(3));
        newCard.setAccount(oldCard.getAccount());
        newCard.setReplacementReason(reason);
        newCard.setReplacedCardId(oldCard.getId());
        
        // Deactivate old card
        oldCard.setStatus(CardStatus.REPLACED);
        oldCard.setReplacementReason(reason);
        
        cardRepository.save(oldCard);
        Card savedNewCard = createCard(newCard);
        
        logger.info("Replacement card created with ID: {}", savedNewCard.getId());
        return savedNewCard;
    }
    
    @Override
    public void reportLostOrStolen(Long cardId, String reason) {
        logger.info("Reporting card as lost/stolen with ID: {}", cardId);
        
        Card card = cardRepository.findById(cardId)
                .orElseThrow(() -> new RuntimeException("Card not found with ID: " + cardId));
        
        card.setStatus(reason.toLowerCase().contains("lost") ? CardStatus.LOST : CardStatus.STOLEN);
        card.setIsBlocked(true);
        card.setBlockReason(reason);
        card.setBlockedDate(LocalDateTime.now());
        
        cardRepository.save(card);
        logger.info("Card reported as {}: {}", card.getStatus(), cardId);
    }
    
    @Override
    public List<Card> getExpiringCards(int daysFromNow) {
        LocalDate expiryDate = LocalDate.now().plusDays(daysFromNow);
        return cardRepository.findExpiringCards(expiryDate);
    }
    
    @Override
    public Long countActiveCardsByUserId(Long userId) {
        return cardRepository.findByUserIdAndStatus(userId, CardStatus.ACTIVE).stream().count();
    }
    
    @Override
    public Long countCardsByType(CardType cardType) {
        return cardRepository.countActiveCardsByType(cardType);
    }
    
    @Override
    public List<Card> getCardsByStatus(CardStatus status) {
        return cardRepository.findByStatus(status);
    }
    
    @Override
    public String generateCardNumber(CardType cardType) {
        String prefix = getCardPrefix(cardType);
        Random random = new Random();
        StringBuilder cardNumber = new StringBuilder(prefix);
        
        // Generate remaining digits
        for (int i = prefix.length(); i < 16; i++) {
            cardNumber.append(random.nextInt(10));
        }
        
        return cardNumber.toString();
    }
    
    @Override
    public String maskCardNumber(String cardNumber) {
        if (cardNumber == null || cardNumber.length() < 4) {
            return cardNumber;
        }
        return cardNumber.substring(0, 4) + " **** **** " + cardNumber.substring(cardNumber.length() - 4);
    }
    
    @Override
    public boolean isCardExpired(Long cardId) {
        Card card = cardRepository.findById(cardId)
                .orElseThrow(() -> new RuntimeException("Card not found with ID: " + cardId));
        return card.isExpired();
    }
    
    @Override
    public boolean isCardBlocked(Long cardId) {
        Card card = cardRepository.findById(cardId)
                .orElseThrow(() -> new RuntimeException("Card not found with ID: " + cardId));
        return card.getIsBlocked();
    }
    
    @Override
    public void addRewardsPoints(Long cardId, Long points) {
        Card card = cardRepository.findById(cardId)
                .orElseThrow(() -> new RuntimeException("Card not found with ID: " + cardId));
        
        card.setRewardsPoints(card.getRewardsPoints() + points);
        cardRepository.save(card);
    }
    
    @Override
    public void redeemRewardsPoints(Long cardId, Long points) {
        Card card = cardRepository.findById(cardId)
                .orElseThrow(() -> new RuntimeException("Card not found with ID: " + cardId));
        
        if (card.getRewardsPoints() < points) {
            throw new RuntimeException("Insufficient rewards points");
        }
        
        card.setRewardsPoints(card.getRewardsPoints() - points);
        cardRepository.save(card);
    }
    
    @Override
    public void addCashback(Long cardId, BigDecimal amount) {
        Card card = cardRepository.findById(cardId)
                .orElseThrow(() -> new RuntimeException("Card not found with ID: " + cardId));
        
        card.setCashbackEarned(card.getCashbackEarned().add(amount));
        cardRepository.save(card);
    }
    
    // Helper methods
    private void setDefaultLimits(Card card) {
        switch (card.getCardType()) {
            case DEBIT:
                card.setDailyLimit(new BigDecimal("50000"));
                card.setMonthlyLimit(new BigDecimal("200000"));
                break;
            case CREDIT:
                card.setDailyLimit(new BigDecimal("100000"));
                card.setMonthlyLimit(new BigDecimal("500000"));
                card.setCreditLimit(new BigDecimal("100000"));
                card.setAvailableCredit(new BigDecimal("100000"));
                break;
            case BUSINESS_DEBIT:
                card.setDailyLimit(new BigDecimal("200000"));
                card.setMonthlyLimit(new BigDecimal("1000000"));
                break;
            case BUSINESS_CREDIT:
                card.setDailyLimit(new BigDecimal("500000"));
                card.setMonthlyLimit(new BigDecimal("2000000"));
                card.setCreditLimit(new BigDecimal("500000"));
                card.setAvailableCredit(new BigDecimal("500000"));
                break;
            default:
                card.setDailyLimit(new BigDecimal("25000"));
                card.setMonthlyLimit(new BigDecimal("100000"));
        }
    }
    
    private String getCardPrefix(CardType cardType) {
        switch (cardType) {
            case DEBIT:
            case BUSINESS_DEBIT:
                return "4532"; // Visa Debit
            case CREDIT:
            case BUSINESS_CREDIT:
                return "5555"; // Mastercard Credit
            case PREPAID:
                return "4111"; // Visa Prepaid
            default:
                return "4000";
        }
    }
    
    private void updateCardFeature(Long cardId, String feature, boolean enabled) {
        Card card = cardRepository.findById(cardId)
                .orElseThrow(() -> new RuntimeException("Card not found with ID: " + cardId));
        
        switch (feature) {
            case "contactlessEnabled":
                card.setContactlessEnabled(enabled);
                break;
            case "onlineTransactionsEnabled":
                card.setOnlineTransactionsEnabled(enabled);
                break;
            case "internationalTransactionsEnabled":
                card.setInternationalTransactionsEnabled(enabled);
                break;
            case "atmTransactionsEnabled":
                card.setAtmTransactionsEnabled(enabled);
                break;
            case "posTransactionsEnabled":
                card.setPosTransactionsEnabled(enabled);
                break;
        }
        
        cardRepository.save(card);
    }
}
