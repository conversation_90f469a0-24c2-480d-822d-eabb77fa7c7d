package org.example.abcbanking.service.impl;

import org.example.abcbanking.model.Account;
import org.example.abcbanking.model.Transaction;
import org.example.abcbanking.model.TransactionStatus;
import org.example.abcbanking.model.TransactionType;
import org.example.abcbanking.repository.AccountRepository;
import org.example.abcbanking.repository.TransactionRepository;
import org.example.abcbanking.service.TransactionService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Service
@Transactional
public class TransactionServiceImpl implements TransactionService {

    private static final Logger logger = LoggerFactory.getLogger(TransactionServiceImpl.class);

    @Autowired
    private TransactionRepository transactionRepository;

    @Autowired
    private AccountRepository accountRepository;

    @Override
    public Transaction createTransaction(Transaction transaction) {
        logger.info("Creating new transaction: {}", transaction.getTransactionId());
        
        // Generate transaction ID if not provided
        if (transaction.getTransactionId() == null || transaction.getTransactionId().isEmpty()) {
            transaction.setTransactionId(generateTransactionId());
        }
        
        // Set default values
        if (transaction.getTransactionDate() == null) {
            transaction.setTransactionDate(LocalDateTime.now());
        }
        
        if (transaction.getStatus() == null) {
            transaction.setStatus(TransactionStatus.PENDING);
        }
        
        // Calculate total amount including fees and taxes
        calculateTotalAmount(transaction);
        
        Transaction savedTransaction = transactionRepository.save(transaction);
        logger.info("Transaction created successfully: {}", savedTransaction.getTransactionId());
        
        return savedTransaction;
    }

    @Override
    @Transactional
    public Transaction processTransfer(Long fromAccountId, Long toAccountId, BigDecimal amount, String description) {
        logger.info("Processing transfer from account {} to account {} for amount: {}", 
                   fromAccountId, toAccountId, amount);

        // Validate accounts
        Account fromAccount = accountRepository.findById(fromAccountId)
                .orElseThrow(() -> new RuntimeException("Source account not found"));
        
        Account toAccount = accountRepository.findById(toAccountId)
                .orElseThrow(() -> new RuntimeException("Destination account not found"));

        // Validate account status
        if (!fromAccount.isActive()) {
            throw new RuntimeException("Source account is not active");
        }
        
        if (!toAccount.isActive()) {
            throw new RuntimeException("Destination account is not active");
        }

        // Check sufficient balance
        if (!fromAccount.hasSufficientBalance(amount)) {
            throw new RuntimeException("Insufficient balance in source account");
        }

        // Create transaction record
        Transaction transaction = new Transaction();
        transaction.setTransactionId(generateTransactionId());
        transaction.setType(TransactionType.TRANSFER);
        transaction.setAmount(amount);
        transaction.setFromAccount(fromAccount);
        transaction.setToAccount(toAccount);
        transaction.setDescription(description);
        transaction.setTransactionDate(LocalDateTime.now());
        transaction.setStatus(TransactionStatus.PENDING);

        // Calculate fees (simple fee structure)
        BigDecimal transactionFee = calculateTransactionFee(amount, TransactionType.TRANSFER);
        transaction.setTransactionFee(transactionFee);
        transaction.setTotalAmount(amount.add(transactionFee));

        try {
            // Debit from source account
            fromAccount.debit(amount.add(transactionFee));
            fromAccount.setLastTransactionDate(LocalDateTime.now());
            accountRepository.save(fromAccount);

            // Credit to destination account
            toAccount.credit(amount);
            toAccount.setLastTransactionDate(LocalDateTime.now());
            accountRepository.save(toAccount);

            // Mark transaction as completed
            transaction.setStatus(TransactionStatus.COMPLETED);
            transaction.setCompletedAt(LocalDateTime.now());

            Transaction savedTransaction = transactionRepository.save(transaction);
            logger.info("Transfer completed successfully: {}", savedTransaction.getTransactionId());

            return savedTransaction;

        } catch (Exception e) {
            logger.error("Transfer failed: {}", transaction.getTransactionId(), e);
            transaction.setStatus(TransactionStatus.FAILED);
            transaction.setFailureReason(e.getMessage());
            transactionRepository.save(transaction);
            throw new RuntimeException("Transfer failed: " + e.getMessage());
        }
    }

    @Override
    public Optional<Transaction> getTransactionById(Long id) {
        return transactionRepository.findById(id);
    }

    @Override
    public Optional<Transaction> getTransactionByTransactionId(String transactionId) {
        return transactionRepository.findByTransactionId(transactionId);
    }

    @Override
    public List<Transaction> getAllTransactions() {
        return transactionRepository.findAll();
    }

    @Override
    public Page<Transaction> getAllTransactions(Pageable pageable) {
        return transactionRepository.findAll(pageable);
    }

    @Override
    public List<Transaction> getTransactionsByAccountId(Long accountId) {
        return transactionRepository.findByFromAccountIdOrToAccountId(accountId, accountId);
    }

    @Override
    public List<Transaction> getTransactionsByType(TransactionType type) {
        return transactionRepository.findByType(type);
    }

    @Override
    public List<Transaction> getTransactionsByStatus(TransactionStatus status) {
        return transactionRepository.findByStatus(status);
    }

    @Override
    public List<Transaction> getTransactionsByDateRange(LocalDateTime start, LocalDateTime end) {
        return transactionRepository.findByTransactionDateBetween(start, end);
    }

    @Override
    public Page<Transaction> searchTransactions(String searchTerm, Pageable pageable) {
        return transactionRepository.findByTransactionIdContainingOrDescriptionContaining(
                searchTerm, searchTerm, pageable);
    }

    @Override
    public void markTransactionAsCompleted(Long id) {
        Transaction transaction = transactionRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Transaction not found"));
        
        transaction.setStatus(TransactionStatus.COMPLETED);
        transaction.setCompletedAt(LocalDateTime.now());
        transactionRepository.save(transaction);
        
        logger.info("Transaction marked as completed: {}", transaction.getTransactionId());
    }

    @Override
    public void markTransactionAsFailed(Long id, String reason) {
        Transaction transaction = transactionRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Transaction not found"));
        
        transaction.setStatus(TransactionStatus.FAILED);
        transaction.setFailureReason(reason);
        transactionRepository.save(transaction);
        
        logger.info("Transaction marked as failed: {}", transaction.getTransactionId());
    }

    @Override
    public void markTransactionAsSuspicious(Long id, String reason) {
        Transaction transaction = transactionRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Transaction not found"));
        
        transaction.setStatus(TransactionStatus.SUSPICIOUS);
        transaction.setFailureReason(reason);
        transactionRepository.save(transaction);
        
        logger.warn("Transaction marked as suspicious: {}", transaction.getTransactionId());
    }

    @Override
    @Transactional
    public void reverseTransaction(Long id, String reason) {
        Transaction originalTransaction = transactionRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Transaction not found"));
        
        if (originalTransaction.getStatus() != TransactionStatus.COMPLETED) {
            throw new RuntimeException("Can only reverse completed transactions");
        }

        // Create reversal transaction
        Transaction reversalTransaction = new Transaction();
        reversalTransaction.setTransactionId(generateTransactionId());
        reversalTransaction.setType(TransactionType.REVERSAL);
        reversalTransaction.setAmount(originalTransaction.getAmount());
        reversalTransaction.setFromAccount(originalTransaction.getToAccount());
        reversalTransaction.setToAccount(originalTransaction.getFromAccount());
        reversalTransaction.setDescription("Reversal of transaction: " + originalTransaction.getTransactionId() + " - " + reason);
        reversalTransaction.setTransactionDate(LocalDateTime.now());
        reversalTransaction.setStatus(TransactionStatus.COMPLETED);
        reversalTransaction.setCompletedAt(LocalDateTime.now());
        reversalTransaction.setReversalReason(reason);

        // Process the reversal
        if (originalTransaction.getFromAccount() != null) {
            originalTransaction.getFromAccount().credit(originalTransaction.getAmount());
            accountRepository.save(originalTransaction.getFromAccount());
        }
        
        if (originalTransaction.getToAccount() != null) {
            originalTransaction.getToAccount().debit(originalTransaction.getAmount());
            accountRepository.save(originalTransaction.getToAccount());
        }

        // Mark original transaction as reversed
        originalTransaction.setStatus(TransactionStatus.REVERSED);
        originalTransaction.setReversalReason(reason);
        
        transactionRepository.save(originalTransaction);
        transactionRepository.save(reversalTransaction);
        
        logger.info("Transaction reversed successfully: {}", originalTransaction.getTransactionId());
    }

    @Override
    public BigDecimal getTotalAmountByAccountId(Long accountId) {
        return transactionRepository.getTotalAmountByAccountId(accountId);
    }

    private String generateTransactionId() {
        return "TXN" + UUID.randomUUID().toString().replace("-", "").substring(0, 12).toUpperCase();
    }

    private void calculateTotalAmount(Transaction transaction) {
        BigDecimal amount = transaction.getAmount();
        BigDecimal fee = transaction.getTransactionFee() != null ? 
                transaction.getTransactionFee() : BigDecimal.ZERO;
        BigDecimal tax = transaction.getTaxAmount() != null ? 
                transaction.getTaxAmount() : BigDecimal.ZERO;
        
        transaction.setTotalAmount(amount.add(fee).add(tax));
    }

    private BigDecimal calculateTransactionFee(BigDecimal amount, TransactionType type) {
        // Simple fee calculation - can be made more sophisticated
        switch (type) {
            case TRANSFER:
                if (amount.compareTo(BigDecimal.valueOf(10000)) > 0) {
                    return BigDecimal.valueOf(10); // Rs. 10 for transfers above 10,000
                }
                return BigDecimal.ZERO;
            case WITHDRAWAL:
                return BigDecimal.valueOf(5); // Rs. 5 for withdrawals
            default:
                return BigDecimal.ZERO;
        }
    }
}
