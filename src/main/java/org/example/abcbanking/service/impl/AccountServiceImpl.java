package org.example.abcbanking.service.impl;

import org.example.abcbanking.model.Account;
import org.example.abcbanking.model.AccountStatus;
import org.example.abcbanking.model.AccountType;
import org.example.abcbanking.model.Transaction;
import org.example.abcbanking.model.TransactionType;
import org.example.abcbanking.repository.AccountRepository;
import org.example.abcbanking.repository.TransactionRepository;
import org.example.abcbanking.service.AccountService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Service
@Transactional
public class AccountServiceImpl implements AccountService {

    private static final Logger logger = LoggerFactory.getLogger(AccountServiceImpl.class);

    @Autowired
    private AccountRepository accountRepository;

    @Autowired
    private TransactionRepository transactionRepository;

    @Override
    public Account createAccount(Account account) {
        logger.info("Creating new account for user: {}", account.getUser().getId());
        
        // Generate unique account number
        account.setAccountNumber(generateAccountNumber());
        account.setStatus(AccountStatus.ACTIVE);
        account.setCreatedAt(LocalDateTime.now());
        account.setUpdatedAt(LocalDateTime.now());
        
        // Set default values
        if (account.getBalance() == null) {
            account.setBalance(BigDecimal.ZERO);
        }
        if (account.getAvailableBalance() == null) {
            account.setAvailableBalance(account.getBalance());
        }
        if (account.getMinimumBalance() == null) {
            account.setMinimumBalance(BigDecimal.valueOf(1000)); // Default minimum balance
        }
        
        Account savedAccount = accountRepository.save(account);
        logger.info("Account created successfully with number: {}", savedAccount.getAccountNumber());
        
        return savedAccount;
    }

    @Override
    public Account updateAccount(Long id, Account account) {
        logger.info("Updating account with ID: {}", id);
        
        Account existingAccount = accountRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Account not found with ID: " + id));
        
        // Update allowed fields
        existingAccount.setAccountHolderName(account.getAccountHolderName());
        existingAccount.setContactNumber(account.getContactNumber());
        existingAccount.setEmailAddress(account.getEmailAddress());
        existingAccount.setAddress(account.getAddress());
        existingAccount.setUpdatedAt(LocalDateTime.now());
        
        return accountRepository.save(existingAccount);
    }

    @Override
    public void closeAccount(Long id, String reason) {
        logger.info("Closing account with ID: {}", id);
        
        Account account = accountRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Account not found with ID: " + id));
        
        if (account.getBalance().compareTo(BigDecimal.ZERO) != 0) {
            throw new RuntimeException("Cannot close account with non-zero balance");
        }
        
        account.setStatus(AccountStatus.CLOSED);
        account.setClosureReason(reason);
        account.setClosureDate(LocalDateTime.now());
        account.setUpdatedAt(LocalDateTime.now());
        
        accountRepository.save(account);
        logger.info("Account closed successfully: {}", account.getAccountNumber());
    }

    @Override
    public void freezeAccount(Long id, String reason) {
        logger.info("Freezing account with ID: {}", id);
        
        Account account = accountRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Account not found with ID: " + id));
        
        account.setStatus(AccountStatus.FROZEN);
        account.setFreezeReason(reason);
        account.setFreezeDate(LocalDateTime.now());
        account.setUpdatedAt(LocalDateTime.now());
        
        accountRepository.save(account);
        logger.info("Account frozen successfully: {}", account.getAccountNumber());
    }

    @Override
    public void unfreezeAccount(Long id) {
        logger.info("Unfreezing account with ID: {}", id);
        
        Account account = accountRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Account not found with ID: " + id));
        
        account.setStatus(AccountStatus.ACTIVE);
        account.setFreezeReason(null);
        account.setFreezeDate(null);
        account.setUpdatedAt(LocalDateTime.now());
        
        accountRepository.save(account);
        logger.info("Account unfrozen successfully: {}", account.getAccountNumber());
    }

    @Override
    public Optional<Account> getAccountById(Long id) {
        return accountRepository.findById(id);
    }

    @Override
    public Optional<Account> getAccountByNumber(String accountNumber) {
        return accountRepository.findByAccountNumber(accountNumber);
    }

    @Override
    public List<Account> getAllAccounts() {
        return accountRepository.findAll();
    }

    @Override
    public Page<Account> getAllAccounts(Pageable pageable) {
        return accountRepository.findAll(pageable);
    }

    @Override
    public List<Account> getAccountsByUserId(Long userId) {
        return accountRepository.findByUserId(userId);
    }

    @Override
    public List<Account> getAccountsByBranchId(Long branchId) {
        return accountRepository.findByBranchId(branchId);
    }

    @Override
    public List<Account> getAccountsByType(AccountType type) {
        return accountRepository.findByType(type);
    }

    @Override
    public List<Account> getAccountsByStatus(AccountStatus status) {
        return accountRepository.findByStatus(status);
    }

    @Override
    public BigDecimal getAccountBalance(Long id) {
        Account account = accountRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Account not found with ID: " + id));
        return account.getBalance();
    }

    @Override
    @Transactional
    public void creditAccount(Long id, BigDecimal amount, String description) {
        logger.info("Crediting account {} with amount: {}", id, amount);
        
        Account account = accountRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Account not found with ID: " + id));
        
        if (!account.isActive()) {
            throw new RuntimeException("Cannot credit inactive account");
        }
        
        // Update account balance
        account.credit(amount);
        account.setUpdatedAt(LocalDateTime.now());
        accountRepository.save(account);
        
        // Create transaction record
        Transaction transaction = new Transaction();
        transaction.setTransactionId(generateTransactionId());
        transaction.setType(TransactionType.CREDIT);
        transaction.setAmount(amount);
        transaction.setToAccount(account);
        transaction.setDescription(description);
        transaction.setTransactionDate(LocalDateTime.now());
        transaction.setStatus(org.example.abcbanking.model.TransactionStatus.COMPLETED);
        
        transactionRepository.save(transaction);
        
        logger.info("Account credited successfully. New balance: {}", account.getBalance());
    }

    @Override
    @Transactional
    public void debitAccount(Long id, BigDecimal amount, String description) {
        logger.info("Debiting account {} with amount: {}", id, amount);
        
        Account account = accountRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Account not found with ID: " + id));
        
        if (!account.isActive()) {
            throw new RuntimeException("Cannot debit inactive account");
        }
        
        if (!account.hasSufficientBalance(amount)) {
            throw new RuntimeException("Insufficient balance for debit operation");
        }
        
        // Update account balance
        account.debit(amount);
        account.setUpdatedAt(LocalDateTime.now());
        accountRepository.save(account);
        
        // Create transaction record
        Transaction transaction = new Transaction();
        transaction.setTransactionId(generateTransactionId());
        transaction.setType(TransactionType.DEBIT);
        transaction.setAmount(amount);
        transaction.setFromAccount(account);
        transaction.setDescription(description);
        transaction.setTransactionDate(LocalDateTime.now());
        transaction.setStatus(org.example.abcbanking.model.TransactionStatus.COMPLETED);
        
        transactionRepository.save(transaction);
        
        logger.info("Account debited successfully. New balance: {}", account.getBalance());
    }

    @Override
    public void setAccountStatus(Long id, AccountStatus status) {
        Account account = accountRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Account not found with ID: " + id));
        
        account.setStatus(status);
        account.setUpdatedAt(LocalDateTime.now());
        accountRepository.save(account);
        
        logger.info("Account status updated to: {}", status);
    }

    private String generateAccountNumber() {
        // Generate account number in format: ABC + 10 digits
        String prefix = "ABC";
        String timestamp = String.valueOf(System.currentTimeMillis()).substring(3);
        String random = String.valueOf((int)(Math.random() * 1000));
        return prefix + timestamp + String.format("%03d", Integer.parseInt(random));
    }

    private String generateTransactionId() {
        return "TXN" + UUID.randomUUID().toString().replace("-", "").substring(0, 12).toUpperCase();
    }
}
