package org.example.abcbanking.service.impl;

import org.example.abcbanking.model.*;
import org.example.abcbanking.repository.NotificationRepository;
import org.example.abcbanking.repository.UserRepository;
import org.example.abcbanking.service.NotificationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Service
@Transactional
public class NotificationServiceImpl implements NotificationService {
    
    private static final Logger logger = LoggerFactory.getLogger(NotificationServiceImpl.class);
    
    @Autowired
    private NotificationRepository notificationRepository;
    
    @Autowired
    private UserRepository userRepository;
    
    @Override
    public Notification createNotification(Notification notification) {
        logger.info("Creating notification for user: {}", notification.getUser().getId());
        
        notification.setStatus(NotificationStatus.PENDING);
        notification.setIsRead(false);
        notification.setDeliveryAttempts(0);
        
        Notification savedNotification = notificationRepository.save(notification);
        logger.info("Notification created with ID: {}", savedNotification.getId());
        
        return savedNotification;
    }
    
    @Override
    public Notification updateNotification(Long id, Notification notification) {
        logger.info("Updating notification with ID: {}", id);
        
        Notification existingNotification = notificationRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Notification not found with ID: " + id));
        
        existingNotification.setTitle(notification.getTitle());
        existingNotification.setMessage(notification.getMessage());
        existingNotification.setType(notification.getType());
        existingNotification.setPriority(notification.getPriority());
        
        return notificationRepository.save(existingNotification);
    }
    
    @Override
    public void deleteNotification(Long id) {
        logger.info("Deleting notification with ID: {}", id);
        
        Notification notification = notificationRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Notification not found with ID: " + id));
        
        notification.setIsDeleted(true);
        notificationRepository.save(notification);
        
        logger.info("Notification deleted successfully: {}", id);
    }
    
    @Override
    public Optional<Notification> getNotificationById(Long id) {
        return notificationRepository.findById(id);
    }
    
    @Override
    public List<Notification> getAllNotifications() {
        return notificationRepository.findAll();
    }
    
    @Override
    public Page<Notification> getAllNotifications(Pageable pageable) {
        return notificationRepository.findAll(pageable);
    }
    
    @Override
    public List<Notification> getNotificationsByUserId(Long userId) {
        return notificationRepository.findByUserId(userId);
    }
    
    @Override
    public Page<Notification> getNotificationsByUserId(Long userId, Pageable pageable) {
        return notificationRepository.findByUserId(userId, pageable);
    }
    
    @Override
    public List<Notification> getUnreadNotificationsByUserId(Long userId) {
        return notificationRepository.findUnreadNotificationsByUserId(userId);
    }
    
    @Override
    public Long countUnreadNotificationsByUserId(Long userId) {
        return notificationRepository.countUnreadNotificationsByUserId(userId);
    }
    
    @Override
    public void markAsRead(Long notificationId) {
        logger.info("Marking notification as read: {}", notificationId);
        notificationRepository.markAsRead(notificationId, LocalDateTime.now());
    }
    
    @Override
    public void markAllAsReadByUserId(Long userId) {
        logger.info("Marking all notifications as read for user: {}", userId);
        notificationRepository.markAllAsReadByUserId(userId, LocalDateTime.now());
    }
    
    @Override
    public void sendNotification(Long notificationId) {
        logger.info("Sending notification: {}", notificationId);
        
        Notification notification = notificationRepository.findById(notificationId)
                .orElseThrow(() -> new RuntimeException("Notification not found with ID: " + notificationId));
        
        try {
            // Here you would integrate with actual notification services (email, SMS, push)
            // For now, we'll just mark it as sent
            notification.markAsSent();
            notificationRepository.save(notification);
            
            logger.info("Notification sent successfully: {}", notificationId);
        } catch (Exception e) {
            logger.error("Failed to send notification: {}", notificationId, e);
            notification.markAsFailed("Failed to send: " + e.getMessage());
            notificationRepository.save(notification);
        }
    }
    
    @Override
    public void scheduleNotification(Notification notification) {
        logger.info("Scheduling notification for user: {}", notification.getUser().getId());
        
        notification.setStatus(NotificationStatus.PENDING);
        notification.setScheduledAt(LocalDateTime.now().plusMinutes(1)); // Schedule for 1 minute later
        
        notificationRepository.save(notification);
        logger.info("Notification scheduled successfully");
    }
    
    @Override
    public List<Notification> getNotificationsByType(NotificationType type) {
        return notificationRepository.findByType(type);
    }
    
    @Override
    public List<Notification> getNotificationsByStatus(NotificationStatus status) {
        return notificationRepository.findByStatus(status);
    }
    
    @Override
    public List<Notification> getNotificationsByUserIdAndType(Long userId, NotificationType type) {
        return notificationRepository.findByUserIdAndType(userId, type);
    }
    
    @Override
    public void sendTransactionAlert(Long userId, String transactionDetails) {
        logger.info("Sending transaction alert to user: {}", userId);
        
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("User not found with ID: " + userId));
        
        Notification notification = new Notification(
                "Transaction Alert",
                "Transaction completed: " + transactionDetails,
                NotificationType.TRANSACTION_ALERT,
                user
        );
        notification.setPriority(NotificationPriority.HIGH);
        notification.setCategory("TRANSACTION");
        
        Notification savedNotification = createNotification(notification);
        sendNotification(savedNotification.getId());
    }
    
    @Override
    public void sendSecurityAlert(Long userId, String securityMessage) {
        logger.info("Sending security alert to user: {}", userId);
        
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("User not found with ID: " + userId));
        
        Notification notification = new Notification(
                "Security Alert",
                securityMessage,
                NotificationType.SECURITY_ALERT,
                user
        );
        notification.setPriority(NotificationPriority.URGENT);
        notification.setCategory("SECURITY");
        
        Notification savedNotification = createNotification(notification);
        sendNotification(savedNotification.getId());
    }
    
    @Override
    public void sendAccountUpdate(Long userId, String updateMessage) {
        logger.info("Sending account update to user: {}", userId);
        
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("User not found with ID: " + userId));
        
        Notification notification = new Notification(
                "Account Update",
                updateMessage,
                NotificationType.ACCOUNT_UPDATE,
                user
        );
        notification.setPriority(NotificationPriority.MEDIUM);
        notification.setCategory("ACCOUNT");
        
        Notification savedNotification = createNotification(notification);
        sendNotification(savedNotification.getId());
    }
    
    @Override
    public void sendWelcomeNotification(Long userId) {
        logger.info("Sending welcome notification to user: {}", userId);
        
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("User not found with ID: " + userId));
        
        Notification notification = new Notification(
                "Welcome to ABC Banking!",
                "Welcome to ABC Banking! Your account has been successfully created. Start exploring our services.",
                NotificationType.WELCOME,
                user
        );
        notification.setPriority(NotificationPriority.MEDIUM);
        notification.setCategory("WELCOME");
        
        Notification savedNotification = createNotification(notification);
        sendNotification(savedNotification.getId());
    }
    
    @Override
    public void sendBulkNotification(List<Long> userIds, String title, String message, NotificationType type) {
        logger.info("Sending bulk notification to {} users", userIds.size());
        
        for (Long userId : userIds) {
            try {
                User user = userRepository.findById(userId)
                        .orElseThrow(() -> new RuntimeException("User not found with ID: " + userId));
                
                Notification notification = new Notification(title, message, type, user);
                notification.setPriority(NotificationPriority.MEDIUM);
                
                Notification savedNotification = createNotification(notification);
                sendNotification(savedNotification.getId());
                
            } catch (Exception e) {
                logger.error("Failed to send notification to user: {}", userId, e);
            }
        }
        
        logger.info("Bulk notification sending completed");
    }
    
    @Override
    public void cleanupOldNotifications(int daysOld) {
        logger.info("Cleaning up notifications older than {} days", daysOld);
        
        LocalDateTime cutoffDate = LocalDateTime.now().minusDays(daysOld);
        // This would typically be implemented with a custom query
        // For now, we'll just log the action
        
        logger.info("Cleanup completed for notifications older than {}", cutoffDate);
    }
}
