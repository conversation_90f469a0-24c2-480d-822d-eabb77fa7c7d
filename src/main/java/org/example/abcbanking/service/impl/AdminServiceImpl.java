package org.example.abcbanking.service.impl;

import org.example.abcbanking.dto.AdminDashboardResponse;
import org.example.abcbanking.model.*;
import org.example.abcbanking.repository.*;
import org.example.abcbanking.service.AdminService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

@Service
@Transactional
public class AdminServiceImpl implements AdminService {

    private static final Logger logger = LoggerFactory.getLogger(AdminServiceImpl.class);

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private AccountRepository accountRepository;

    @Autowired
    private TransactionRepository transactionRepository;

    @Autowired
    private CardRepository cardRepository;

    @Autowired
    private BranchRepository branchRepository;

    @Autowired
    private NotificationRepository notificationRepository;

    @Override
    public AdminDashboardResponse getDashboardAnalytics() {
        logger.info("Generating admin dashboard analytics");
        
        AdminDashboardResponse dashboard = new AdminDashboardResponse();
        
        try {
            // System Overview
            dashboard.setTotalCustomers(userRepository.countByRoles_Name(ERole.ROLE_CUSTOMER));
            dashboard.setTotalAccounts(accountRepository.count());
            dashboard.setTotalTransactions(transactionRepository.count());
            dashboard.setTotalCards(cardRepository.count());
            dashboard.setTotalLoans(0L); // Will be implemented when LoanRepository is available
            dashboard.setTotalBranches(branchRepository.count());
            
            // Financial Overview
            BigDecimal totalDeposits = calculateTotalDeposits();
            BigDecimal totalLoansAmount = calculateTotalLoansAmount();
            dashboard.setTotalDeposits(totalDeposits);
            dashboard.setTotalLoansAmount(totalLoansAmount);
            dashboard.setTotalAssets(totalDeposits.add(totalLoansAmount));
            
            // Today's Statistics
            LocalDate today = LocalDate.now();
            LocalDateTime startOfDay = today.atStartOfDay();
            LocalDateTime endOfDay = today.plusDays(1).atStartOfDay();
            
            dashboard.setTodayTransactions(
                transactionRepository.countByTransactionDateBetween(startOfDay, endOfDay));
            
            BigDecimal todayVolume = transactionRepository.findByTransactionDateBetween(startOfDay, endOfDay)
                .stream()
                .map(Transaction::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            dashboard.setTodayTransactionVolume(todayVolume);
            
            // Account Statistics
            dashboard.setActiveAccounts(accountRepository.countByStatus(AccountStatus.ACTIVE));
            dashboard.setInactiveAccounts(accountRepository.countByStatus(AccountStatus.INACTIVE));
            dashboard.setFrozenAccounts(accountRepository.countByStatus(AccountStatus.FROZEN));
            dashboard.setClosedAccounts(accountRepository.countByStatus(AccountStatus.CLOSED));
            
            // Card Statistics
            dashboard.setActiveCards(cardRepository.countByStatus(CardStatus.ACTIVE));
            dashboard.setBlockedCards(cardRepository.countByStatus(CardStatus.BLOCKED));
            dashboard.setExpiredCards(cardRepository.countByStatus(CardStatus.EXPIRED));
            
            // Loan Statistics - Mock data for now
            dashboard.setPendingLoans(23L);
            dashboard.setApprovedLoans(398L);
            dashboard.setRejectedLoans(35L);
            dashboard.setActiveLoans(387L);
            dashboard.setDefaultedLoans(11L);
            
            // Recent Activity
            dashboard.setRecentTransactions(
                transactionRepository.findTop10ByOrderByTransactionDateDesc());
            dashboard.setRecentCustomers(
                userRepository.findTop10ByOrderByCreatedAtDesc());
            
            // System Health
            dashboard.setSystemStatus("HEALTHY");
            dashboard.setLastUpdated(LocalDateTime.now());
            
            return dashboard;
            
        } catch (Exception e) {
            logger.error("Error generating dashboard analytics", e);
            throw new RuntimeException("Failed to generate dashboard analytics", e);
        }
    }

    @Override
    public Map<String, Object> getSystemHealth() {
        Map<String, Object> health = new HashMap<>();
        
        try {
            // Database connectivity
            long userCount = userRepository.count();
            health.put("databaseConnected", true);
            health.put("totalRecords", userCount);
            
            // System metrics
            health.put("uptime", "99.9%");
            health.put("responseTime", "150ms");
            health.put("errorRate", "0.1%");
            health.put("activeConnections", 25);
            
            // Memory usage
            Runtime runtime = Runtime.getRuntime();
            long totalMemory = runtime.totalMemory();
            long freeMemory = runtime.freeMemory();
            long usedMemory = totalMemory - freeMemory;
            
            health.put("totalMemory", totalMemory / (1024 * 1024) + " MB");
            health.put("usedMemory", usedMemory / (1024 * 1024) + " MB");
            health.put("freeMemory", freeMemory / (1024 * 1024) + " MB");
            health.put("memoryUsagePercent", (usedMemory * 100) / totalMemory);
            
            health.put("status", "HEALTHY");
            health.put("timestamp", LocalDateTime.now());
            
        } catch (Exception e) {
            logger.error("Error getting system health", e);
            health.put("status", "UNHEALTHY");
            health.put("error", e.getMessage());
        }
        
        return health;
    }

    @Override
    public Map<String, Object> getFinancialSummary() {
        Map<String, Object> summary = new HashMap<>();
        
        try {
            BigDecimal totalDeposits = calculateTotalDeposits();
            BigDecimal totalLoans = calculateTotalLoansAmount();
            BigDecimal totalAssets = totalDeposits.add(totalLoans);
            
            summary.put("totalDeposits", totalDeposits);
            summary.put("totalLoans", totalLoans);
            summary.put("totalAssets", totalAssets);
            summary.put("netWorth", totalAssets.multiply(new BigDecimal("0.1"))); // Simplified calculation
            
            // Daily revenue (simplified)
            LocalDate today = LocalDate.now();
            BigDecimal dailyRevenue = calculateDailyRevenue(today);
            summary.put("dailyRevenue", dailyRevenue);
            
            // Growth rates (mock data for now)
            summary.put("customerGrowthRate", 5.2);
            summary.put("accountGrowthRate", 3.8);
            summary.put("revenueGrowthRate", 7.1);
            
            summary.put("timestamp", LocalDateTime.now());
            
        } catch (Exception e) {
            logger.error("Error getting financial summary", e);
            throw new RuntimeException("Failed to get financial summary", e);
        }
        
        return summary;
    }

    @Override
    public Page<User> getAllCustomers(Pageable pageable) {
        return userRepository.findByRoles_Name(ERole.ROLE_CUSTOMER, pageable);
    }

    @Override
    public Page<User> searchCustomers(String searchTerm, Pageable pageable) {
        return userRepository.findByFirstNameContainingIgnoreCaseOrLastNameContainingIgnoreCaseOrEmailContainingIgnoreCase(
            searchTerm, searchTerm, searchTerm, pageable);
    }

    @Override
    public User getCustomerDetails(Long customerId) {
        return userRepository.findById(customerId)
            .orElseThrow(() -> new RuntimeException("Customer not found with ID: " + customerId));
    }

    @Override
    public List<Account> getCustomerAccounts(Long customerId) {
        return accountRepository.findByUserId(customerId);
    }

    @Override
    public Page<Transaction> getCustomerTransactions(Long customerId, Pageable pageable) {
        return transactionRepository.findByFromAccountUserIdOrToAccountUserId(
            customerId, customerId, pageable);
    }

    @Override
    public void updateCustomerStatus(Long customerId, UserStatus status) {
        User customer = getCustomerDetails(customerId);
        customer.setStatus(status);
        userRepository.save(customer);
        
        // Log the action
        logger.info("Customer {} status updated to {} by admin", customerId, status);
    }

    @Override
    public void freezeCustomerAccounts(Long customerId, String reason) {
        List<Account> accounts = getCustomerAccounts(customerId);
        for (Account account : accounts) {
            account.setStatus(AccountStatus.FROZEN);
            account.setFreezeReason(reason);
            account.setFreezeDate(LocalDateTime.now());
        }
        accountRepository.saveAll(accounts);
        
        logger.info("All accounts for customer {} have been frozen. Reason: {}", customerId, reason);
    }

    @Override
    public void unfreezeCustomerAccounts(Long customerId) {
        List<Account> accounts = getCustomerAccounts(customerId);
        for (Account account : accounts) {
            if (account.getStatus() == AccountStatus.FROZEN) {
                account.setStatus(AccountStatus.ACTIVE);
                account.setFreezeReason(null);
                account.setFreezeDate(null);
            }
        }
        accountRepository.saveAll(accounts);
        
        logger.info("All frozen accounts for customer {} have been unfrozen", customerId);
    }

    @Override
    public Page<Account> getAllAccounts(Pageable pageable) {
        return accountRepository.findAll(pageable);
    }

    @Override
    public Page<Account> getAccountsByType(AccountType type, Pageable pageable) {
        return accountRepository.findByType(type, pageable);
    }

    @Override
    public Page<Account> getAccountsByStatus(AccountStatus status, Pageable pageable) {
        return accountRepository.findByStatus(status, pageable);
    }

    @Override
    public void updateAccountStatus(Long accountId, AccountStatus status, String reason) {
        Account account = accountRepository.findById(accountId)
            .orElseThrow(() -> new RuntimeException("Account not found with ID: " + accountId));
        
        AccountStatus oldStatus = account.getStatus();
        account.setStatus(status);
        
        if (status == AccountStatus.FROZEN) {
            account.setFreezeReason(reason);
            account.setFreezeDate(LocalDateTime.now());
        } else if (status == AccountStatus.CLOSED) {
            account.setClosureReason(reason);
            account.setClosureDate(LocalDateTime.now());
        }
        
        accountRepository.save(account);
        
        logger.info("Account {} status changed from {} to {}. Reason: {}", 
            accountId, oldStatus, status, reason);
    }

    @Override
    public List<Transaction> getSuspiciousTransactions() {
        // Define criteria for suspicious transactions
        BigDecimal highAmountThreshold = new BigDecimal("100000"); // 1 Lakh INR
        LocalDateTime last24Hours = LocalDateTime.now().minusHours(24);
        
        return transactionRepository.findByAmountGreaterThanAndTransactionDateAfter(
            highAmountThreshold, last24Hours);
    }

    // Helper methods
    private BigDecimal calculateTotalDeposits() {
        return accountRepository.findAll().stream()
            .filter(account -> account.getType() == AccountType.SAVINGS || 
                             account.getType() == AccountType.CURRENT)
            .map(Account::getBalance)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    private BigDecimal calculateTotalLoansAmount() {
        // Mock data for now - will be implemented when LoanRepository is available
        return new BigDecimal("********");
    }

    @Override
    public BigDecimal calculateDailyRevenue(LocalDate date) {
        // Simplified revenue calculation based on transaction fees
        LocalDateTime startOfDay = date.atStartOfDay();
        LocalDateTime endOfDay = date.plusDays(1).atStartOfDay();
        
        List<Transaction> dayTransactions = transactionRepository
            .findByTransactionDateBetween(startOfDay, endOfDay);
        
        // Assume 0.1% fee on all transactions
        return dayTransactions.stream()
            .map(Transaction::getAmount)
            .map(amount -> amount.multiply(new BigDecimal("0.001")))
            .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    // Placeholder implementations for interface compliance
    @Override public void freezeAccount(Long accountId, String reason) { updateAccountStatus(accountId, AccountStatus.FROZEN, reason); }
    @Override public void unfreezeAccount(Long accountId) { updateAccountStatus(accountId, AccountStatus.ACTIVE, null); }
    @Override public void closeAccount(Long accountId, String reason) { updateAccountStatus(accountId, AccountStatus.CLOSED, reason); }
    @Override public BigDecimal getAccountBalance(Long accountId) { return accountRepository.findById(accountId).map(Account::getBalance).orElse(BigDecimal.ZERO); }
    @Override public void adjustAccountBalance(Long accountId, BigDecimal amount, String reason) { /* Implementation needed */ }
    @Override public Page<Transaction> getAllTransactions(Pageable pageable) { return transactionRepository.findAll(pageable); }
    @Override public Page<Transaction> getTransactionsByStatus(TransactionStatus status, Pageable pageable) { return transactionRepository.findByStatus(status, pageable); }
    @Override public Page<Transaction> getTransactionsByType(TransactionType type, Pageable pageable) { return transactionRepository.findByType(type, pageable); }
    @Override public Page<Transaction> getTransactionsByDateRange(LocalDate startDate, LocalDate endDate, Pageable pageable) { return transactionRepository.findByTransactionDateBetween(startDate.atStartOfDay(), endDate.atStartOfDay(), pageable); }
}
