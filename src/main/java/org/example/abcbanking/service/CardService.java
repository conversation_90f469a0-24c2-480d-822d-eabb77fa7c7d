package org.example.abcbanking.service;

import org.example.abcbanking.model.Card;
import org.example.abcbanking.model.CardStatus;
import org.example.abcbanking.model.CardType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

public interface CardService {
    
    // Basic CRUD operations
    Card createCard(Card card);
    Card updateCard(Long id, Card card);
    void deleteCard(Long id);
    Optional<Card> getCardById(Long id);
    Optional<Card> getCardByNumber(String cardNumber);
    List<Card> getAllCards();
    Page<Card> getAllCards(Pageable pageable);
    
    // User-specific operations
    List<Card> getCardsByUserId(Long userId);
    List<Card> getCardsByAccountId(Long accountId);
    Page<Card> getCardsByUserId(Long userId, Pageable pageable);
    
    // Card management operations
    void blockCard(Long cardId, String reason, String blockedBy);
    void unblockCard(Long cardId);
    void activateCard(Long cardId, String pin);
    void deactivateCard(Long cardId);
    
    // Security operations
    boolean validatePin(Long cardId, String pin);
    void changePin(Long cardId, String oldPin, String newPin);
    void resetPin(Long cardId, String newPin);
    void lockPin(Long cardId);
    void unlockPin(Long cardId);
    
    // Limit management
    void setDailyLimit(Long cardId, BigDecimal limit);
    void setMonthlyLimit(Long cardId, BigDecimal limit);
    void setCreditLimit(Long cardId, BigDecimal limit);
    
    // Transaction controls
    void enableContactless(Long cardId);
    void disableContactless(Long cardId);
    void enableOnlineTransactions(Long cardId);
    void disableOnlineTransactions(Long cardId);
    void enableInternationalTransactions(Long cardId);
    void disableInternationalTransactions(Long cardId);
    
    // Card lifecycle
    Card requestReplacement(Long cardId, String reason);
    void reportLostOrStolen(Long cardId, String reason);
    List<Card> getExpiringCards(int daysFromNow);
    
    // Analytics and reporting
    Long countActiveCardsByUserId(Long userId);
    Long countCardsByType(CardType cardType);
    List<Card> getCardsByStatus(CardStatus status);
    
    // Utility methods
    String generateCardNumber(CardType cardType);
    String maskCardNumber(String cardNumber);
    boolean isCardExpired(Long cardId);
    boolean isCardBlocked(Long cardId);
    
    // Rewards and cashback
    void addRewardsPoints(Long cardId, Long points);
    void redeemRewardsPoints(Long cardId, Long points);
    void addCashback(Long cardId, BigDecimal amount);
}
