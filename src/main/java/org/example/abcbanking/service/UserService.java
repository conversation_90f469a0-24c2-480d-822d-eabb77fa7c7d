package org.example.abcbanking.service;

import org.example.abcbanking.model.User;
import org.example.abcbanking.model.UserStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Optional;

public interface UserService {
    User createUser(User user);
    User updateUser(Long id, User user);
    void deleteUser(Long id);
    Optional<User> getUserById(Long id);
    Optional<User> getUserByUsername(String username);
    Optional<User> getUserByEmail(String email);
    List<User> getAllUsers();
    Page<User> getAllUsers(Pageable pageable);
    List<User> getUsersByStatus(UserStatus status);
    Page<User> searchUsers(String searchTerm, Pageable pageable);
    void lockUser(Long id);
    void unlockUser(Long id);
    void verifyEmail(Long id);
    void verifyPhone(Long id);
    void verifyKyc(Long id);
    void resetPassword(Long id, String newPassword);
    void changePassword(Long id, String oldPassword, String newPassword);
    void incrementFailedLoginAttempts(Long id);
    void resetFailedLoginAttempts(Long id);
    void setUserStatus(Long id, UserStatus status);
    boolean isAccountLocked(Long id);
    boolean isEmailVerified(Long id);
    boolean isPhoneVerified(Long id);
    boolean isKycVerified(Long id);
} 