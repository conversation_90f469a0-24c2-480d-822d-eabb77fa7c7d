package org.example.abcbanking.service;

import org.example.abcbanking.model.Notification;
import org.example.abcbanking.model.NotificationStatus;
import org.example.abcbanking.model.NotificationType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Optional;

public interface NotificationService {
    
    // Basic CRUD operations
    Notification createNotification(Notification notification);
    Notification updateNotification(Long id, Notification notification);
    void deleteNotification(Long id);
    Optional<Notification> getNotificationById(Long id);
    List<Notification> getAllNotifications();
    Page<Notification> getAllNotifications(Pageable pageable);
    
    // User-specific operations
    List<Notification> getNotificationsByUserId(Long userId);
    Page<Notification> getNotificationsByUserId(Long userId, Pageable pageable);
    List<Notification> getUnreadNotificationsByUserId(Long userId);
    Long countUnreadNotificationsByUserId(Long userId);
    
    // Notification management
    void markAsRead(Long notificationId);
    void markAllAsReadByUserId(Long userId);
    void sendNotification(Long notificationId);
    void scheduleNotification(Notification notification);
    
    // Filtering and search
    List<Notification> getNotificationsByType(NotificationType type);
    List<Notification> getNotificationsByStatus(NotificationStatus status);
    List<Notification> getNotificationsByUserIdAndType(Long userId, NotificationType type);
    
    // Utility methods
    void sendTransactionAlert(Long userId, String transactionDetails);
    void sendSecurityAlert(Long userId, String securityMessage);
    void sendAccountUpdate(Long userId, String updateMessage);
    void sendWelcomeNotification(Long userId);
    
    // Bulk operations
    void sendBulkNotification(List<Long> userIds, String title, String message, NotificationType type);
    void cleanupOldNotifications(int daysOld);
}
