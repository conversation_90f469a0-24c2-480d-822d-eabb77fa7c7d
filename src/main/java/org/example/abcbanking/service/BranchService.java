package org.example.abcbanking.service;

import org.example.abcbanking.model.Branch;
import org.example.abcbanking.model.BranchStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Optional;

public interface BranchService {
    Branch createBranch(Branch branch);
    Branch updateBranch(Long id, Branch branch);
    void deleteBranch(Long id);
    Optional<Branch> getBranchById(Long id);
    Optional<Branch> getBranchByCode(String branchCode);
    List<Branch> getAllBranches();
    Page<Branch> getAllBranches(Pageable pageable);
    List<Branch> getBranchesByStatus(BranchStatus status);
    Page<Branch> searchBranches(String searchTerm, Pageable pageable);
    void setBranchStatus(Long id, BranchStatus status);
} 