package org.example.abcbanking.service;

import org.example.abcbanking.dto.AdminDashboardResponse;
import org.example.abcbanking.model.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

public interface AdminService {
    
    // Dashboard Analytics
    AdminDashboardResponse getDashboardAnalytics();
    Map<String, Object> getSystemHealth();
    Map<String, Object> getFinancialSummary();
    
    // Customer Management
    Page<User> getAllCustomers(Pageable pageable);
    Page<User> searchCustomers(String searchTerm, Pageable pageable);
    User getCustomerDetails(Long customerId);
    List<Account> getCustomerAccounts(Long customerId);
    Page<Transaction> getCustomerTransactions(Long customerId, Pageable pageable);
    void updateCustomerStatus(Long customerId, UserStatus status);
    void freezeCustomerAccounts(Long customerId, String reason);
    void unfreezeCustomerAccounts(Long customerId);
    
    // Account Management
    Page<Account> getAllAccounts(Pageable pageable);
    Page<Account> getAccountsByType(AccountType type, Pageable pageable);
    Page<Account> getAccountsByStatus(AccountStatus status, Pageable pageable);
    void updateAccountStatus(Long accountId, AccountStatus status, String reason);
    void freezeAccount(Long accountId, String reason);
    void unfreezeAccount(Long accountId);
    void closeAccount(Long accountId, String reason);
    BigDecimal getAccountBalance(Long accountId);
    void adjustAccountBalance(Long accountId, BigDecimal amount, String reason);
    
    // Transaction Management
    Page<Transaction> getAllTransactions(Pageable pageable);
    Page<Transaction> getTransactionsByStatus(TransactionStatus status, Pageable pageable);
    Page<Transaction> getTransactionsByType(TransactionType type, Pageable pageable);
    Page<Transaction> getTransactionsByDateRange(LocalDate startDate, LocalDate endDate, Pageable pageable);
    List<Transaction> getSuspiciousTransactions();
    List<Transaction> getHighValueTransactions(BigDecimal threshold);
    List<Transaction> getFailedTransactions();
    void reverseTransaction(Long transactionId, String reason);
    void approveTransaction(Long transactionId);
    void rejectTransaction(Long transactionId, String reason);
    
    // Financial Operations
    BigDecimal calculateTotalAssets();
    BigDecimal calculateTotalLiabilities();
    BigDecimal calculateNetWorth();
    BigDecimal calculateDailyRevenue(LocalDate date);
    BigDecimal calculateMonthlyRevenue(int year, int month);
    BigDecimal calculateYearlyRevenue(int year);
}
