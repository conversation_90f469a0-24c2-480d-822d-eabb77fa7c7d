package org.example.abcbanking.service;

import org.example.abcbanking.model.ERole;
import org.example.abcbanking.model.Role;

import java.util.List;
import java.util.Optional;

public interface RoleService {
    Role createRole(Role role);
    Role updateRole(Long id, Role role);
    void deleteRole(Long id);
    Optional<Role> getRoleById(Long id);
    Optional<Role> getRoleByName(ERole name);
    List<Role> getAllRoles();
    List<Role> getActiveRoles();
} 