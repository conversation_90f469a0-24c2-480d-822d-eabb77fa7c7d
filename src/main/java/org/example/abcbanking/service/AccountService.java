package org.example.abcbanking.service;

import org.example.abcbanking.model.Account;
import org.example.abcbanking.model.AccountStatus;
import org.example.abcbanking.model.AccountType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

public interface AccountService {
    Account createAccount(Account account);
    Account updateAccount(Long id, Account account);
    void closeAccount(Long id, String reason);
    void freezeAccount(Long id, String reason);
    void unfreezeAccount(Long id);
    Optional<Account> getAccountById(Long id);
    Optional<Account> getAccountByNumber(String accountNumber);
    List<Account> getAllAccounts();
    Page<Account> getAllAccounts(Pageable pageable);
    List<Account> getAccountsByUserId(Long userId);
    List<Account> getAccountsByBranchId(Long branchId);
    List<Account> getAccountsByType(AccountType type);
    List<Account> getAccountsByStatus(AccountStatus status);
    BigDecimal getAccountBalance(Long id);
    void creditAccount(Long id, BigDecimal amount, String description);
    void debitAccount(Long id, BigDecimal amount, String description);
    void setAccountStatus(Long id, AccountStatus status);
} 