package org.example.abcbanking.service;

import org.example.abcbanking.model.Loan;
import org.example.abcbanking.model.LoanEMI;
import org.example.abcbanking.model.LoanStatus;
import org.example.abcbanking.model.LoanType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

public interface LoanService {
    Loan createLoan(Loan loan);
    Loan updateLoan(Long id, Loan loan);
    void deleteLoan(Long id);
    Optional<Loan> getLoanById(Long id);
    Optional<Loan> getLoanByNumber(String loanNumber);
    List<Loan> getAllLoans();
    Page<Loan> getAllLoans(Pageable pageable);
    List<Loan> getLoansByUserId(Long userId);
    List<Loan> getLoansByBranchId(Long branchId);
    List<Loan> getLoansByType(LoanType type);
    List<Loan> getLoansByStatus(LoanStatus status);
    void approveLoan(Long id, Long approvedById);
    void rejectLoan(Long id, String reason);
    void disburseLoan(Long id);
    void closeLoan(Long id);
    void markLoanAsDefaulted(Long id);
    void setLoanStatus(Long id, LoanStatus status);
    BigDecimal getOutstandingAmount(Long id);
    void calculateEMI(Long id);

    // Enhanced EMI Management
    List<LoanEMI> generateEmiSchedule(Long loanId);
    LoanEMI processEmiPayment(Long emiId, BigDecimal amount, String paymentMethod);
    List<LoanEMI> getEmisByLoanId(Long loanId);
    List<LoanEMI> getOverdueEmis();
    List<LoanEMI> getUpcomingEmis(int days);

    // EMI Calculations
    BigDecimal calculateEmiAmount(BigDecimal principal, BigDecimal interestRate, int tenureMonths);
    BigDecimal calculateTotalInterest(BigDecimal principal, BigDecimal interestRate, int tenureMonths);
    BigDecimal calculateOutstandingAmount(Long loanId);
    BigDecimal calculatePenaltyAmount(Long emiId);

    // Loan Analytics
    BigDecimal getTotalLoanAmountByUserId(Long userId);
    BigDecimal getTotalOutstandingAmountByUserId(Long userId);
    List<Loan> getLoansDueForPayment(LocalDate date);
    BigDecimal calculateCreditScore(Long userId);

    // Loan Eligibility
    boolean checkLoanEligibility(Long userId, BigDecimal requestedAmount, LoanType loanType);
    BigDecimal calculateMaxEligibleAmount(Long userId, LoanType loanType);

    // Enhanced Search and Reporting
    Page<Loan> searchLoans(String searchTerm, Pageable pageable);
    List<Loan> getLoansCreatedBetween(LocalDate startDate, LocalDate endDate);
    BigDecimal getTotalDisbursedAmount(LocalDate startDate, LocalDate endDate);
    List<LoanEMI> getDefaultedEmis();
}