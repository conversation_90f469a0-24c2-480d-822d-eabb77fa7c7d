package org.example.abcbanking.service;

import org.example.abcbanking.model.EMIStatus;
import org.example.abcbanking.model.LoanEMI;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

public interface LoanEMIService {
    LoanEMI createEMI(LoanEMI emi);
    LoanEMI updateEMI(Long id, LoanEMI emi);
    void deleteEMI(Long id);
    Optional<LoanEMI> getEMIById(Long id);
    List<LoanEMI> getEMIsByLoanId(Long loanId);
    List<LoanEMI> getEMIsByStatus(EMIStatus status);
    List<LoanEMI> getOverdueEMIs();
    void markEMIAsPaid(Long id, BigDecimal amount, String paymentMethod, String transactionId);
    void markEMIAsOverdue(Long id);
    void markEMIAsDefaulted(Long id);
    void calculateLateFee(Long id, BigDecimal lateFeeRate);
    void calculateOverdueDays(Long id);
} 