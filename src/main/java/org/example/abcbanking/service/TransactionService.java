package org.example.abcbanking.service;

import org.example.abcbanking.model.Transaction;
import org.example.abcbanking.model.TransactionStatus;
import org.example.abcbanking.model.TransactionType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

public interface TransactionService {
    Transaction createTransaction(Transaction transaction);
    Transaction processTransfer(Long fromAccountId, Long toAccountId, BigDecimal amount, String description);
    Optional<Transaction> getTransactionById(Long id);
    Optional<Transaction> getTransactionByTransactionId(String transactionId);
    List<Transaction> getAllTransactions();
    Page<Transaction> getAllTransactions(Pageable pageable);
    List<Transaction> getTransactionsByAccountId(Long accountId);
    List<Transaction> getTransactionsByType(TransactionType type);
    List<Transaction> getTransactionsByStatus(TransactionStatus status);
    List<Transaction> getTransactionsByDateRange(LocalDateTime start, LocalDateTime end);
    Page<Transaction> searchTransactions(String searchTerm, Pageable pageable);
    void markTransactionAsCompleted(Long id);
    void markTransactionAsFailed(Long id, String reason);
    void markTransactionAsSuspicious(Long id, String reason);
    void reverseTransaction(Long id, String reason);
    BigDecimal getTotalAmountByAccountId(Long accountId);
} 