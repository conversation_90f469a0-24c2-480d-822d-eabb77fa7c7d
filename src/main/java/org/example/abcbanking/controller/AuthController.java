package org.example.abcbanking.controller;

import org.example.abcbanking.dto.LoginRequest;
import org.example.abcbanking.dto.LoginResponse;
import org.example.abcbanking.dto.UserDto;
import org.example.abcbanking.dto.MessageResponse;
import org.example.abcbanking.dto.ErrorResponse;
import org.example.abcbanking.dto.RefreshTokenRequest;
import org.example.abcbanking.model.User;
import org.example.abcbanking.repository.UserRepository;
import org.example.abcbanking.security.JwtUtils;
import org.example.abcbanking.service.UserService;
import org.example.abcbanking.service.impl.UserPrincipal;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

@RestController
@RequestMapping("/auth")
@CrossOrigin(origins = "*", maxAge = 3600)
public class AuthController {
    
    private static final Logger logger = LoggerFactory.getLogger(AuthController.class);

    @Autowired
    private AuthenticationManager authenticationManager;

    @Autowired
    private JwtUtils jwtUtils;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private UserService userService;

    @GetMapping("/test")
    public ResponseEntity<?> testEndpoint() {
        return ResponseEntity.ok(new MessageResponse("Auth endpoint is working!"));
    }

    @GetMapping("/test/db")
    public ResponseEntity<?> testDatabase() {
        try {
            long userCount = userRepository.count();
            String message = String.format("Database is connected and working! Found %d users in database.", userCount);
            return ResponseEntity.ok(new MessageResponse(message));
        } catch (Exception e) {
            return ResponseEntity.status(500)
                .body(new ErrorResponse("Database connection failed: " + e.getMessage()));
        }
    }

    @PostMapping("/login")
    public ResponseEntity<?> authenticateUser(@Valid @RequestBody LoginRequest loginRequest) {
        try {
            Authentication authentication = authenticationManager.authenticate(
                new UsernamePasswordAuthenticationToken(
                    loginRequest.getUsername(), 
                    loginRequest.getPassword()
                )
            );

            SecurityContextHolder.getContext().setAuthentication(authentication);
            String jwt = jwtUtils.generateJwtToken(authentication);
            String refreshToken = jwtUtils.generateRefreshToken(authentication);

            // Get real user data
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            User user = userRepository.findById(userPrincipal.getId())
                    .orElseThrow(() -> new RuntimeException("User not found"));

            // Reset failed login attempts on successful login
            userService.resetFailedLoginAttempts(user.getId());

            // Create user DTO with real data
            UserDto userDto = new UserDto();
            userDto.setId(user.getId());
            userDto.setUsername(user.getUsername());
            userDto.setEmail(user.getEmail());
            userDto.setFirstName(user.getFirstName());
            userDto.setLastName(user.getLastName());
            userDto.setStatus(user.getStatus().toString());
            userDto.setEmailVerified(user.isEmailVerified());
            userDto.setPhoneVerified(user.isPhoneVerified());
            userDto.setKycVerified(user.isKycVerified());

            LoginResponse response = new LoginResponse();
            response.setToken(jwt);
            response.setRefreshToken(refreshToken);
            response.setUser(userDto);
            response.setExpiresIn(jwtUtils.getJwtExpirationMs());

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Authentication failed for user: {}", loginRequest.getUsername(), e);

            // Increment failed login attempts
            try {
                User user = userRepository.findByUsername(loginRequest.getUsername()).orElse(null);
                if (user != null) {
                    userService.incrementFailedLoginAttempts(user.getId());
                }
            } catch (Exception ex) {
                logger.error("Error incrementing failed login attempts", ex);
            }

            return ResponseEntity.badRequest()
                .body(new ErrorResponse("Invalid username or password"));
        }
    }

    @PostMapping("/logout")
    public ResponseEntity<?> logoutUser() {
        SecurityContextHolder.clearContext();
        return ResponseEntity.ok(new MessageResponse("User logged out successfully!"));
    }

    @PostMapping("/refresh")
    public ResponseEntity<?> refreshToken(@RequestBody RefreshTokenRequest request) {
        try {
            String refreshToken = request.getRefreshToken();
            
            if (jwtUtils.validateJwtToken(refreshToken)) {
                String username = jwtUtils.getUserNameFromJwtToken(refreshToken);
                
                // Create new authentication for refresh
                Authentication authentication = new UsernamePasswordAuthenticationToken(
                    username, null, null
                );
                
                String newJwt = jwtUtils.generateJwtToken(authentication);
                String newRefreshToken = jwtUtils.generateRefreshToken(authentication);

                // Mock user response
                UserDto userDto = new UserDto();
                userDto.setId(1L);
                userDto.setUsername(username);
                userDto.setEmail("<EMAIL>");
                userDto.setFirstName("John");
                userDto.setLastName("Doe");
                userDto.setStatus("ACTIVE");

                LoginResponse response = new LoginResponse();
                response.setToken(newJwt);
                response.setRefreshToken(newRefreshToken);
                response.setUser(userDto);
                response.setExpiresIn(jwtUtils.getJwtExpirationMs());

                return ResponseEntity.ok(response);
            } else {
                return ResponseEntity.badRequest()
                    .body(new ErrorResponse("Invalid refresh token"));
            }
        } catch (Exception e) {
            logger.error("Token refresh failed", e);
            return ResponseEntity.badRequest()
                .body(new ErrorResponse("Token refresh failed"));
        }
    }

    // Helper classes
    public static class RefreshTokenRequest {
        private String refreshToken;
        
        public String getRefreshToken() { return refreshToken; }
        public void setRefreshToken(String refreshToken) { this.refreshToken = refreshToken; }
    }

    public static class MessageResponse {
        private String message;
        
        public MessageResponse(String message) { this.message = message; }
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
    }

    public static class ErrorResponse {
        private String error;
        
        public ErrorResponse(String error) { this.error = error; }
        public String getError() { return error; }
        public void setError(String error) { this.error = error; }
    }
}
