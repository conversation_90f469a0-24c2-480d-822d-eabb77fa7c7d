package org.example.abcbanking.controller;

import org.example.abcbanking.dto.AccountDto;
import org.example.abcbanking.dto.CreateAccountRequest;
import org.example.abcbanking.dto.MessageResponse;
import org.example.abcbanking.dto.ErrorResponse;
import org.example.abcbanking.model.Account;
import org.example.abcbanking.model.AccountStatus;
import org.example.abcbanking.model.AccountType;
import org.example.abcbanking.model.User;
import org.example.abcbanking.repository.UserRepository;
import org.example.abcbanking.service.AccountService;
import org.example.abcbanking.service.impl.UserPrincipal;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/accounts")
@CrossOrigin(origins = "*", maxAge = 3600)
public class AccountController {

    private static final Logger logger = LoggerFactory.getLogger(AccountController.class);

    @Autowired
    private AccountService accountService;

    @Autowired
    private UserRepository userRepository;

    @PostMapping("/create")
    @PreAuthorize("hasRole('ADMIN') or hasRole('BRANCH_MANAGER')")
    public ResponseEntity<?> createAccount(@Valid @RequestBody CreateAccountRequest request) {
        try {
            logger.info("Creating account for user ID: {}", request.getUserId());

            User user = userRepository.findById(request.getUserId())
                    .orElseThrow(() -> new RuntimeException("User not found with ID: " + request.getUserId()));

            Account account = new Account();
            account.setUser(user);
            account.setType(AccountType.valueOf(request.getAccountType()));
            account.setAccountHolderName(request.getAccountHolderName());
            account.setContactNumber(request.getContactNumber());
            account.setEmailAddress(request.getEmailAddress());
            account.setAddress(request.getAddress());
            account.setBalance(request.getInitialDeposit() != null ? request.getInitialDeposit() : BigDecimal.ZERO);

            Account createdAccount = accountService.createAccount(account);
            AccountDto accountDto = convertToDto(createdAccount);

            return ResponseEntity.ok(accountDto);
        } catch (Exception e) {
            logger.error("Error creating account", e);
            return ResponseEntity.badRequest()
                    .body(new ErrorResponse("Failed to create account: " + e.getMessage()));
        }
    }

    @GetMapping("/my")
    public ResponseEntity<?> getMyAccounts(Authentication authentication) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            Long userId = userPrincipal.getId();

            List<Account> accounts = accountService.getAccountsByUserId(userId);
            List<AccountDto> accountDtos = accounts.stream()
                    .map(this::convertToDto)
                    .collect(Collectors.toList());

            return ResponseEntity.ok(accountDtos);
        } catch (Exception e) {
            logger.error("Error fetching user accounts", e);
            return ResponseEntity.badRequest()
                    .body(new ErrorResponse("Failed to fetch accounts: " + e.getMessage()));
        }
    }

    @GetMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('BRANCH_MANAGER') or @accountService.getAccountById(#id).orElse(null)?.user?.id == authentication.principal.id")
    public ResponseEntity<?> getAccountById(@PathVariable Long id) {
        try {
            Optional<Account> account = accountService.getAccountById(id);
            if (account.isPresent()) {
                AccountDto accountDto = convertToDto(account.get());
                return ResponseEntity.ok(accountDto);
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            logger.error("Error fetching account by ID: {}", id, e);
            return ResponseEntity.badRequest()
                    .body(new ErrorResponse("Failed to fetch account: " + e.getMessage()));
        }
    }

    @GetMapping("/number/{accountNumber}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('BRANCH_MANAGER')")
    public ResponseEntity<?> getAccountByNumber(@PathVariable String accountNumber) {
        try {
            Optional<Account> account = accountService.getAccountByNumber(accountNumber);
            if (account.isPresent()) {
                AccountDto accountDto = convertToDto(account.get());
                return ResponseEntity.ok(accountDto);
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            logger.error("Error fetching account by number: {}", accountNumber, e);
            return ResponseEntity.badRequest()
                    .body(new ErrorResponse("Failed to fetch account: " + e.getMessage()));
        }
    }

    @GetMapping("/all")
    @PreAuthorize("hasRole('ADMIN') or hasRole('BRANCH_MANAGER')")
    public ResponseEntity<?> getAllAccounts(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "createdAt") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir) {
        try {
            Sort sort = sortDir.equalsIgnoreCase("desc") ? 
                    Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
            Pageable pageable = PageRequest.of(page, size, sort);

            Page<Account> accounts = accountService.getAllAccounts(pageable);
            Page<AccountDto> accountDtos = accounts.map(this::convertToDto);

            return ResponseEntity.ok(accountDtos);
        } catch (Exception e) {
            logger.error("Error fetching all accounts", e);
            return ResponseEntity.badRequest()
                    .body(new ErrorResponse("Failed to fetch accounts: " + e.getMessage()));
        }
    }

    @GetMapping("/user/{userId}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('BRANCH_MANAGER') or #userId == authentication.principal.id")
    public ResponseEntity<?> getAccountsByUserId(@PathVariable Long userId) {
        try {
            List<Account> accounts = accountService.getAccountsByUserId(userId);
            List<AccountDto> accountDtos = accounts.stream()
                    .map(this::convertToDto)
                    .collect(Collectors.toList());

            return ResponseEntity.ok(accountDtos);
        } catch (Exception e) {
            logger.error("Error fetching accounts for user: {}", userId, e);
            return ResponseEntity.badRequest()
                    .body(new ErrorResponse("Failed to fetch accounts: " + e.getMessage()));
        }
    }

    @GetMapping("/type/{type}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('BRANCH_MANAGER')")
    public ResponseEntity<?> getAccountsByType(@PathVariable String type) {
        try {
            AccountType accountType = AccountType.valueOf(type.toUpperCase());
            List<Account> accounts = accountService.getAccountsByType(accountType);
            List<AccountDto> accountDtos = accounts.stream()
                    .map(this::convertToDto)
                    .collect(Collectors.toList());

            return ResponseEntity.ok(accountDtos);
        } catch (Exception e) {
            logger.error("Error fetching accounts by type: {}", type, e);
            return ResponseEntity.badRequest()
                    .body(new ErrorResponse("Failed to fetch accounts: " + e.getMessage()));
        }
    }

    @GetMapping("/status/{status}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('BRANCH_MANAGER')")
    public ResponseEntity<?> getAccountsByStatus(@PathVariable String status) {
        try {
            AccountStatus accountStatus = AccountStatus.valueOf(status.toUpperCase());
            List<Account> accounts = accountService.getAccountsByStatus(accountStatus);
            List<AccountDto> accountDtos = accounts.stream()
                    .map(this::convertToDto)
                    .collect(Collectors.toList());

            return ResponseEntity.ok(accountDtos);
        } catch (Exception e) {
            logger.error("Error fetching accounts by status: {}", status, e);
            return ResponseEntity.badRequest()
                    .body(new ErrorResponse("Failed to fetch accounts: " + e.getMessage()));
        }
    }

    @GetMapping("/{id}/balance")
    @PreAuthorize("hasRole('ADMIN') or hasRole('BRANCH_MANAGER') or @accountService.getAccountById(#id).orElse(null)?.user?.id == authentication.principal.id")
    public ResponseEntity<?> getAccountBalance(@PathVariable Long id) {
        try {
            BigDecimal balance = accountService.getAccountBalance(id);
            return ResponseEntity.ok(new BalanceResponse(balance));
        } catch (Exception e) {
            logger.error("Error fetching account balance for ID: {}", id, e);
            return ResponseEntity.badRequest()
                    .body(new ErrorResponse("Failed to fetch balance: " + e.getMessage()));
        }
    }

    @PostMapping("/{id}/freeze")
    @PreAuthorize("hasRole('ADMIN') or hasRole('BRANCH_MANAGER')")
    public ResponseEntity<?> freezeAccount(@PathVariable Long id, @RequestBody FreezeAccountRequest request) {
        try {
            accountService.freezeAccount(id, request.getReason());
            return ResponseEntity.ok(new MessageResponse("Account frozen successfully"));
        } catch (Exception e) {
            logger.error("Error freezing account: {}", id, e);
            return ResponseEntity.badRequest()
                    .body(new ErrorResponse("Failed to freeze account: " + e.getMessage()));
        }
    }

    @PostMapping("/{id}/unfreeze")
    @PreAuthorize("hasRole('ADMIN') or hasRole('BRANCH_MANAGER')")
    public ResponseEntity<?> unfreezeAccount(@PathVariable Long id) {
        try {
            accountService.unfreezeAccount(id);
            return ResponseEntity.ok(new MessageResponse("Account unfrozen successfully"));
        } catch (Exception e) {
            logger.error("Error unfreezing account: {}", id, e);
            return ResponseEntity.badRequest()
                    .body(new ErrorResponse("Failed to unfreeze account: " + e.getMessage()));
        }
    }

    @PostMapping("/{id}/close")
    @PreAuthorize("hasRole('ADMIN') or hasRole('BRANCH_MANAGER')")
    public ResponseEntity<?> closeAccount(@PathVariable Long id, @RequestBody CloseAccountRequest request) {
        try {
            accountService.closeAccount(id, request.getReason());
            return ResponseEntity.ok(new MessageResponse("Account closed successfully"));
        } catch (Exception e) {
            logger.error("Error closing account: {}", id, e);
            return ResponseEntity.badRequest()
                    .body(new ErrorResponse("Failed to close account: " + e.getMessage()));
        }
    }

    private AccountDto convertToDto(Account account) {
        AccountDto dto = new AccountDto();
        dto.setId(account.getId());
        dto.setAccountNumber(account.getAccountNumber());
        dto.setAccountHolderName(account.getAccountHolderName());
        dto.setType(account.getType().toString());
        dto.setStatus(account.getStatus().toString());
        dto.setBalance(account.getBalance());
        dto.setAvailableBalance(account.getAvailableBalance());
        dto.setMinimumBalance(account.getMinimumBalance());
        dto.setContactNumber(account.getContactNumber());
        dto.setEmailAddress(account.getEmailAddress());
        dto.setAddress(account.getAddress());
        dto.setCreatedAt(account.getCreatedAt());
        dto.setUpdatedAt(account.getUpdatedAt());
        
        if (account.getUser() != null) {
            dto.setUserId(account.getUser().getId());
            dto.setUserName(account.getUser().getUsername());
        }
        
        return dto;
    }

    // Inner classes for request/response DTOs
    public static class BalanceResponse {
        private BigDecimal balance;
        
        public BalanceResponse(BigDecimal balance) {
            this.balance = balance;
        }
        
        public BigDecimal getBalance() { return balance; }
        public void setBalance(BigDecimal balance) { this.balance = balance; }
    }

    public static class FreezeAccountRequest {
        private String reason;
        
        public String getReason() { return reason; }
        public void setReason(String reason) { this.reason = reason; }
    }

    public static class CloseAccountRequest {
        private String reason;
        
        public String getReason() { return reason; }
        public void setReason(String reason) { this.reason = reason; }
    }
}
