package org.example.abcbanking.controller;

import org.example.abcbanking.dto.TransactionDto;
import org.example.abcbanking.dto.TransferRequest;
import org.example.abcbanking.dto.MessageResponse;
import org.example.abcbanking.dto.ErrorResponse;
import org.example.abcbanking.model.Transaction;
import org.example.abcbanking.model.TransactionStatus;
import org.example.abcbanking.model.TransactionType;
import org.example.abcbanking.service.TransactionService;
import org.example.abcbanking.service.impl.UserPrincipal;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/transactions")
@CrossOrigin(origins = "*", maxAge = 3600)
public class TransactionController {

    private static final Logger logger = LoggerFactory.getLogger(TransactionController.class);

    @Autowired
    private TransactionService transactionService;

    @PostMapping("/transfer")
    public ResponseEntity<?> transferFunds(@Valid @RequestBody TransferRequest request, Authentication authentication) {
        try {
            logger.info("Processing transfer request from account {} to account {}", 
                       request.getFromAccountId(), request.getToAccountId());

            Transaction transaction = transactionService.processTransfer(
                    request.getFromAccountId(),
                    request.getToAccountId(),
                    request.getAmount(),
                    request.getDescription()
            );

            TransactionDto transactionDto = convertToDto(transaction);
            return ResponseEntity.ok(transactionDto);

        } catch (Exception e) {
            logger.error("Transfer failed", e);
            return ResponseEntity.badRequest()
                    .body(new ErrorResponse("Transfer failed: " + e.getMessage()));
        }
    }

    @GetMapping("/my")
    public ResponseEntity<?> getMyTransactions(
            Authentication authentication,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "transactionDate") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            Long userId = userPrincipal.getId();

            Sort sort = sortDir.equalsIgnoreCase("desc") ? 
                    Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
            Pageable pageable = PageRequest.of(page, size, sort);

            // Get user's account transactions
            // Note: This would need to be enhanced to get user's account IDs first
            Page<Transaction> transactions = transactionService.getAllTransactions(pageable);
            Page<TransactionDto> transactionDtos = transactions.map(this::convertToDto);

            return ResponseEntity.ok(transactionDtos);

        } catch (Exception e) {
            logger.error("Error fetching user transactions", e);
            return ResponseEntity.badRequest()
                    .body(new ErrorResponse("Failed to fetch transactions: " + e.getMessage()));
        }
    }

    @GetMapping("/account/{accountId}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('BRANCH_MANAGER') or @accountService.getAccountById(#accountId).orElse(null)?.user?.id == authentication.principal.id")
    public ResponseEntity<?> getTransactionsByAccount(@PathVariable Long accountId) {
        try {
            List<Transaction> transactions = transactionService.getTransactionsByAccountId(accountId);
            List<TransactionDto> transactionDtos = transactions.stream()
                    .map(this::convertToDto)
                    .collect(Collectors.toList());

            return ResponseEntity.ok(transactionDtos);

        } catch (Exception e) {
            logger.error("Error fetching transactions for account: {}", accountId, e);
            return ResponseEntity.badRequest()
                    .body(new ErrorResponse("Failed to fetch transactions: " + e.getMessage()));
        }
    }

    @GetMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('BRANCH_MANAGER')")
    public ResponseEntity<?> getTransactionById(@PathVariable Long id) {
        try {
            Optional<Transaction> transaction = transactionService.getTransactionById(id);
            if (transaction.isPresent()) {
                TransactionDto transactionDto = convertToDto(transaction.get());
                return ResponseEntity.ok(transactionDto);
            } else {
                return ResponseEntity.notFound().build();
            }

        } catch (Exception e) {
            logger.error("Error fetching transaction by ID: {}", id, e);
            return ResponseEntity.badRequest()
                    .body(new ErrorResponse("Failed to fetch transaction: " + e.getMessage()));
        }
    }

    @GetMapping("/txn/{transactionId}")
    public ResponseEntity<?> getTransactionByTransactionId(@PathVariable String transactionId) {
        try {
            Optional<Transaction> transaction = transactionService.getTransactionByTransactionId(transactionId);
            if (transaction.isPresent()) {
                TransactionDto transactionDto = convertToDto(transaction.get());
                return ResponseEntity.ok(transactionDto);
            } else {
                return ResponseEntity.notFound().build();
            }

        } catch (Exception e) {
            logger.error("Error fetching transaction by transaction ID: {}", transactionId, e);
            return ResponseEntity.badRequest()
                    .body(new ErrorResponse("Failed to fetch transaction: " + e.getMessage()));
        }
    }

    @GetMapping("/all")
    @PreAuthorize("hasRole('ADMIN') or hasRole('BRANCH_MANAGER')")
    public ResponseEntity<?> getAllTransactions(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "transactionDate") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir) {
        try {
            Sort sort = sortDir.equalsIgnoreCase("desc") ? 
                    Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
            Pageable pageable = PageRequest.of(page, size, sort);

            Page<Transaction> transactions = transactionService.getAllTransactions(pageable);
            Page<TransactionDto> transactionDtos = transactions.map(this::convertToDto);

            return ResponseEntity.ok(transactionDtos);

        } catch (Exception e) {
            logger.error("Error fetching all transactions", e);
            return ResponseEntity.badRequest()
                    .body(new ErrorResponse("Failed to fetch transactions: " + e.getMessage()));
        }
    }

    @GetMapping("/type/{type}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('BRANCH_MANAGER')")
    public ResponseEntity<?> getTransactionsByType(@PathVariable String type) {
        try {
            TransactionType transactionType = TransactionType.valueOf(type.toUpperCase());
            List<Transaction> transactions = transactionService.getTransactionsByType(transactionType);
            List<TransactionDto> transactionDtos = transactions.stream()
                    .map(this::convertToDto)
                    .collect(Collectors.toList());

            return ResponseEntity.ok(transactionDtos);

        } catch (Exception e) {
            logger.error("Error fetching transactions by type: {}", type, e);
            return ResponseEntity.badRequest()
                    .body(new ErrorResponse("Failed to fetch transactions: " + e.getMessage()));
        }
    }

    @GetMapping("/status/{status}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('BRANCH_MANAGER')")
    public ResponseEntity<?> getTransactionsByStatus(@PathVariable String status) {
        try {
            TransactionStatus transactionStatus = TransactionStatus.valueOf(status.toUpperCase());
            List<Transaction> transactions = transactionService.getTransactionsByStatus(transactionStatus);
            List<TransactionDto> transactionDtos = transactions.stream()
                    .map(this::convertToDto)
                    .collect(Collectors.toList());

            return ResponseEntity.ok(transactionDtos);

        } catch (Exception e) {
            logger.error("Error fetching transactions by status: {}", status, e);
            return ResponseEntity.badRequest()
                    .body(new ErrorResponse("Failed to fetch transactions: " + e.getMessage()));
        }
    }

    @GetMapping("/date-range")
    @PreAuthorize("hasRole('ADMIN') or hasRole('BRANCH_MANAGER')")
    public ResponseEntity<?> getTransactionsByDateRange(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate) {
        try {
            List<Transaction> transactions = transactionService.getTransactionsByDateRange(startDate, endDate);
            List<TransactionDto> transactionDtos = transactions.stream()
                    .map(this::convertToDto)
                    .collect(Collectors.toList());

            return ResponseEntity.ok(transactionDtos);

        } catch (Exception e) {
            logger.error("Error fetching transactions by date range", e);
            return ResponseEntity.badRequest()
                    .body(new ErrorResponse("Failed to fetch transactions: " + e.getMessage()));
        }
    }

    @GetMapping("/search")
    @PreAuthorize("hasRole('ADMIN') or hasRole('BRANCH_MANAGER')")
    public ResponseEntity<?> searchTransactions(
            @RequestParam String searchTerm,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        try {
            Pageable pageable = PageRequest.of(page, size, Sort.by("transactionDate").descending());
            Page<Transaction> transactions = transactionService.searchTransactions(searchTerm, pageable);
            Page<TransactionDto> transactionDtos = transactions.map(this::convertToDto);

            return ResponseEntity.ok(transactionDtos);

        } catch (Exception e) {
            logger.error("Error searching transactions", e);
            return ResponseEntity.badRequest()
                    .body(new ErrorResponse("Failed to search transactions: " + e.getMessage()));
        }
    }

    @PostMapping("/{id}/complete")
    @PreAuthorize("hasRole('ADMIN') or hasRole('BRANCH_MANAGER')")
    public ResponseEntity<?> markTransactionAsCompleted(@PathVariable Long id) {
        try {
            transactionService.markTransactionAsCompleted(id);
            return ResponseEntity.ok(new MessageResponse("Transaction marked as completed"));

        } catch (Exception e) {
            logger.error("Error marking transaction as completed: {}", id, e);
            return ResponseEntity.badRequest()
                    .body(new ErrorResponse("Failed to complete transaction: " + e.getMessage()));
        }
    }

    @PostMapping("/{id}/fail")
    @PreAuthorize("hasRole('ADMIN') or hasRole('BRANCH_MANAGER')")
    public ResponseEntity<?> markTransactionAsFailed(@PathVariable Long id, @RequestBody FailTransactionRequest request) {
        try {
            transactionService.markTransactionAsFailed(id, request.getReason());
            return ResponseEntity.ok(new MessageResponse("Transaction marked as failed"));

        } catch (Exception e) {
            logger.error("Error marking transaction as failed: {}", id, e);
            return ResponseEntity.badRequest()
                    .body(new ErrorResponse("Failed to mark transaction as failed: " + e.getMessage()));
        }
    }

    @PostMapping("/{id}/reverse")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> reverseTransaction(@PathVariable Long id, @RequestBody ReverseTransactionRequest request) {
        try {
            transactionService.reverseTransaction(id, request.getReason());
            return ResponseEntity.ok(new MessageResponse("Transaction reversed successfully"));

        } catch (Exception e) {
            logger.error("Error reversing transaction: {}", id, e);
            return ResponseEntity.badRequest()
                    .body(new ErrorResponse("Failed to reverse transaction: " + e.getMessage()));
        }
    }

    private TransactionDto convertToDto(Transaction transaction) {
        TransactionDto dto = new TransactionDto();
        dto.setId(transaction.getId());
        dto.setTransactionId(transaction.getTransactionId());
        dto.setType(transaction.getType().toString());
        dto.setStatus(transaction.getStatus().toString());
        dto.setAmount(transaction.getAmount());
        dto.setTotalAmount(transaction.getTotalAmount());
        dto.setTransactionFee(transaction.getTransactionFee());
        dto.setTaxAmount(transaction.getTaxAmount());
        dto.setDescription(transaction.getDescription());
        dto.setTransactionDate(transaction.getTransactionDate());
        dto.setCompletedAt(transaction.getCompletedAt());
        
        if (transaction.getFromAccount() != null) {
            dto.setFromAccountId(transaction.getFromAccount().getId());
            dto.setFromAccountNumber(transaction.getFromAccount().getAccountNumber());
        }
        
        if (transaction.getToAccount() != null) {
            dto.setToAccountId(transaction.getToAccount().getId());
            dto.setToAccountNumber(transaction.getToAccount().getAccountNumber());
        }
        
        return dto;
    }

    // Inner classes for request DTOs
    public static class FailTransactionRequest {
        private String reason;
        
        public String getReason() { return reason; }
        public void setReason(String reason) { this.reason = reason; }
    }

    public static class ReverseTransactionRequest {
        private String reason;
        
        public String getReason() { return reason; }
        public void setReason(String reason) { this.reason = reason; }
    }
}
