package org.example.abcbanking.controller;

import org.example.abcbanking.dto.MessageResponse;
import org.example.abcbanking.dto.ErrorResponse;
import org.example.abcbanking.dto.UserDto;
import org.example.abcbanking.dto.ChangePasswordRequest;
import org.example.abcbanking.model.User;
import org.example.abcbanking.model.UserStatus;
import org.example.abcbanking.service.UserService;
import org.example.abcbanking.service.impl.UserPrincipal;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/users")
@CrossOrigin(origins = "*", maxAge = 3600)
public class UserController {

    private static final Logger logger = LoggerFactory.getLogger(UserController.class);

    @Autowired
    private UserService userService;

    @GetMapping("/profile")
    public ResponseEntity<?> getCurrentUserProfile(Authentication authentication) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            Long userId = userPrincipal.getId();

            Optional<User> user = userService.getUserById(userId);
            if (user.isPresent()) {
                UserDto userDto = convertToDto(user.get());
                return ResponseEntity.ok(userDto);
            } else {
                return ResponseEntity.notFound().build();
            }

        } catch (Exception e) {
            logger.error("Error fetching user profile", e);
            return ResponseEntity.badRequest()
                    .body(new ErrorResponse("Failed to fetch profile: " + e.getMessage()));
        }
    }

    @PutMapping("/profile")
    public ResponseEntity<?> updateCurrentUserProfile(@Valid @RequestBody UserDto userDto, Authentication authentication) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            Long userId = userPrincipal.getId();

            User user = convertFromDto(userDto);
            User updatedUser = userService.updateUser(userId, user);
            UserDto updatedUserDto = convertToDto(updatedUser);

            return ResponseEntity.ok(updatedUserDto);

        } catch (Exception e) {
            logger.error("Error updating user profile", e);
            return ResponseEntity.badRequest()
                    .body(new ErrorResponse("Failed to update profile: " + e.getMessage()));
        }
    }

    @PostMapping("/change-password")
    public ResponseEntity<?> changePassword(@Valid @RequestBody ChangePasswordRequest request, Authentication authentication) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            Long userId = userPrincipal.getId();

            userService.changePassword(userId, request.getOldPassword(), request.getNewPassword());
            return ResponseEntity.ok(new MessageResponse("Password changed successfully"));

        } catch (Exception e) {
            logger.error("Error changing password", e);
            return ResponseEntity.badRequest()
                    .body(new ErrorResponse("Failed to change password: " + e.getMessage()));
        }
    }

    @GetMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('BRANCH_MANAGER') or #id == authentication.principal.id")
    public ResponseEntity<?> getUserById(@PathVariable Long id) {
        try {
            Optional<User> user = userService.getUserById(id);
            if (user.isPresent()) {
                UserDto userDto = convertToDto(user.get());
                return ResponseEntity.ok(userDto);
            } else {
                return ResponseEntity.notFound().build();
            }

        } catch (Exception e) {
            logger.error("Error fetching user by ID: {}", id, e);
            return ResponseEntity.badRequest()
                    .body(new ErrorResponse("Failed to fetch user: " + e.getMessage()));
        }
    }

    @GetMapping("/all")
    @PreAuthorize("hasRole('ADMIN') or hasRole('BRANCH_MANAGER')")
    public ResponseEntity<?> getAllUsers(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "createdAt") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir) {
        try {
            Sort sort = sortDir.equalsIgnoreCase("desc") ? 
                    Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
            Pageable pageable = PageRequest.of(page, size, sort);

            Page<User> users = userService.getAllUsers(pageable);
            Page<UserDto> userDtos = users.map(this::convertToDto);

            return ResponseEntity.ok(userDtos);

        } catch (Exception e) {
            logger.error("Error fetching all users", e);
            return ResponseEntity.badRequest()
                    .body(new ErrorResponse("Failed to fetch users: " + e.getMessage()));
        }
    }

    @GetMapping("/search")
    @PreAuthorize("hasRole('ADMIN') or hasRole('BRANCH_MANAGER')")
    public ResponseEntity<?> searchUsers(
            @RequestParam String searchTerm,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        try {
            Pageable pageable = PageRequest.of(page, size, Sort.by("createdAt").descending());
            Page<User> users = userService.searchUsers(searchTerm, pageable);
            Page<UserDto> userDtos = users.map(this::convertToDto);

            return ResponseEntity.ok(userDtos);

        } catch (Exception e) {
            logger.error("Error searching users", e);
            return ResponseEntity.badRequest()
                    .body(new ErrorResponse("Failed to search users: " + e.getMessage()));
        }
    }

    @GetMapping("/status/{status}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('BRANCH_MANAGER')")
    public ResponseEntity<?> getUsersByStatus(@PathVariable String status) {
        try {
            UserStatus userStatus = UserStatus.valueOf(status.toUpperCase());
            List<User> users = userService.getUsersByStatus(userStatus);
            List<UserDto> userDtos = users.stream()
                    .map(this::convertToDto)
                    .collect(Collectors.toList());

            return ResponseEntity.ok(userDtos);

        } catch (Exception e) {
            logger.error("Error fetching users by status: {}", status, e);
            return ResponseEntity.badRequest()
                    .body(new ErrorResponse("Failed to fetch users: " + e.getMessage()));
        }
    }

    @PostMapping("/{id}/lock")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> lockUser(@PathVariable Long id) {
        try {
            userService.lockUser(id);
            return ResponseEntity.ok(new MessageResponse("User locked successfully"));

        } catch (Exception e) {
            logger.error("Error locking user: {}", id, e);
            return ResponseEntity.badRequest()
                    .body(new ErrorResponse("Failed to lock user: " + e.getMessage()));
        }
    }

    @PostMapping("/{id}/unlock")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> unlockUser(@PathVariable Long id) {
        try {
            userService.unlockUser(id);
            return ResponseEntity.ok(new MessageResponse("User unlocked successfully"));

        } catch (Exception e) {
            logger.error("Error unlocking user: {}", id, e);
            return ResponseEntity.badRequest()
                    .body(new ErrorResponse("Failed to unlock user: " + e.getMessage()));
        }
    }

    @PostMapping("/{id}/verify-email")
    @PreAuthorize("hasRole('ADMIN') or hasRole('BRANCH_MANAGER')")
    public ResponseEntity<?> verifyEmail(@PathVariable Long id) {
        try {
            userService.verifyEmail(id);
            return ResponseEntity.ok(new MessageResponse("Email verified successfully"));

        } catch (Exception e) {
            logger.error("Error verifying email for user: {}", id, e);
            return ResponseEntity.badRequest()
                    .body(new ErrorResponse("Failed to verify email: " + e.getMessage()));
        }
    }

    @PostMapping("/{id}/verify-phone")
    @PreAuthorize("hasRole('ADMIN') or hasRole('BRANCH_MANAGER')")
    public ResponseEntity<?> verifyPhone(@PathVariable Long id) {
        try {
            userService.verifyPhone(id);
            return ResponseEntity.ok(new MessageResponse("Phone verified successfully"));

        } catch (Exception e) {
            logger.error("Error verifying phone for user: {}", id, e);
            return ResponseEntity.badRequest()
                    .body(new ErrorResponse("Failed to verify phone: " + e.getMessage()));
        }
    }

    @PostMapping("/{id}/verify-kyc")
    @PreAuthorize("hasRole('ADMIN') or hasRole('BRANCH_MANAGER')")
    public ResponseEntity<?> verifyKyc(@PathVariable Long id) {
        try {
            userService.verifyKyc(id);
            return ResponseEntity.ok(new MessageResponse("KYC verified successfully"));

        } catch (Exception e) {
            logger.error("Error verifying KYC for user: {}", id, e);
            return ResponseEntity.badRequest()
                    .body(new ErrorResponse("Failed to verify KYC: " + e.getMessage()));
        }
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> deleteUser(@PathVariable Long id) {
        try {
            userService.deleteUser(id);
            return ResponseEntity.ok(new MessageResponse("User deleted successfully"));

        } catch (Exception e) {
            logger.error("Error deleting user: {}", id, e);
            return ResponseEntity.badRequest()
                    .body(new ErrorResponse("Failed to delete user: " + e.getMessage()));
        }
    }

    private UserDto convertToDto(User user) {
        UserDto dto = new UserDto();
        dto.setId(user.getId());
        dto.setUsername(user.getUsername());
        dto.setEmail(user.getEmail());
        dto.setFirstName(user.getFirstName());
        dto.setLastName(user.getLastName());
        dto.setPhoneNumber(user.getPhoneNumber());
        dto.setDateOfBirth(user.getDateOfBirth());
        dto.setAddress(user.getAddress());
        dto.setCity(user.getCity());
        dto.setState(user.getState());
        dto.setCountry(user.getCountry());
        dto.setPinCode(user.getPinCode());
        dto.setStatus(user.getStatus().toString());
        dto.setEmailVerified(user.isEmailVerified());
        dto.setPhoneVerified(user.isPhoneVerified());
        dto.setKycVerified(user.isKycVerified());
        dto.setCreatedAt(user.getCreatedAt());
        dto.setUpdatedAt(user.getUpdatedAt());
        dto.setLastLogin(user.getLastLogin());
        
        return dto;
    }

    private User convertFromDto(UserDto dto) {
        User user = new User();
        user.setFirstName(dto.getFirstName());
        user.setLastName(dto.getLastName());
        user.setEmail(dto.getEmail());
        user.setPhoneNumber(dto.getPhoneNumber());
        user.setDateOfBirth(dto.getDateOfBirth());
        user.setAddress(dto.getAddress());
        user.setCity(dto.getCity());
        user.setState(dto.getState());
        user.setCountry(dto.getCountry());
        user.setPinCode(dto.getPinCode());
        
        return user;
    }
}
