package org.example.abcbanking.controller;

import org.example.abcbanking.dto.*;
import org.example.abcbanking.model.*;
import org.example.abcbanking.service.NotificationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/notifications")
@CrossOrigin(origins = {"http://localhost:3000", "http://localhost:3001", "http://localhost:3002"})
public class NotificationController {
    
    private static final Logger logger = LoggerFactory.getLogger(NotificationController.class);
    
    @Autowired
    private NotificationService notificationService;
    
    // Get all notifications for current user
    @GetMapping
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<List<NotificationDto>> getUserNotifications(
            @RequestParam(required = false) Long userId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        try {
            // In real implementation, get userId from security context
            Long currentUserId = userId != null ? userId : 1L; // Default for testing
            
            Pageable pageable = PageRequest.of(page, size);
            Page<Notification> notifications = notificationService.getNotificationsByUserId(currentUserId, pageable);
            
            List<NotificationDto> notificationDtos = notifications.getContent().stream()
                    .map(this::convertToDto)
                    .collect(Collectors.toList());
            
            return ResponseEntity.ok(notificationDtos);
        } catch (Exception e) {
            logger.error("Error fetching user notifications", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    // Get unread notifications
    @GetMapping("/unread")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<List<NotificationDto>> getUnreadNotifications(@RequestParam(required = false) Long userId) {
        try {
            Long currentUserId = userId != null ? userId : 1L; // Default for testing
            
            List<Notification> notifications = notificationService.getUnreadNotificationsByUserId(currentUserId);
            List<NotificationDto> notificationDtos = notifications.stream()
                    .map(this::convertToDto)
                    .collect(Collectors.toList());
            
            return ResponseEntity.ok(notificationDtos);
        } catch (Exception e) {
            logger.error("Error fetching unread notifications", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    // Get unread count
    @GetMapping("/unread/count")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<Long> getUnreadCount(@RequestParam(required = false) Long userId) {
        try {
            Long currentUserId = userId != null ? userId : 1L; // Default for testing
            Long count = notificationService.countUnreadNotificationsByUserId(currentUserId);
            return ResponseEntity.ok(count);
        } catch (Exception e) {
            logger.error("Error fetching unread count", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    // Get notification by ID
    @GetMapping("/{notificationId}")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<NotificationDto> getNotification(@PathVariable Long notificationId) {
        try {
            Optional<Notification> notification = notificationService.getNotificationById(notificationId);
            if (notification.isPresent()) {
                return ResponseEntity.ok(convertToDto(notification.get()));
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            logger.error("Error fetching notification: {}", notificationId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    // Mark notification as read
    @PostMapping("/{notificationId}/read")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse> markAsRead(@PathVariable Long notificationId) {
        try {
            notificationService.markAsRead(notificationId);
            return ResponseEntity.ok(new ApiResponse("Notification marked as read", true));
        } catch (Exception e) {
            logger.error("Error marking notification as read: {}", notificationId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ApiResponse("Failed to mark notification as read: " + e.getMessage(), false));
        }
    }
    
    // Mark all notifications as read
    @PostMapping("/read-all")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse> markAllAsRead(@RequestParam(required = false) Long userId) {
        try {
            Long currentUserId = userId != null ? userId : 1L; // Default for testing
            notificationService.markAllAsReadByUserId(currentUserId);
            return ResponseEntity.ok(new ApiResponse("All notifications marked as read", true));
        } catch (Exception e) {
            logger.error("Error marking all notifications as read", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ApiResponse("Failed to mark all notifications as read: " + e.getMessage(), false));
        }
    }
    
    // Get notifications by type
    @GetMapping("/type/{type}")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<List<NotificationDto>> getNotificationsByType(
            @PathVariable NotificationType type,
            @RequestParam(required = false) Long userId) {
        try {
            Long currentUserId = userId != null ? userId : 1L; // Default for testing
            
            List<Notification> notifications = notificationService.getNotificationsByUserIdAndType(currentUserId, type);
            List<NotificationDto> notificationDtos = notifications.stream()
                    .map(this::convertToDto)
                    .collect(Collectors.toList());
            
            return ResponseEntity.ok(notificationDtos);
        } catch (Exception e) {
            logger.error("Error fetching notifications by type: {}", type, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    // Send test notification (for development/testing)
    @PostMapping("/test")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse> sendTestNotification(@RequestParam(required = false) Long userId) {
        try {
            Long currentUserId = userId != null ? userId : 1L; // Default for testing
            notificationService.sendTransactionAlert(currentUserId, "Test transaction of $100.00");
            return ResponseEntity.ok(new ApiResponse("Test notification sent", true));
        } catch (Exception e) {
            logger.error("Error sending test notification", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ApiResponse("Failed to send test notification: " + e.getMessage(), false));
        }
    }
    
    // Helper method to convert Notification to NotificationDto
    private NotificationDto convertToDto(Notification notification) {
        NotificationDto dto = new NotificationDto();
        dto.setId(notification.getId());
        dto.setTitle(notification.getTitle());
        dto.setMessage(notification.getMessage());
        dto.setType(notification.getType());
        dto.setPriority(notification.getPriority());
        dto.setStatus(notification.getStatus());
        dto.setIsRead(notification.getIsRead());
        dto.setReadAt(notification.getReadAt());
        dto.setSentAt(notification.getSentAt());
        dto.setScheduledAt(notification.getScheduledAt());
        dto.setExpiresAt(notification.getExpiresAt());
        dto.setChannel(notification.getChannel());
        dto.setActionUrl(notification.getActionUrl());
        dto.setActionText(notification.getActionText());
        dto.setCategory(notification.getCategory());
        dto.setReferenceId(notification.getReferenceId());
        dto.setReferenceType(notification.getReferenceType());
        dto.setUserId(notification.getUser().getId());
        dto.setCreatedAt(notification.getCreatedAt());
        dto.setUpdatedAt(notification.getUpdatedAt());
        
        return dto;
    }
}
