package org.example.abcbanking.controller;

import org.example.abcbanking.dto.DashboardStatsDto;
import org.example.abcbanking.dto.ErrorResponse;
import org.example.abcbanking.model.Account;
import org.example.abcbanking.model.Transaction;
import org.example.abcbanking.service.AccountService;
import org.example.abcbanking.service.TransactionService;
import org.example.abcbanking.service.UserService;
import org.example.abcbanking.service.impl.UserPrincipal;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/dashboard")
@CrossOrigin(origins = "*", maxAge = 3600)
public class DashboardController {

    private static final Logger logger = LoggerFactory.getLogger(DashboardController.class);

    @Autowired
    private AccountService accountService;

    @Autowired
    private TransactionService transactionService;

    @Autowired
    private UserService userService;

    @GetMapping("/customer")
    public ResponseEntity<?> getCustomerDashboard(Authentication authentication) {
        try {
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            Long userId = userPrincipal.getId();

            // Get user's accounts
            List<Account> accounts = accountService.getAccountsByUserId(userId);
            
            // Calculate total balance
            BigDecimal totalBalance = accounts.stream()
                    .map(Account::getBalance)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            // Get recent transactions (last 10)
            List<Transaction> recentTransactions = accounts.stream()
                    .flatMap(account -> transactionService.getTransactionsByAccountId(account.getId()).stream())
                    .sorted((t1, t2) -> t2.getTransactionDate().compareTo(t1.getTransactionDate()))
                    .limit(10)
                    .collect(Collectors.toList());

            // Calculate monthly spending (current month)
            LocalDateTime startOfMonth = LocalDateTime.now().withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0);
            LocalDateTime endOfMonth = LocalDateTime.now();
            
            List<Transaction> monthlyTransactions = transactionService.getTransactionsByDateRange(startOfMonth, endOfMonth);
            BigDecimal monthlySpending = monthlyTransactions.stream()
                    .filter(t -> accounts.stream().anyMatch(a -> a.getId().equals(t.getFromAccount() != null ? t.getFromAccount().getId() : null)))
                    .map(Transaction::getAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            Map<String, Object> dashboardData = new HashMap<>();
            dashboardData.put("accounts", accounts.stream().map(this::convertAccountToMap).collect(Collectors.toList()));
            dashboardData.put("recentTransactions", recentTransactions.stream().map(this::convertTransactionToMap).collect(Collectors.toList()));
            dashboardData.put("totalBalance", totalBalance);
            dashboardData.put("monthlySpending", monthlySpending);
            dashboardData.put("accountCount", accounts.size());
            dashboardData.put("savingsGoalProgress", 75.5); // Mock data - can be calculated based on user goals

            return ResponseEntity.ok(dashboardData);

        } catch (Exception e) {
            logger.error("Error fetching customer dashboard", e);
            return ResponseEntity.badRequest()
                    .body(new ErrorResponse("Failed to fetch dashboard data: " + e.getMessage()));
        }
    }

    @GetMapping("/admin")
    @PreAuthorize("hasRole('ADMIN') or hasRole('BRANCH_MANAGER')")
    public ResponseEntity<?> getAdminDashboard() {
        try {
            DashboardStatsDto stats = new DashboardStatsDto();
            
            // Get all accounts
            List<Account> allAccounts = accountService.getAllAccounts();
            stats.setTotalAccounts(allAccounts.size());
            
            // Calculate total balance across all accounts
            BigDecimal totalSystemBalance = allAccounts.stream()
                    .map(Account::getBalance)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            stats.setTotalBalance(totalSystemBalance);
            
            // Get all transactions
            List<Transaction> allTransactions = transactionService.getAllTransactions();
            stats.setTotalTransactions(allTransactions.size());
            
            // Get today's transactions
            LocalDateTime startOfDay = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0);
            LocalDateTime endOfDay = LocalDateTime.now().withHour(23).withMinute(59).withSecond(59);
            List<Transaction> todayTransactions = transactionService.getTransactionsByDateRange(startOfDay, endOfDay);
            stats.setDailyTransactions(todayTransactions.size());
            
            // Get all users
            stats.setTotalCustomers(userService.getAllUsers().size());
            
            // Calculate monthly growth (mock calculation)
            stats.setMonthlyGrowth(12.5); // This should be calculated based on actual data
            
            // Active users (users who logged in last 30 days) - mock data
            stats.setActiveUsers(85); // This should be calculated based on actual login data

            Map<String, Object> dashboardData = new HashMap<>();
            dashboardData.put("stats", stats);
            dashboardData.put("recentTransactions", allTransactions.stream()
                    .sorted((t1, t2) -> t2.getTransactionDate().compareTo(t1.getTransactionDate()))
                    .limit(10)
                    .map(this::convertTransactionToMap)
                    .collect(Collectors.toList()));
            dashboardData.put("recentAccounts", allAccounts.stream()
                    .sorted((a1, a2) -> a2.getCreatedAt().compareTo(a1.getCreatedAt()))
                    .limit(5)
                    .map(this::convertAccountToMap)
                    .collect(Collectors.toList()));

            return ResponseEntity.ok(dashboardData);

        } catch (Exception e) {
            logger.error("Error fetching admin dashboard", e);
            return ResponseEntity.badRequest()
                    .body(new ErrorResponse("Failed to fetch dashboard data: " + e.getMessage()));
        }
    }

    @GetMapping("/stats")
    @PreAuthorize("hasRole('ADMIN') or hasRole('BRANCH_MANAGER')")
    public ResponseEntity<?> getDashboardStats() {
        try {
            Map<String, Object> stats = new HashMap<>();
            
            // Account statistics
            List<Account> accounts = accountService.getAllAccounts();
            stats.put("totalAccounts", accounts.size());
            stats.put("totalBalance", accounts.stream()
                    .map(Account::getBalance)
                    .reduce(BigDecimal.ZERO, BigDecimal::add));
            
            // Transaction statistics
            List<Transaction> transactions = transactionService.getAllTransactions();
            stats.put("totalTransactions", transactions.size());
            
            // User statistics
            stats.put("totalUsers", userService.getAllUsers().size());
            
            // Today's statistics
            LocalDateTime startOfDay = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0);
            LocalDateTime endOfDay = LocalDateTime.now().withHour(23).withMinute(59).withSecond(59);
            List<Transaction> todayTransactions = transactionService.getTransactionsByDateRange(startOfDay, endOfDay);
            stats.put("todayTransactions", todayTransactions.size());
            stats.put("todayVolume", todayTransactions.stream()
                    .map(Transaction::getAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add));

            return ResponseEntity.ok(stats);

        } catch (Exception e) {
            logger.error("Error fetching dashboard stats", e);
            return ResponseEntity.badRequest()
                    .body(new ErrorResponse("Failed to fetch stats: " + e.getMessage()));
        }
    }

    private Map<String, Object> convertAccountToMap(Account account) {
        Map<String, Object> accountMap = new HashMap<>();
        accountMap.put("id", account.getId());
        accountMap.put("accountNumber", account.getAccountNumber());
        accountMap.put("accountHolderName", account.getAccountHolderName());
        accountMap.put("type", account.getType().toString());
        accountMap.put("status", account.getStatus().toString());
        accountMap.put("balance", account.getBalance());
        accountMap.put("availableBalance", account.getAvailableBalance());
        accountMap.put("createdAt", account.getCreatedAt());
        return accountMap;
    }

    private Map<String, Object> convertTransactionToMap(Transaction transaction) {
        Map<String, Object> transactionMap = new HashMap<>();
        transactionMap.put("id", transaction.getId());
        transactionMap.put("transactionId", transaction.getTransactionId());
        transactionMap.put("type", transaction.getType().toString());
        transactionMap.put("status", transaction.getStatus().toString());
        transactionMap.put("amount", transaction.getAmount());
        transactionMap.put("description", transaction.getDescription());
        transactionMap.put("transactionDate", transaction.getTransactionDate());
        
        if (transaction.getFromAccount() != null) {
            transactionMap.put("fromAccountNumber", transaction.getFromAccount().getAccountNumber());
        }
        if (transaction.getToAccount() != null) {
            transactionMap.put("toAccountNumber", transaction.getToAccount().getAccountNumber());
        }
        
        return transactionMap;
    }
}
