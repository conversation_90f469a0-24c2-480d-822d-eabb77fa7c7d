package org.example.abcbanking.controller;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/test")
public class TestController {

    @GetMapping("/health")
    public Map<String, Object> health() {
        Map<String, Object> response = new HashMap<>();
        response.put("status", "UP");
        response.put("message", "ABC Banking Enterprise is running!");
        response.put("timestamp", System.currentTimeMillis());
        response.put("service", "ABC Banking Backend");
        response.put("version", "1.0.0");
        return response;
    }

    @GetMapping("/info")
    public Map<String, Object> info() {
        Map<String, Object> response = new HashMap<>();
        response.put("application", "ABC Banking Enterprise");
        response.put("description", "Enterprise-grade banking management system");
        response.put("features", new String[]{
            "MySQL Database Integration",
            "Spring Boot 3.x Backend",
            "Next.js 15 Frontend",
            "JWT Authentication",
            "Role-Based Access Control",
            "Advanced Security",
            "Banking Operations",
            "Real-time Transactions"
        });
        response.put("technology", new String[]{
            "Spring Boot 3.2.0",
            "Java 17",
            "MySQL 8",
            "Next.js 15.4.4",
            "React 19",
            "TypeScript",
            "Tailwind CSS v4",
            "Framer Motion"
        });
        return response;
    }
} 