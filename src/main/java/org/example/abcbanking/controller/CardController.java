package org.example.abcbanking.controller;

import org.example.abcbanking.dto.*;
import org.example.abcbanking.model.*;
import org.example.abcbanking.service.CardService;
import org.example.abcbanking.service.AccountService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/cards")
@CrossOrigin(origins = {"http://localhost:3000", "http://localhost:3001", "http://localhost:3002"})
public class CardController {
    
    private static final Logger logger = LoggerFactory.getLogger(CardController.class);
    
    @Autowired
    private CardService cardService;
    
    @Autowired
    private AccountService accountService;
    
    // Get all cards for current user
    @GetMapping
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<List<CardDto>> getUserCards(@RequestParam(required = false) Long userId) {
        try {
            // In real implementation, get userId from security context
            Long currentUserId = userId != null ? userId : 1L; // Default for testing
            
            List<Card> cards = cardService.getCardsByUserId(currentUserId);
            List<CardDto> cardDtos = cards.stream()
                    .map(this::convertToDto)
                    .collect(Collectors.toList());
            
            return ResponseEntity.ok(cardDtos);
        } catch (Exception e) {
            logger.error("Error fetching user cards", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    // Get cards by account
    @GetMapping("/account/{accountId}")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<List<CardDto>> getCardsByAccount(@PathVariable Long accountId) {
        try {
            List<Card> cards = cardService.getCardsByAccountId(accountId);
            List<CardDto> cardDtos = cards.stream()
                    .map(this::convertToDto)
                    .collect(Collectors.toList());
            
            return ResponseEntity.ok(cardDtos);
        } catch (Exception e) {
            logger.error("Error fetching cards for account: {}", accountId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    // Get card by ID
    @GetMapping("/{cardId}")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<CardDto> getCard(@PathVariable Long cardId) {
        try {
            Optional<Card> card = cardService.getCardById(cardId);
            if (card.isPresent()) {
                return ResponseEntity.ok(convertToDto(card.get()));
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            logger.error("Error fetching card: {}", cardId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    // Create new card
    @PostMapping
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<CardDto> createCard(@Valid @RequestBody CreateCardRequest request) {
        try {
            // Validate account exists and belongs to user
            Optional<Account> account = accountService.getAccountById(request.getAccountId());
            if (!account.isPresent()) {
                return ResponseEntity.badRequest().build();
            }
            
            Card card = new Card();
            card.setCardHolderName(request.getCardHolderName());
            card.setCardType(request.getCardType());
            card.setAccount(account.get());
            card.setDailyLimit(request.getDailyLimit());
            card.setMonthlyLimit(request.getMonthlyLimit());
            card.setCreditLimit(request.getCreditLimit());
            card.setContactlessEnabled(request.getContactlessEnabled());
            card.setOnlineTransactionsEnabled(request.getOnlineTransactionsEnabled());
            card.setInternationalTransactionsEnabled(request.getInternationalTransactionsEnabled());
            card.setAtmTransactionsEnabled(request.getAtmTransactionsEnabled());
            card.setPosTransactionsEnabled(request.getPosTransactionsEnabled());
            card.setCardFeatures(request.getCardFeatures());
            
            Card createdCard = cardService.createCard(card);
            return ResponseEntity.status(HttpStatus.CREATED).body(convertToDto(createdCard));
            
        } catch (Exception e) {
            logger.error("Error creating card", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    // Update card
    @PutMapping("/{cardId}")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<CardDto> updateCard(@PathVariable Long cardId, 
                                            @Valid @RequestBody UpdateCardRequest request) {
        try {
            Optional<Card> existingCard = cardService.getCardById(cardId);
            if (!existingCard.isPresent()) {
                return ResponseEntity.notFound().build();
            }
            
            Card card = existingCard.get();
            card.setCardHolderName(request.getCardHolderName());
            card.setDailyLimit(request.getDailyLimit());
            card.setMonthlyLimit(request.getMonthlyLimit());
            card.setContactlessEnabled(request.getContactlessEnabled());
            card.setOnlineTransactionsEnabled(request.getOnlineTransactionsEnabled());
            card.setInternationalTransactionsEnabled(request.getInternationalTransactionsEnabled());
            card.setAtmTransactionsEnabled(request.getAtmTransactionsEnabled());
            card.setPosTransactionsEnabled(request.getPosTransactionsEnabled());
            
            Card updatedCard = cardService.updateCard(cardId, card);
            return ResponseEntity.ok(convertToDto(updatedCard));
            
        } catch (Exception e) {
            logger.error("Error updating card: {}", cardId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    // Block card
    @PostMapping("/{cardId}/block")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse> blockCard(@PathVariable Long cardId, 
                                               @RequestBody BlockCardRequest request) {
        try {
            cardService.blockCard(cardId, request.getReason(), request.getBlockedBy());
            return ResponseEntity.ok(new ApiResponse("Card blocked successfully", true));
        } catch (Exception e) {
            logger.error("Error blocking card: {}", cardId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ApiResponse("Failed to block card: " + e.getMessage(), false));
        }
    }
    
    // Unblock card
    @PostMapping("/{cardId}/unblock")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse> unblockCard(@PathVariable Long cardId) {
        try {
            cardService.unblockCard(cardId);
            return ResponseEntity.ok(new ApiResponse("Card unblocked successfully", true));
        } catch (Exception e) {
            logger.error("Error unblocking card: {}", cardId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ApiResponse("Failed to unblock card: " + e.getMessage(), false));
        }
    }
    
    // Activate card
    @PostMapping("/{cardId}/activate")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse> activateCard(@PathVariable Long cardId, 
                                                  @RequestBody ActivateCardRequest request) {
        try {
            cardService.activateCard(cardId, request.getPin());
            return ResponseEntity.ok(new ApiResponse("Card activated successfully", true));
        } catch (Exception e) {
            logger.error("Error activating card: {}", cardId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ApiResponse("Failed to activate card: " + e.getMessage(), false));
        }
    }
    
    // Change PIN
    @PostMapping("/{cardId}/change-pin")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse> changePin(@PathVariable Long cardId, 
                                               @RequestBody ChangePinRequest request) {
        try {
            cardService.changePin(cardId, request.getOldPin(), request.getNewPin());
            return ResponseEntity.ok(new ApiResponse("PIN changed successfully", true));
        } catch (Exception e) {
            logger.error("Error changing PIN for card: {}", cardId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ApiResponse("Failed to change PIN: " + e.getMessage(), false));
        }
    }
    
    // Set limits
    @PostMapping("/{cardId}/limits")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse> setLimits(@PathVariable Long cardId, 
                                               @RequestBody SetLimitsRequest request) {
        try {
            if (request.getDailyLimit() != null) {
                cardService.setDailyLimit(cardId, request.getDailyLimit());
            }
            if (request.getMonthlyLimit() != null) {
                cardService.setMonthlyLimit(cardId, request.getMonthlyLimit());
            }
            if (request.getCreditLimit() != null) {
                cardService.setCreditLimit(cardId, request.getCreditLimit());
            }
            
            return ResponseEntity.ok(new ApiResponse("Limits updated successfully", true));
        } catch (Exception e) {
            logger.error("Error setting limits for card: {}", cardId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ApiResponse("Failed to set limits: " + e.getMessage(), false));
        }
    }
    
    // Request replacement
    @PostMapping("/{cardId}/replace")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<CardDto> requestReplacement(@PathVariable Long cardId, 
                                                    @RequestBody ReplacementRequest request) {
        try {
            Card newCard = cardService.requestReplacement(cardId, request.getReason());
            return ResponseEntity.ok(convertToDto(newCard));
        } catch (Exception e) {
            logger.error("Error requesting replacement for card: {}", cardId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    // Report lost or stolen
    @PostMapping("/{cardId}/report")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<ApiResponse> reportLostOrStolen(@PathVariable Long cardId, 
                                                        @RequestBody ReportCardRequest request) {
        try {
            cardService.reportLostOrStolen(cardId, request.getReason());
            return ResponseEntity.ok(new ApiResponse("Card reported successfully", true));
        } catch (Exception e) {
            logger.error("Error reporting card: {}", cardId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new ApiResponse("Failed to report card: " + e.getMessage(), false));
        }
    }
    
    // Get card statistics
    @GetMapping("/stats")
    @PreAuthorize("hasRole('USER')")
    public ResponseEntity<CardStatsDto> getCardStats(@RequestParam(required = false) Long userId) {
        try {
            Long currentUserId = userId != null ? userId : 1L; // Default for testing
            
            Long activeCards = cardService.countActiveCardsByUserId(currentUserId);
            List<Card> userCards = cardService.getCardsByUserId(currentUserId);
            
            CardStatsDto stats = new CardStatsDto();
            stats.setTotalCards((long) userCards.size());
            stats.setActiveCards(activeCards);
            stats.setBlockedCards(userCards.stream()
                    .filter(Card::getIsBlocked)
                    .count());
            stats.setExpiringCards(cardService.getExpiringCards(30).stream()
                    .filter(card -> card.getAccount().getUser().getId().equals(currentUserId))
                    .count());
            
            return ResponseEntity.ok(stats);
        } catch (Exception e) {
            logger.error("Error fetching card statistics", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    // Helper method to convert Card to CardDto
    private CardDto convertToDto(Card card) {
        CardDto dto = new CardDto();
        dto.setId(card.getId());
        dto.setCardNumber(card.getCardNumber());
        dto.setMaskedCardNumber(card.getMaskedCardNumber());
        dto.setCardHolderName(card.getCardHolderName());
        dto.setCardType(card.getCardType());
        dto.setExpiryDate(card.getExpiryDate());
        dto.setStatus(card.getStatus());
        dto.setDailyLimit(card.getDailyLimit());
        dto.setMonthlyLimit(card.getMonthlyLimit());
        dto.setCreditLimit(card.getCreditLimit());
        dto.setAvailableCredit(card.getAvailableCredit());
        dto.setOutstandingBalance(card.getOutstandingBalance());
        dto.setIsBlocked(card.getIsBlocked());
        dto.setBlockReason(card.getBlockReason());
        dto.setBlockedDate(card.getBlockedDate());
        dto.setLastUsedDate(card.getLastUsedDate());
        dto.setActivationDate(card.getActivationDate());
        dto.setContactlessEnabled(card.getContactlessEnabled());
        dto.setOnlineTransactionsEnabled(card.getOnlineTransactionsEnabled());
        dto.setInternationalTransactionsEnabled(card.getInternationalTransactionsEnabled());
        dto.setAtmTransactionsEnabled(card.getAtmTransactionsEnabled());
        dto.setPosTransactionsEnabled(card.getPosTransactionsEnabled());
        dto.setRewardsPoints(card.getRewardsPoints());
        dto.setCashbackEarned(card.getCashbackEarned());
        dto.setAccountId(card.getAccount().getId());
        dto.setAccountName(card.getAccount().getAccountHolderName());
        
        return dto;
    }
}
