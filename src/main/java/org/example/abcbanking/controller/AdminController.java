package org.example.abcbanking.controller;

import org.example.abcbanking.dto.AdminDashboardResponse;
import org.example.abcbanking.dto.ApiResponse;
import org.example.abcbanking.model.*;
import org.example.abcbanking.repository.*;
import org.example.abcbanking.service.AdminService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/admin")
@CrossOrigin(origins = "*", maxAge = 3600)
@PreAuthorize("hasRole('ADMIN') or hasRole('BRANCH_MANAGER')")
public class AdminController {

    private static final Logger logger = LoggerFactory.getLogger(AdminController.class);

    @Autowired
    private AdminService adminService;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private AccountRepository accountRepository;

    @Autowired
    private TransactionRepository transactionRepository;

    @Autowired
    private CardRepository cardRepository;

    @Autowired
    private LoanRepository loanRepository;

    @Autowired
    private BranchRepository branchRepository;

    @Autowired
    private NotificationRepository notificationRepository;

    @Autowired
    private AuditLogRepository auditLogRepository;

    // ========================================
    // ADMIN DASHBOARD OVERVIEW
    // ========================================

    @GetMapping("/dashboard")
    public ResponseEntity<AdminDashboardResponse> getAdminDashboard() {
        try {
            logger.info("Fetching admin dashboard data");
            
            AdminDashboardResponse dashboard = new AdminDashboardResponse();
            
            // System Overview
            dashboard.setTotalCustomers(userRepository.countByRoles_Name(ERole.ROLE_CUSTOMER));
            dashboard.setTotalAccounts(accountRepository.count());
            dashboard.setTotalTransactions(transactionRepository.count());
            dashboard.setTotalCards(cardRepository.count());
            dashboard.setTotalLoans(loanRepository.count());
            dashboard.setTotalBranches(branchRepository.count());
            
            // Financial Overview
            BigDecimal totalDeposits = accountRepository.findAll().stream()
                .filter(account -> account.getType() == AccountType.SAVINGS || account.getType() == AccountType.CURRENT)
                .map(Account::getBalance)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            dashboard.setTotalDeposits(totalDeposits);
            
            BigDecimal totalLoansAmount = loanRepository.findAll().stream()
                .map(Loan::getLoanAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            dashboard.setTotalLoansAmount(totalLoansAmount);
            
            // Today's Statistics
            LocalDate today = LocalDate.now();
            LocalDateTime startOfDay = today.atStartOfDay();
            LocalDateTime endOfDay = today.plusDays(1).atStartOfDay();
            
            long todayTransactions = transactionRepository.countByTransactionDateBetween(startOfDay, endOfDay);
            dashboard.setTodayTransactions(todayTransactions);
            
            BigDecimal todayVolume = transactionRepository.findByTransactionDateBetween(startOfDay, endOfDay)
                .stream()
                .map(Transaction::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            dashboard.setTodayTransactionVolume(todayVolume);
            
            // Active vs Inactive Statistics
            long activeAccounts = accountRepository.countByStatus(AccountStatus.ACTIVE);
            long inactiveAccounts = accountRepository.count() - activeAccounts;
            dashboard.setActiveAccounts(activeAccounts);
            dashboard.setInactiveAccounts(inactiveAccounts);
            
            long activeCards = cardRepository.countByStatus(CardStatus.ACTIVE);
            long blockedCards = cardRepository.countByStatus(CardStatus.BLOCKED);
            dashboard.setActiveCards(activeCards);
            dashboard.setBlockedCards(blockedCards);
            
            // Loan Statistics
            long pendingLoans = loanRepository.countByStatus(LoanStatus.PENDING);
            long approvedLoans = loanRepository.countByStatus(LoanStatus.APPROVED);
            long rejectedLoans = loanRepository.countByStatus(LoanStatus.REJECTED);
            dashboard.setPendingLoans(pendingLoans);
            dashboard.setApprovedLoans(approvedLoans);
            dashboard.setRejectedLoans(rejectedLoans);
            
            // Recent Activity
            List<Transaction> recentTransactions = transactionRepository.findTop10ByOrderByTransactionDateDesc();
            dashboard.setRecentTransactions(recentTransactions);
            
            List<User> recentCustomers = userRepository.findTop10ByOrderByCreatedAtDesc();
            dashboard.setRecentCustomers(recentCustomers);
            
            // System Health
            dashboard.setSystemStatus("HEALTHY");
            dashboard.setLastUpdated(LocalDateTime.now());
            
            return ResponseEntity.ok(dashboard);
            
        } catch (Exception e) {
            logger.error("Error fetching admin dashboard", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    // ========================================
    // CUSTOMER MANAGEMENT
    // ========================================

    @GetMapping("/customers")
    public ResponseEntity<Page<User>> getAllCustomers(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(defaultValue = "createdAt") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir,
            @RequestParam(required = false) String search) {
        
        try {
            Sort sort = sortDir.equalsIgnoreCase("desc") ? 
                Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
            
            Pageable pageable = PageRequest.of(page, size, sort);
            
            Page<User> customers;
            if (search != null && !search.trim().isEmpty()) {
                customers = userRepository.findByFirstNameContainingIgnoreCaseOrLastNameContainingIgnoreCaseOrEmailContainingIgnoreCase(
                    search, search, search, pageable);
            } else {
                customers = userRepository.findByRoles_Name(ERole.ROLE_CUSTOMER, pageable);
            }
            
            return ResponseEntity.ok(customers);
            
        } catch (Exception e) {
            logger.error("Error fetching customers", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    @GetMapping("/customers/{customerId}")
    public ResponseEntity<User> getCustomerDetails(@PathVariable Long customerId) {
        try {
            Optional<User> customer = userRepository.findById(customerId);
            if (customer.isPresent()) {
                return ResponseEntity.ok(customer.get());
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            logger.error("Error fetching customer details", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    @GetMapping("/customers/{customerId}/accounts")
    public ResponseEntity<List<Account>> getCustomerAccounts(@PathVariable Long customerId) {
        try {
            List<Account> accounts = accountRepository.findByUserId(customerId);
            return ResponseEntity.ok(accounts);
        } catch (Exception e) {
            logger.error("Error fetching customer accounts", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    @GetMapping("/customers/{customerId}/transactions")
    public ResponseEntity<Page<Transaction>> getCustomerTransactions(
            @PathVariable Long customerId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        try {
            Pageable pageable = PageRequest.of(page, size, Sort.by("transactionDate").descending());
            Page<Transaction> transactions = transactionRepository.findByFromAccountUserIdOrToAccountUserId(
                customerId, customerId, pageable);
            return ResponseEntity.ok(transactions);
        } catch (Exception e) {
            logger.error("Error fetching customer transactions", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    // ========================================
    // ACCOUNT MANAGEMENT
    // ========================================

    @GetMapping("/accounts")
    public ResponseEntity<Page<Account>> getAllAccounts(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(defaultValue = "createdAt") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir,
            @RequestParam(required = false) AccountType type,
            @RequestParam(required = false) AccountStatus status) {
        
        try {
            Sort sort = sortDir.equalsIgnoreCase("desc") ? 
                Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
            
            Pageable pageable = PageRequest.of(page, size, sort);
            
            Page<Account> accounts;
            if (type != null && status != null) {
                accounts = accountRepository.findByTypeAndStatus(type, status, pageable);
            } else if (type != null) {
                accounts = accountRepository.findByType(type, pageable);
            } else if (status != null) {
                accounts = accountRepository.findByStatus(status, pageable);
            } else {
                accounts = accountRepository.findAll(pageable);
            }
            
            return ResponseEntity.ok(accounts);
            
        } catch (Exception e) {
            logger.error("Error fetching accounts", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    @PutMapping("/accounts/{accountId}/status")
    public ResponseEntity<ApiResponse> updateAccountStatus(
            @PathVariable Long accountId,
            @RequestParam AccountStatus status,
            @RequestParam(required = false) String reason) {
        
        try {
            Optional<Account> accountOpt = accountRepository.findById(accountId);
            if (accountOpt.isPresent()) {
                Account account = accountOpt.get();
                AccountStatus oldStatus = account.getStatus();
                account.setStatus(status);
                
                if (status == AccountStatus.FROZEN && reason != null) {
                    account.setFreezeReason(reason);
                    account.setFreezeDate(LocalDateTime.now());
                }
                
                accountRepository.save(account);
                
                // Log the action
                logger.info("Account {} status changed from {} to {} by admin", 
                    accountId, oldStatus, status);
                
                return ResponseEntity.ok(new ApiResponse("Account status updated successfully", true));
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            logger.error("Error updating account status", e);
            return ResponseEntity.internalServerError()
                .body(new ApiResponse("Failed to update account status", false));
        }
    }

    // ========================================
    // TRANSACTION MONITORING
    // ========================================

    @GetMapping("/transactions")
    public ResponseEntity<Page<Transaction>> getAllTransactions(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(defaultValue = "transactionDate") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String type) {
        
        try {
            Sort sort = sortDir.equalsIgnoreCase("desc") ? 
                Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
            
            Pageable pageable = PageRequest.of(page, size, sort);
            
            Page<Transaction> transactions;
            if (status != null && type != null) {
                transactions = transactionRepository.findByStatusAndType(
                    TransactionStatus.valueOf(status), TransactionType.valueOf(type), pageable);
            } else if (status != null) {
                transactions = transactionRepository.findByStatus(TransactionStatus.valueOf(status), pageable);
            } else if (type != null) {
                transactions = transactionRepository.findByType(TransactionType.valueOf(type), pageable);
            } else {
                transactions = transactionRepository.findAll(pageable);
            }
            
            return ResponseEntity.ok(transactions);
            
        } catch (Exception e) {
            logger.error("Error fetching transactions", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    @GetMapping("/transactions/suspicious")
    public ResponseEntity<List<Transaction>> getSuspiciousTransactions() {
        try {
            // Define criteria for suspicious transactions
            BigDecimal highAmountThreshold = new BigDecimal("100000"); // 1 Lakh INR
            LocalDateTime last24Hours = LocalDateTime.now().minusHours(24);
            
            List<Transaction> suspiciousTransactions = transactionRepository
                .findByAmountGreaterThanAndTransactionDateAfter(highAmountThreshold, last24Hours);
            
            return ResponseEntity.ok(suspiciousTransactions);
        } catch (Exception e) {
            logger.error("Error fetching suspicious transactions", e);
            return ResponseEntity.internalServerError().build();
        }
    }
}
