package org.example.abcbanking.config;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

/**
 * Configuration for external services like Redis, RabbitMQ, etc.
 * These services are optional and can be enabled/disabled based on profiles and properties.
 */
@Configuration
public class ExternalServicesConfig {

    /**
     * Redis configuration - only enabled in production or when explicitly configured
     */
    @Configuration
    @ConditionalOnProperty(
        name = "spring.data.redis.repositories.enabled", 
        havingValue = "true", 
        matchIfMissing = false
    )
    @Profile("!dev")
    public static class RedisConfig {
        // Redis configuration will be auto-configured by Spring Boot
        // when the conditions are met
    }

    /**
     * RabbitMQ configuration - only enabled in production or when explicitly configured
     */
    @Configuration
    @ConditionalOnProperty(
        name = "spring.rabbitmq.host", 
        matchIfMissing = false
    )
    @Profile("!dev")
    public static class RabbitMQConfig {
        // RabbitMQ configuration will be auto-configured by Spring Boot
        // when the conditions are met
    }

    /**
     * Email configuration - can be disabled for development
     */
    @Configuration
    @ConditionalOnProperty(
        name = "spring.mail.host", 
        matchIfMissing = true
    )
    public static class EmailConfig {
        // Email configuration will be auto-configured by Spring Boot
        // when the conditions are met
    }
}
