package org.example.abcbanking.config;

import org.example.abcbanking.model.ERole;
import org.example.abcbanking.model.Role;
import org.example.abcbanking.model.User;
import org.example.abcbanking.repository.RoleRepository;
import org.example.abcbanking.repository.UserRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.security.crypto.password.PasswordEncoder;

import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;

/**
 * Development data initializer
 * This class creates sample data for development and testing purposes
 */
@Configuration
@Profile("dev")
public class DevDataInitializer {

    private static final Logger logger = LoggerFactory.getLogger(DevDataInitializer.class);

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private RoleRepository roleRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Bean
    public CommandLineRunner initDevData() {
        return args -> {
            logger.info("=".repeat(50));
            logger.info("ABC Banking Enterprise - Development Mode");
            logger.info("=".repeat(50));
            logger.info("Application started successfully in DEVELOPMENT mode");
            logger.info("Database: H2 In-Memory Database");
            logger.info("H2 Console: http://localhost:8080/api/h2-console");
            logger.info("Swagger UI: http://localhost:8080/api/swagger-ui.html");
            logger.info("API Docs: http://localhost:8080/api/api-docs");
            logger.info("Health Check: http://localhost:8080/api/test/health");
            logger.info("=".repeat(50));
            logger.info("Default Admin Credentials:");
            logger.info("Username: admin");
            logger.info("Password: admin123");
            logger.info("=".repeat(50));
            
            // Initialize roles
            initializeRoles();

            // Initialize users
            initializeUsers();

            logger.info("Development data initialization completed");
        };
    }

    private void initializeRoles() {
        if (roleRepository.count() == 0) {
            logger.info("Creating default roles...");

            Role adminRole = new Role();
            adminRole.setName(ERole.ROLE_ADMIN);
            roleRepository.save(adminRole);

            Role customerRole = new Role();
            customerRole.setName(ERole.ROLE_CUSTOMER);
            roleRepository.save(customerRole);

            logger.info("Default roles created successfully");
        }
    }

    private void initializeUsers() {
        if (userRepository.count() == 0) {
            logger.info("Creating default users...");

            // Create admin user
            User admin = new User();
            admin.setUsername("admin");
            admin.setEmail("<EMAIL>");
            admin.setPassword(passwordEncoder.encode("admin123"));
            admin.setFirstName("Admin");
            admin.setLastName("User");
            admin.setAddress("123 Admin Street, Banking City, BC 12345");
            admin.setCreatedAt(LocalDateTime.now());

            Set<Role> adminRoles = new HashSet<>();
            adminRoles.add(roleRepository.findByName(ERole.ROLE_ADMIN).orElseThrow());
            adminRoles.add(roleRepository.findByName(ERole.ROLE_CUSTOMER).orElseThrow());
            admin.setRoles(adminRoles);

            userRepository.save(admin);

            // Create regular user
            User user = new User();
            user.setUsername("john.doe");
            user.setEmail("<EMAIL>");
            user.setPassword(passwordEncoder.encode("password123"));
            user.setFirstName("John");
            user.setLastName("Doe");
            user.setAddress("456 Customer Avenue, User City, UC 67890");
            user.setCreatedAt(LocalDateTime.now());

            Set<Role> userRoles = new HashSet<>();
            userRoles.add(roleRepository.findByName(ERole.ROLE_CUSTOMER).orElseThrow());
            user.setRoles(userRoles);

            userRepository.save(user);

            logger.info("Default users created successfully");
        }
    }
}
