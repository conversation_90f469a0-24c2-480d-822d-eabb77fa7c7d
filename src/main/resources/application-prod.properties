# ========================================
# PRODUCTION ENVIRONMENT CONFIGURATION
# ========================================
# NOTE: Override these values with environment variables in production

# ========================================
# SERVER CONFIGURATION
# ========================================
server.port=${SERVER_PORT:8080}
server.servlet.context-path=/api
server.error.include-message=never
server.error.include-binding-errors=never
server.error.include-stacktrace=never

# ========================================
# DATABASE CONFIGURATION (MySQL for Production)
# ========================================
spring.datasource.url=${DATABASE_URL:*********************************************************************************************************************************}
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.username=${DATABASE_USERNAME:abc_bank_user}
spring.datasource.password=${DATABASE_PASSWORD:SecurePassword123!}

# Connection Pool Configuration (HikariCP)
spring.datasource.hikari.connection-timeout=30000
spring.datasource.hikari.idle-timeout=600000
spring.datasource.hikari.max-lifetime=1800000
spring.datasource.hikari.maximum-pool-size=20
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.pool-name=ABCBankingHikariCP

# JPA Configuration
spring.jpa.database-platform=org.hibernate.dialect.MySQL8Dialect
spring.jpa.hibernate.ddl-auto=validate
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=false
spring.jpa.properties.hibernate.use_sql_comments=false
spring.jpa.properties.hibernate.jdbc.batch_size=25
spring.jpa.properties.hibernate.order_inserts=true
spring.jpa.properties.hibernate.order_updates=true
spring.jpa.properties.hibernate.jdbc.batch_versioned_data=true
spring.jpa.properties.hibernate.dialect.storage_engine=innodb
spring.jpa.properties.hibernate.connection.characterEncoding=utf8
spring.jpa.properties.hibernate.connection.CharSet=utf8
spring.jpa.properties.hibernate.connection.useUnicode=true

# H2 Database (Disabled in Production)
spring.h2.console.enabled=false

# ========================================
# JWT CONFIGURATION (Production)
# ========================================
jwt.secret=${JWT_SECRET:your-super-secret-jwt-key-here-make-it-very-long-and-secure-for-production-use}
jwt.expiration=${JWT_EXPIRATION:86400000}
jwt.refresh-expiration=${JWT_REFRESH_EXPIRATION:*********}
jwt.header=Authorization
jwt.prefix=Bearer

# ========================================
# SECURITY CONFIGURATION (Production)
# ========================================
spring.security.user.name=${ADMIN_USERNAME:admin}
spring.security.user.password=${ADMIN_PASSWORD:admin123}

# ========================================
# LOGGING CONFIGURATION (Production)
# ========================================
logging.level.org.springframework.security=WARN
logging.level.org.example.abcbanking=INFO
logging.level.org.hibernate.SQL=WARN
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=WARN
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} - %msg%n

# ========================================
# SWAGGER/OPENAPI CONFIGURATION (Disabled in Production)
# ========================================
springdoc.api-docs.enabled=false
springdoc.swagger-ui.enabled=false

# ========================================
# ACTUATOR CONFIGURATION (Limited in Production)
# ========================================
management.endpoints.web.exposure.include=health,info,metrics,prometheus
management.endpoint.health.show-details=when-authorized
management.metrics.export.prometheus.enabled=true

# ========================================
# CACHE CONFIGURATION (Caffeine for Production)
# ========================================
spring.cache.type=caffeine
spring.cache.caffeine.spec=maximumSize=500,expireAfterWrite=600s

# ========================================
# ASYNC CONFIGURATION
# ========================================
spring.task.execution.pool.core-size=5
spring.task.execution.pool.max-size=20
spring.task.execution.pool.queue-capacity=100
spring.task.execution.thread-name-prefix=bank-async-

# ========================================
# FILE UPLOAD CONFIGURATION
# ========================================
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB

# ========================================
# CORS CONFIGURATION
# ========================================
cors.allowed-origins=${CORS_ALLOWED_ORIGINS:http://localhost:3000,http://localhost:3001}
cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS
cors.allowed-headers=*
cors.allow-credentials=true

# ========================================
# BUSINESS LOGIC CONFIGURATION
# ========================================
# Transaction limits
bank.transaction.max-amount=${BANK_TRANSACTION_MAX_AMOUNT:1000000}
bank.transaction.daily-limit=${BANK_TRANSACTION_DAILY_LIMIT:50000}
bank.transaction.minimum-amount=${BANK_TRANSACTION_MIN_AMOUNT:1}

# Interest rates
bank.interest.savings=${BANK_INTEREST_SAVINGS:2.5}
bank.interest.current=${BANK_INTEREST_CURRENT:0.5}
bank.interest.loan.min=${BANK_INTEREST_LOAN_MIN:8.0}
bank.interest.loan.max=${BANK_INTEREST_LOAN_MAX:15.0}

# Account limits
bank.account.minimum-balance.savings=${BANK_ACCOUNT_MIN_BALANCE_SAVINGS:1000}
bank.account.minimum-balance.current=${BANK_ACCOUNT_MIN_BALANCE_CURRENT:5000}
bank.account.overdraft-limit.max=${BANK_ACCOUNT_OVERDRAFT_LIMIT_MAX:50000}

# Loan configuration
bank.loan.max-amount=${BANK_LOAN_MAX_AMOUNT:********}
bank.loan.max-tenure=${BANK_LOAN_MAX_TENURE:360}
bank.loan.processing-fee=${BANK_LOAN_PROCESSING_FEE:1.0}

# ========================================
# EMAIL CONFIGURATION (Production)
# ========================================
spring.mail.host=${MAIL_HOST:smtp.gmail.com}
spring.mail.port=${MAIL_PORT:587}
spring.mail.username=${MAIL_USERNAME:<EMAIL>}
spring.mail.password=${MAIL_PASSWORD:your-app-password}
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true

# ========================================
# SCHEDULING CONFIGURATION
# ========================================
bank.scheduling.interest-credit.cron=${BANK_SCHEDULING_INTEREST_CRON:0 0 1 * * *}
bank.scheduling.loan-emi.cron=${BANK_SCHEDULING_LOAN_EMI_CRON:0 0 5 * * *}
bank.scheduling.reports.cron=${BANK_SCHEDULING_REPORTS_CRON:0 0 6 * * 1}

# ========================================
# EXTERNAL SERVICES (Production)
# ========================================
# Redis Configuration
spring.data.redis.host=${REDIS_HOST:localhost}
spring.data.redis.port=${REDIS_PORT:6379}
spring.data.redis.password=${REDIS_PASSWORD:}
spring.data.redis.timeout=2000ms

# RabbitMQ Configuration
spring.rabbitmq.host=${RABBITMQ_HOST:localhost}
spring.rabbitmq.port=${RABBITMQ_PORT:5672}
spring.rabbitmq.username=${RABBITMQ_USERNAME:guest}
spring.rabbitmq.password=${RABBITMQ_PASSWORD:guest}
