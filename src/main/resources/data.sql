-- ========================================
-- ABC BANKING - INDIAN TEST DATA
-- ========================================
-- This file contains realistic Indian banking test data
-- Currency: INR (Indian Rupees)
-- All amounts are in INR

-- ========================================
-- ROLES DATA
-- ========================================
INSERT IGNORE INTO roles (id, name, description, permissions, is_active, created_at, updated_at, created_by, updated_by, version, is_deleted) VALUES
(1, 'ROLE_CUSTOMER', 'Regular bank customer', 'ACCOUNT_VIEW,TRANSACTION_CREATE,CARD_MANAGE', true, NOW(), NOW(), 'SYSTEM', 'SYSTEM', 0, false),
(2, 'ROLE_ADMIN', 'Bank administrator', 'ALL_PERMISSIONS', true, NOW(), NOW(), 'SYSTEM', 'SYSTEM', 0, false),
(3, 'ROLE_BRANCH_MANAGER', 'Bank branch manager', 'CUSTOMER_VIEW,ACCOUNT_MANAGE,TRANSACTION_APPROVE,LOAN_APPROVE', true, NOW(), NOW(), 'SYSTEM', 'SYSTEM', 0, false),
(4, 'ROLE_LOAN_OFFICER', 'Loan officer', 'LOAN_VIEW,LOAN_PROCESS,CUSTOMER_VIEW', true, NOW(), NOW(), 'SYSTEM', 'SYSTEM', 0, false);

-- ========================================
-- BRANCHES DATA (Indian Cities)
-- ========================================
INSERT IGNORE INTO branches (id, branch_code, name, address, city, state, pin_code, phone_number, email, ifsc_code, micr_code, status, created_at, updated_at, created_by, updated_by, version, is_deleted) VALUES
(1, 'ABC001', 'Mumbai Central Branch', 'Plot No. 123, Nariman Point', 'Mumbai', 'Maharashtra', '400001', '+91-22-********', '<EMAIL>', 'A**********', '*********', 'ACTIVE', NOW(), NOW(), 'SYSTEM', 'SYSTEM', 0, false),
(2, 'ABC002', 'Delhi Connaught Place', 'Block A, Connaught Place', 'New Delhi', 'Delhi', '110001', '+91-11-********', '<EMAIL>', 'ABCB0000002', '*********', 'ACTIVE', NOW(), NOW(), 'SYSTEM', 'SYSTEM', 0, false),
(3, 'ABC003', 'Bangalore Koramangala', '5th Block, Koramangala', 'Bangalore', 'Karnataka', '560034', '+91-80-33445566', '<EMAIL>', 'ABCB0000003', '560002003', 'ACTIVE', NOW(), NOW(), 'SYSTEM', 'SYSTEM', 0, false),
(4, 'ABC004', 'Pune Baner', 'Baner Road, Pune', 'Pune', 'Maharashtra', '411045', '+91-20-22334455', '<EMAIL>', 'ABCB0000004', '411002004', 'ACTIVE', NOW(), NOW(), 'SYSTEM', 'SYSTEM', 0, false),
(5, 'ABC005', 'Chennai Anna Nagar', 'Anna Nagar West', 'Chennai', 'Tamil Nadu', '600040', '+91-44-********', '<EMAIL>', 'ABCB0000005', '*********', 'ACTIVE', NOW(), NOW(), 'SYSTEM', 'SYSTEM', 0, false);

-- ========================================
-- USERS DATA (Indian Names and Details)
-- ========================================
INSERT IGNORE INTO users (id, username, password, email, first_name, last_name, phone_number, date_of_birth, address, city, state, pin_code, aadhaar_number, pan_number, status, failed_login_attempts, account_locked_until, password_changed_at, created_at, updated_at, created_by, updated_by, version, is_deleted) VALUES
(1, 'vedant.heda', '$2a$10$N.zmdr9k7uOsU5uwZ/vHuOKgOoGfZr1XJbr4QzjQU5PYjhp.x/Eim', '<EMAIL>', 'Vedant', 'Heda', '+91-**********', '1995-08-15', 'Flat 301, Skyline Apartments, Baner', 'Pune', 'Maharashtra', '411045', '************', '**********', 'ACTIVE', 0, NULL, NOW(), NOW(), NOW(), 'SYSTEM', 'SYSTEM', 0, false),
(2, 'priya.sharma', '$2a$10$N.zmdr9k7uOsU5uwZ/vHuOKgOoGfZr1XJbr4QzjQU5PYjhp.x/Eim', '<EMAIL>', 'Priya', 'Sharma', '+91-**********', '1988-03-22', 'B-204, Green Valley Society, Andheri', 'Mumbai', 'Maharashtra', '400058', '***********3', '**********', 'ACTIVE', 0, NULL, NOW(), NOW(), NOW(), 'SYSTEM', 'SYSTEM', 0, false),
(3, 'rajesh.kumar', '$2a$10$N.zmdr9k7uOsU5uwZ/vHuOKgOoGfZr1XJbr4QzjQU5PYjhp.x/Eim', '<EMAIL>', 'Rajesh', 'Kumar', '+91-**********', '1985-11-10', 'House No. 45, Sector 15, Gurgaon', 'Gurgaon', 'Haryana', '122001', '************', 'CDEFG3456H', 'ACTIVE', 0, NULL, NOW(), NOW(), NOW(), 'SYSTEM', 'SYSTEM', 0, false),
(4, 'anita.patel', '$2a$10$N.zmdr9k7uOsU5uwZ/vHuOKgOoGfZr1XJbr4QzjQU5PYjhp.x/Eim', '<EMAIL>', 'Anita', 'Patel', '+91-9876543213', '1992-07-05', '12/A, Satellite Road, Ahmedabad', 'Ahmedabad', 'Gujarat', '380015', '***********5', 'DEFGH4567I', 'ACTIVE', 0, NULL, NOW(), NOW(), NOW(), 'SYSTEM', 'SYSTEM', 0, false),
(5, 'suresh.reddy', '$2a$10$N.zmdr9k7uOsU5uwZ/vHuOKgOoGfZr1XJbr4QzjQU5PYjhp.x/Eim', '<EMAIL>', 'Suresh', 'Reddy', '+91-9876543214', '1990-12-18', 'Plot 67, Jubilee Hills, Hyderabad', 'Hyderabad', 'Telangana', '500033', '***********6', 'EFGHI5678J', 'ACTIVE', 0, NULL, NOW(), NOW(), NOW(), 'SYSTEM', 'SYSTEM', 0, false);

-- ========================================
-- USER ROLES MAPPING
-- ========================================
INSERT IGNORE INTO user_roles (user_id, role_id) VALUES
(1, 2), -- Vedant Heda - Premium Customer
(2, 1), -- Priya Sharma - Regular Customer
(3, 1), -- Rajesh Kumar - Regular Customer
(4, 2), -- Anita Patel - Premium Customer
(5, 1); -- Suresh Reddy - Regular Customer

-- ========================================
-- ACCOUNTS DATA (Indian Banking Account Types)
-- ========================================
INSERT IGNORE INTO accounts (id, account_number, account_holder_name, type, balance, currency, minimum_balance, overdraft_limit, interest_rate, status, opening_date, last_transaction_date, user_id, branch_id, created_at, updated_at, created_by, updated_by, version, is_deleted) VALUES
-- Vedant Heda's Accounts
(1, '***********', 'Vedant Heda Savings', 'SAVINGS', 125000.00, 'INR', 1000.00, 0.00, 3.5, 'ACTIVE', '2023-01-15', NOW(), 1, 4, NOW(), NOW(), 'SYSTEM', 'SYSTEM', 0, false),
(2, '***********', 'Vedant Heda Current', 'CURRENT', 85000.00, 'INR', 10000.00, 50000.00, 0.0, 'ACTIVE', '2023-02-20', NOW(), 1, 4, NOW(), NOW(), 'SYSTEM', 'SYSTEM', 0, false),

-- Priya Sharma's Accounts
(3, '***********', 'Priya Sharma Savings', 'SAVINGS', 75000.00, 'INR', 1000.00, 0.00, 3.5, 'ACTIVE', true, '2022-06-10', NOW(), 2, 1, NOW(), NOW(), 'SYSTEM', 'SYSTEM', 0, false),
(4, '***********', 'Priya Sharma Salary', 'SALARY', 45000.00, 'INR', 0.00, 0.00, 3.5, 'ACTIVE', true, '2022-07-01', NOW(), 2, 1, NOW(), NOW(), 'SYSTEM', 'SYSTEM', 0, false),

-- Rajesh Kumar's Accounts
(5, '***********', 'Rajesh Kumar Savings', 'SAVINGS', 95000.00, 'INR', 1000.00, 0.00, 3.5, 'ACTIVE', true, '2021-03-15', NOW(), 3, 2, NOW(), NOW(), 'SYSTEM', 'SYSTEM', 0, false),

-- Anita Patel's Accounts
(6, '***********', 'Anita Patel Savings', 'SAVINGS', 150000.00, 'INR', 1000.00, 0.00, 3.5, 'ACTIVE', true, '2023-05-20', NOW(), 4, 1, NOW(), NOW(), 'SYSTEM', 'SYSTEM', 0, false),
(7, '***********', 'Anita Patel Business', 'CURRENT', 250000.00, 'INR', 10000.00, 100000.00, 0.0, 'ACTIVE', true, '2023-06-01', NOW(), 4, 1, NOW(), NOW(), 'SYSTEM', 'SYSTEM', 0, false),

-- Suresh Reddy's Accounts
(8, '***********', 'Suresh Reddy Savings', 'SAVINGS', 65000.00, 'INR', 1000.00, 0.00, 3.5, 'ACTIVE', true, '2022-09-10', NOW(), 5, 5, NOW(), NOW(), 'SYSTEM', 'SYSTEM', 0, false);

-- ========================================
-- CARDS DATA (Indian Banking Cards)
-- ========================================
INSERT IGNORE INTO cards (id, card_number, card_holder_name, card_type, expiry_date, cvv_hash, status, daily_limit, monthly_limit, credit_limit, available_credit, outstanding_balance, is_blocked, pin_hash, pin_attempts, contactless_enabled, online_transactions_enabled, international_transactions_enabled, atm_transactions_enabled, pos_transactions_enabled, rewards_points, cashback_earned, account_id, activation_date, created_at, updated_at, created_by, updated_by, version, is_deleted) VALUES
-- Vedant Heda's Cards
(1, '4532************', 'VEDANT HEDA', 'DEBIT', '2027-08-31', '$2a$10$encrypted_cvv_hash', 'ACTIVE', 50000.00, 200000.00, 0.00, 0.00, 0.00, false, '$2a$10$encrypted_pin_hash', 0, true, true, true, true, true, 1250, 850.00, 1, '2023-01-20', NOW(), NOW(), 'SYSTEM', 'SYSTEM', 0, false),
(2, '5555**********98', 'VEDANT HEDA', 'CREDIT', '2028-02-28', '$2a$10$encrypted_cvv_hash', 'ACTIVE', 100000.00, 500000.00, 300000.00, 285000.00, 15000.00, false, '$2a$10$encrypted_pin_hash', 0, true, true, true, true, true, 3500, 1200.00, 2, '2023-02-25', NOW(), NOW(), 'SYSTEM', 'SYSTEM', 0, false),

-- Priya Sharma's Cards
(3, '****************', 'PRIYA SHARMA', 'DEBIT', '2026-12-31', '$2a$10$encrypted_cvv_hash', 'ACTIVE', 50000.00, 200000.00, 0.00, 0.00, 0.00, false, '$2a$10$encrypted_pin_hash', 0, true, true, false, true, true, 750, 450.00, 3, '2022-06-15', NOW(), NOW(), 'SYSTEM', 'SYSTEM', 0, false),

-- Rajesh Kumar's Cards
(4, '****************', 'RAJESH KUMAR', 'DEBIT', '2027-03-31', '$2a$10$encrypted_cvv_hash', 'ACTIVE', 50000.00, 200000.00, 0.00, 0.00, 0.00, false, '$2a$10$encrypted_pin_hash', 0, true, true, false, true, true, 950, 320.00, 5, '2021-03-20', NOW(), NOW(), 'SYSTEM', 'SYSTEM', 0, false),

-- Anita Patel's Cards
(5, '****************', 'ANITA PATEL', 'DEBIT', '2028-05-31', '$2a$10$encrypted_cvv_hash', 'ACTIVE', 50000.00, 200000.00, 0.00, 0.00, 0.00, false, '$2a$10$encrypted_pin_hash', 0, true, true, true, true, true, 1800, 950.00, 6, '2023-05-25', NOW(), NOW(), 'SYSTEM', 'SYSTEM', 0, false),
(6, '****************', 'ANITA PATEL', 'CREDIT', '2029-06-30', '$2a$10$encrypted_cvv_hash', 'ACTIVE', 150000.00, 750000.00, 500000.00, 475000.00, 25000.00, false, '$2a$10$encrypted_pin_hash', 0, true, true, true, true, true, 5200, 2100.00, 7, '2023-06-05', NOW(), NOW(), 'SYSTEM', 'SYSTEM', 0, false),

-- Suresh Reddy's Cards
(7, '****************', 'SURESH REDDY', 'DEBIT', '2027-09-30', '$2a$10$encrypted_cvv_hash', 'ACTIVE', 50000.00, 200000.00, 0.00, 0.00, 0.00, false, '$2a$10$encrypted_pin_hash', 0, true, true, false, true, true, 650, 280.00, 8, '2022-09-15', NOW(), NOW(), 'SYSTEM', 'SYSTEM', 0, false);

-- ========================================
-- TRANSACTIONS DATA (Recent Indian Banking Transactions)
-- ========================================
INSERT IGNORE INTO transactions (id, transaction_id, transaction_type, amount, currency, description, status, transaction_date, processed_date, from_account_id, to_account_id, reference_number, category, fee_amount, tax_amount, exchange_rate, channel, location, device_info, ip_address, user_id, approved_by, card_id, created_at, updated_at, created_by, updated_by, version, is_deleted) VALUES
-- Vedant Heda's Transactions
(1, 'TXN202401001', 'CREDIT', 50000.00, 'INR', 'Salary Credit - Tech Corp India', 'COMPLETED', '2024-01-01 09:30:00', '2024-01-01 09:30:00', NULL, 1, 'SAL202401001', 'SALARY', 0.00, 0.00, 1.0, 'NEFT', 'Pune', 'Mobile App', '*************', 1, NULL, NULL, NOW(), NOW(), 'SYSTEM', 'SYSTEM', 0, false),
(2, 'TXN202401002', 'DEBIT', 2500.00, 'INR', 'ATM Withdrawal - ABC Bank ATM Baner', 'COMPLETED', '2024-01-02 14:15:00', '2024-01-02 14:15:00', 1, NULL, 'ATM202401002', 'CASH_WITHDRAWAL', 5.00, 0.90, 1.0, 'ATM', 'Pune', 'ATM Terminal', '*************', 1, NULL, 1, NOW(), NOW(), 'SYSTEM', 'SYSTEM', 0, false),
(3, 'TXN202401003', 'DEBIT', 1200.00, 'INR', 'Online Shopping - Amazon India', 'COMPLETED', '2024-01-03 16:45:00', '2024-01-03 16:45:00', 1, NULL, 'AMZ202401003', 'SHOPPING', 0.00, 216.00, 1.0, 'ONLINE', 'Pune', 'Web Browser', '*************', 1, NULL, 1, NOW(), NOW(), 'SYSTEM', 'SYSTEM', 0, false),
(4, 'TXN202401004', 'TRANSFER', 5000.00, 'INR', 'Transfer to Priya Sharma', 'COMPLETED', '2024-01-04 11:20:00', '2024-01-04 11:20:00', 1, 3, 'TRF202401004', 'PERSONAL', 2.50, 0.45, 1.0, 'IMPS', 'Pune', 'Mobile App', '192.168.1.103', 1, NULL, NULL, NOW(), NOW(), 'SYSTEM', 'SYSTEM', 0, false),
(5, 'TXN202401005', 'DEBIT', 850.00, 'INR', 'Swiggy Food Order', 'COMPLETED', '2024-01-05 20:30:00', '2024-01-05 20:30:00', 1, NULL, 'SWG202401005', 'FOOD', 0.00, 153.00, 1.0, 'UPI', 'Pune', 'Mobile App', '192.168.1.104', 1, NULL, 1, NOW(), NOW(), 'SYSTEM', 'SYSTEM', 0, false),

-- Priya Sharma's Transactions
(6, 'TXN202401006', 'CREDIT', 45000.00, 'INR', 'Salary Credit - Mumbai Finance Ltd', 'COMPLETED', '2024-01-01 10:00:00', '2024-01-01 10:00:00', NULL, 3, 'SAL202401006', 'SALARY', 0.00, 0.00, 1.0, 'NEFT', 'Mumbai', 'Bank Transfer', '192.168.1.105', 2, NULL, NULL, NOW(), NOW(), 'SYSTEM', 'SYSTEM', 0, false),
(7, 'TXN202401007', 'DEBIT', 15000.00, 'INR', 'Rent Payment - Landlord', 'COMPLETED', '2024-01-05 09:00:00', '2024-01-05 09:00:00', 3, NULL, 'RENT202401007', 'RENT', 0.00, 0.00, 1.0, 'NEFT', 'Mumbai', 'Net Banking', '192.168.1.106', 2, NULL, NULL, NOW(), NOW(), 'SYSTEM', 'SYSTEM', 0, false),
(8, 'TXN202401008', 'CREDIT', 5000.00, 'INR', 'Transfer from Vedant Heda', 'COMPLETED', '2024-01-04 11:20:00', '2024-01-04 11:20:00', 1, 3, 'TRF202401004', 'PERSONAL', 0.00, 0.00, 1.0, 'IMPS', 'Mumbai', 'Mobile App', '192.168.1.107', 2, NULL, NULL, NOW(), NOW(), 'SYSTEM', 'SYSTEM', 0, false),

-- Rajesh Kumar's Transactions
(9, 'TXN202401009', 'CREDIT', 75000.00, 'INR', 'Salary Credit - Delhi IT Solutions', 'COMPLETED', '2024-01-01 08:45:00', '2024-01-01 08:45:00', NULL, 5, 'SAL202401009', 'SALARY', 0.00, 0.00, 1.0, 'NEFT', 'Delhi', 'Bank Transfer', '192.168.1.108', 3, NULL, NULL, NOW(), NOW(), 'SYSTEM', 'SYSTEM', 0, false),
(10, 'TXN202401010', 'DEBIT', 3500.00, 'INR', 'Petrol - Indian Oil', 'COMPLETED', '2024-01-03 18:20:00', '2024-01-03 18:20:00', 5, NULL, 'IOC202401010', 'FUEL', 0.00, 630.00, 1.0, 'POS', 'Delhi', 'Card Terminal', '*************', 3, NULL, 4, NOW(), NOW(), 'SYSTEM', 'SYSTEM', 0, false);

-- ========================================
-- LOANS DATA (Indian Banking Loans)
-- ========================================
INSERT IGNORE INTO loans (id, loan_number, loan_type, principal_amount, interest_rate, tenure_months, monthly_emi, outstanding_amount, status, application_date, approval_date, disbursement_date, maturity_date, purpose, collateral_details, guarantor_details, processing_fee, user_id, loan_account_id, approved_by, created_at, updated_at, created_by, updated_by, version, is_deleted) VALUES
-- Vedant Heda's Home Loan
(1, 'HL202301001', 'HOME_LOAN', 2500000.00, 8.5, 240, 25983.00, 2450000.00, 'ACTIVE', '2023-01-10', '2023-01-25', '2023-02-01', '2043-02-01', 'Purchase of 2BHK apartment in Pune', 'Property worth INR 3,500,000 in Baner, Pune', 'Mr. Ramesh Heda (Father)', 25000.00, 1, 1, 4, NOW(), NOW(), 'SYSTEM', 'SYSTEM', 0, false),

-- Anita Patel's Car Loan
(2, 'CL202306001', 'CAR_LOAN', 800000.00, 9.5, 60, 16784.00, 720000.00, 'ACTIVE', '2023-06-05', '2023-06-15', '2023-06-20', '2028-06-20', 'Purchase of Maruti Suzuki Swift Dzire', 'Vehicle Registration: MH12AB1234', 'Self-employed, Business income proof', 8000.00, 4, 6, 4, NOW(), NOW(), 'SYSTEM', 'SYSTEM', 0, false),

-- Rajesh Kumar's Personal Loan
(3, 'PL202309001', 'PERSONAL_LOAN', 500000.00, 10.5, 36, 16134.00, 420000.00, 'ACTIVE', '2023-09-10', '2023-09-20', '2023-09-25', '2026-09-25', 'Home renovation and medical expenses', 'No collateral - unsecured loan', 'Salary certificate and ITR', 5000.00, 3, 5, 3, NOW(), NOW(), 'SYSTEM', 'SYSTEM', 0, false);

-- ========================================
-- LOAN EMI DATA
-- ========================================
INSERT IGNORE INTO loan_emi (id, loan_id, emi_number, due_date, emi_amount, principal_component, interest_component, outstanding_balance, status, payment_date, payment_mode, late_fee, penalty, created_at, updated_at, created_by, updated_by, version, is_deleted) VALUES
-- Vedant's Home Loan EMIs
(1, 1, 1, '2023-03-01', 25983.00, 15400.00, 10583.00, 2484600.00, 'PAID', '2023-02-28', 'AUTO_DEBIT', 0.00, 0.00, NOW(), NOW(), 'SYSTEM', 'SYSTEM', 0, false),
(2, 1, 2, '2023-04-01', 25983.00, 15510.00, 10473.00, 2469090.00, 'PAID', '2023-03-31', 'AUTO_DEBIT', 0.00, 0.00, NOW(), NOW(), 'SYSTEM', 'SYSTEM', 0, false),
(3, 1, 3, '2023-05-01', 25983.00, 15621.00, 10362.00, 2453469.00, 'PAID', '2023-04-30', 'AUTO_DEBIT', 0.00, 0.00, NOW(), NOW(), 'SYSTEM', 'SYSTEM', 0, false),

-- Anita's Car Loan EMIs
(4, 2, 1, '2023-07-20', 16784.00, 10451.00, 6333.00, 789549.00, 'PAID', '2023-07-19', 'AUTO_DEBIT', 0.00, 0.00, NOW(), NOW(), 'SYSTEM', 'SYSTEM', 0, false),
(5, 2, 2, '2023-08-20', 16784.00, 10534.00, 6250.00, 779015.00, 'PAID', '2023-08-19', 'AUTO_DEBIT', 0.00, 0.00, NOW(), NOW(), 'SYSTEM', 'SYSTEM', 0, false),

-- Rajesh's Personal Loan EMIs
(6, 3, 1, '2023-10-25', 16134.00, 11759.00, 4375.00, 488241.00, 'PAID', '2023-10-24', 'AUTO_DEBIT', 0.00, 0.00, NOW(), NOW(), 'SYSTEM', 'SYSTEM', 0, false),
(7, 3, 2, '2023-11-25', 16134.00, 11862.00, 4272.00, 476379.00, 'PAID', '2023-11-24', 'AUTO_DEBIT', 0.00, 0.00, NOW(), NOW(), 'SYSTEM', 'SYSTEM', 0, false);

-- ========================================
-- BENEFICIARIES DATA (Saved Payees)
-- ========================================
INSERT IGNORE INTO beneficiaries (id, beneficiary_name, account_number, ifsc_code, bank_name, branch_name, type, status, nickname, mobile_number, email, is_verified, verification_date, verification_method, last_used_date, usage_count, is_favorite, relationship, purpose, user_id, created_at, updated_at, created_by, updated_by, version, is_deleted) VALUES
-- Vedant Heda's Beneficiaries
(1, 'Priya Sharma', '***********', 'A**********', 'ABC Bank', 'Mumbai Central Branch', 'INTERNAL', 'VERIFIED', 'Priya', '+91-**********', '<EMAIL>', true, NOW(), 'PENNY_DROP', '2024-01-04', 3, true, 'FRIEND', 'PERSONAL', 1, NOW(), NOW(), 'SYSTEM', 'SYSTEM', 0, false),
(2, 'Ramesh Heda', '**********1', 'HDFC0001234', 'HDFC Bank', 'Pune Camp Branch', 'EXTERNAL', 'VERIFIED', 'Papa', '+91-**********', '<EMAIL>', true, NOW(), 'MANUAL', '2023-12-15', 5, true, 'FAMILY', 'FAMILY_SUPPORT', 1, NOW(), NOW(), 'SYSTEM', 'SYSTEM', 0, false),
(3, 'Tech Corp India', '********556', 'ICIC0001111', 'ICICI Bank', 'Baner Branch', 'EXTERNAL', 'VERIFIED', 'Salary Account', '+91-20-********', '<EMAIL>', true, NOW(), 'AUTO', '2024-01-01', 12, false, 'BUSINESS', 'SALARY', 1, NOW(), NOW(), 'SYSTEM', 'SYSTEM', 0, false),

-- Priya Sharma's Beneficiaries
(4, 'Vedant Heda', '***********', 'ABCB0000004', 'ABC Bank', 'Pune Baner', 'INTERNAL', 'VERIFIED', 'Vedant', '+91-**********', '<EMAIL>', true, NOW(), 'PENNY_DROP', '2024-01-04', 2, true, 'FRIEND', 'PERSONAL', 2, NOW(), NOW(), 'SYSTEM', 'SYSTEM', 0, false),
(5, 'Mumbai Landlord', '55********0', '**********', 'State Bank of India', 'Andheri Branch', 'EXTERNAL', 'VERIFIED', 'Landlord', '+91-**********', '<EMAIL>', true, NOW(), 'MANUAL', '2024-01-05', 12, false, 'BUSINESS', 'RENT', 2, NOW(), NOW(), 'SYSTEM', 'SYSTEM', 0, false),

-- Anita Patel's Beneficiaries
(6, 'Maruti Suzuki Finance', '***********', 'MSIL0001234', 'Maruti Suzuki Finance', 'Ahmedabad Branch', 'EXTERNAL', 'VERIFIED', 'Car Loan', '+91-79-********', '<EMAIL>', true, NOW(), 'AUTO', '2023-06-20', 6, false, 'BUSINESS', 'LOAN_EMI', 4, NOW(), NOW(), 'SYSTEM', 'SYSTEM', 0, false);

-- ========================================
-- NOTIFICATIONS DATA (Recent Alerts)
-- ========================================
INSERT IGNORE INTO notifications (id, title, message, type, priority, status, is_read, sent_at, channel, category, reference_id, reference_type, user_id, created_at, updated_at, created_by, updated_by, version, is_deleted) VALUES
-- Vedant Heda's Notifications
(1, 'Salary Credited', 'Your salary of ₹50,000 has been credited to your Savings Account ending 8901', 'TRANSACTION_ALERT', 'HIGH', 'SENT', false, NOW(), 'SMS', 'TRANSACTION', '1', 'TRANSACTION', 1, NOW(), NOW(), 'SYSTEM', 'SYSTEM', 0, false),
(2, 'Card Transaction Alert', 'You have made a purchase of ₹1,200 at Amazon India using your Debit Card ending 9012', 'CARD_ALERT', 'MEDIUM', 'SENT', false, NOW(), 'PUSH', 'TRANSACTION', '3', 'TRANSACTION', 1, NOW(), NOW(), 'SYSTEM', 'SYSTEM', 0, false),
(3, 'EMI Deducted', 'Your Home Loan EMI of ₹25,983 has been deducted from your account', 'LOAN_REMINDER', 'MEDIUM', 'SENT', true, NOW(), 'EMAIL', 'LOAN', '1', 'LOAN', 1, NOW(), NOW(), 'SYSTEM', 'SYSTEM', 0, false),
(4, 'Low Balance Alert', 'Your Savings Account balance is below ₹10,000. Current balance: ₹8,500', 'BALANCE_LOW', 'HIGH', 'SENT', false, NOW(), 'SMS', 'ACCOUNT', '1', 'ACCOUNT', 1, NOW(), NOW(), 'SYSTEM', 'SYSTEM', 0, false),

-- Priya Sharma's Notifications
(5, 'Money Received', 'You have received ₹5,000 from Vedant Heda via IMPS', 'TRANSACTION_ALERT', 'MEDIUM', 'SENT', true, NOW(), 'SMS', 'TRANSACTION', '8', 'TRANSACTION', 2, NOW(), NOW(), 'SYSTEM', 'SYSTEM', 0, false),
(6, 'Rent Payment Successful', 'Your rent payment of ₹15,000 has been successfully processed', 'PAYMENT_DUE', 'MEDIUM', 'SENT', true, NOW(), 'EMAIL', 'PAYMENT', '7', 'TRANSACTION', 2, NOW(), NOW(), 'SYSTEM', 'SYSTEM', 0, false),

-- Rajesh Kumar's Notifications
(7, 'Personal Loan EMI Due', 'Your Personal Loan EMI of ₹16,134 is due on 25th December 2023', 'EMI_DUE', 'HIGH', 'SENT', false, NOW(), 'SMS', 'LOAN', '3', 'LOAN', 3, NOW(), NOW(), 'SYSTEM', 'SYSTEM', 0, false),

-- Anita Patel's Notifications
(8, 'Car Loan EMI Deducted', 'Your Car Loan EMI of ₹16,784 has been auto-debited from your account', 'EMI_DUE', 'MEDIUM', 'SENT', true, NOW(), 'EMAIL', 'LOAN', '2', 'LOAN', 4, NOW(), NOW(), 'SYSTEM', 'SYSTEM', 0, false),
(9, 'Credit Card Bill Generated', 'Your Credit Card bill of ₹25,000 is generated. Due date: 15th January 2024', 'CARD_ALERT', 'HIGH', 'SENT', false, NOW(), 'EMAIL', 'CARD', '6', 'CARD', 4, NOW(), NOW(), 'SYSTEM', 'SYSTEM', 0, false);

-- ========================================
-- STATEMENTS DATA (Account Statements)
-- ========================================
INSERT IGNORE INTO statements (id, statement_id, type, period, from_date, to_date, generation_date, status, opening_balance, closing_balance, total_credits, total_debits, transaction_count, file_name, file_format, account_id, created_at, updated_at, created_by, updated_by, version, is_deleted) VALUES
(1, 'STMT202312001', 'ACCOUNT_STATEMENT', 'MONTHLY', '2023-12-01', '2023-12-31', NOW(), 'READY', 120000.00, 125000.00, 55000.00, 50000.00, 15, 'statement_dec_2023_vedant.pdf', 'PDF', 1, NOW(), NOW(), 'SYSTEM', 'SYSTEM', 0, false),
(2, 'STMT202312002', 'ACCOUNT_STATEMENT', 'MONTHLY', '2023-12-01', '2023-12-31', NOW(), 'READY', 70000.00, 75000.00, 45000.00, 40000.00, 12, 'statement_dec_2023_priya.pdf', 'PDF', 3, NOW(), NOW(), 'SYSTEM', 'SYSTEM', 0, false),
(3, 'STMT202312003', 'ACCOUNT_STATEMENT', 'MONTHLY', '2023-12-01', '2023-12-31', NOW(), 'READY', 90000.00, 95000.00, 75000.00, 70000.00, 18, 'statement_dec_2023_rajesh.pdf', 'PDF', 5, NOW(), NOW(), 'SYSTEM', 'SYSTEM', 0, false);
