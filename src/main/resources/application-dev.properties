# ========================================
# DEVELOPMENT ENVIRONMENT CONFIGURATION
# ========================================

# ========================================
# SERVER CONFIGURATION
# ========================================
server.port=8080
server.servlet.context-path=/api
server.error.include-message=always
server.error.include-binding-errors=always
server.error.include-stacktrace=on_param

# ========================================
# DATABASE CONFIGURATION (MySQL for Development)
# ========================================
# MySQL Database (Development)
spring.datasource.url=**********************************************************************************************************************************
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.username=root
spring.datasource.password=root

# Connection Pool Configuration (HikariCP)
spring.datasource.hikari.connection-timeout=30000
spring.datasource.hikari.idle-timeout=600000
spring.datasource.hikari.max-lifetime=1800000
spring.datasource.hikari.maximum-pool-size=10
spring.datasource.hikari.minimum-idle=2
spring.datasource.hikari.pool-name=ABCBankingDevHikariCP

# H2 Console Configuration (Disabled for MySQL)
spring.h2.console.enabled=false

# JPA Configuration for MySQL
spring.jpa.database-platform=org.hibernate.dialect.MySQL8Dialect
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.use_sql_comments=true
spring.jpa.properties.hibernate.dialect.storage_engine=innodb
spring.jpa.properties.hibernate.connection.characterEncoding=utf8
spring.jpa.properties.hibernate.connection.CharSet=utf8
spring.jpa.properties.hibernate.connection.useUnicode=true

# Data initialization - DISABLED for now
spring.jpa.defer-datasource-initialization=true
spring.sql.init.mode=never
# spring.sql.init.data-locations=classpath:data.sql
# spring.sql.init.continue-on-error=false

# ========================================
# JWT CONFIGURATION (Development)
# ========================================
jwt.secret=dev-jwt-secret-key-for-development-only-not-for-production-use-make-it-long-enough
jwt.expiration=********
jwt.refresh-expiration=*********
jwt.header=Authorization
jwt.prefix=Bearer

# ========================================
# SECURITY CONFIGURATION (Development)
# ========================================
spring.security.user.name=admin
spring.security.user.password=admin123

# ========================================
# LOGGING CONFIGURATION (Development)
# ========================================
logging.level.org.springframework.security=DEBUG
logging.level.org.example.abcbanking=DEBUG
logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} - %msg%n

# ========================================
# SWAGGER/OPENAPI CONFIGURATION
# ========================================
springdoc.api-docs.path=/api-docs
springdoc.swagger-ui.path=/swagger-ui.html
springdoc.swagger-ui.operationsSorter=method
springdoc.swagger-ui.tagsSorter=alpha
springdoc.default-produces-media-type=application/json
springdoc.default-consumes-media-type=application/json

# ========================================
# ACTUATOR CONFIGURATION
# ========================================
management.endpoints.web.exposure.include=health,info,metrics,prometheus
management.endpoint.health.show-details=always
management.metrics.export.prometheus.enabled=true

# ========================================
# CACHE CONFIGURATION (Simple for Development)
# ========================================
spring.cache.type=simple

# ========================================
# ASYNC CONFIGURATION
# ========================================
spring.task.execution.pool.core-size=2
spring.task.execution.pool.max-size=5
spring.task.execution.pool.queue-capacity=50
spring.task.execution.thread-name-prefix=bank-async-

# ========================================
# FILE UPLOAD CONFIGURATION
# ========================================
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB

# ========================================
# CORS CONFIGURATION
# ========================================
cors.allowed-origins=http://localhost:3000,http://localhost:3001
cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS
cors.allowed-headers=*
cors.allow-credentials=true

# ========================================
# BUSINESS LOGIC CONFIGURATION (Indian Banking)
# ========================================
# Currency and locale
bank.currency=INR
bank.locale=en_IN
bank.timezone=Asia/Kolkata

# Transaction limits (in INR)
bank.transaction.max-amount=********
bank.transaction.daily-limit=200000
bank.transaction.minimum-amount=1
bank.transaction.imps-limit=500000
bank.transaction.neft-limit=1000000
bank.transaction.rtgs-limit=********

# Interest rates (Indian banking rates)
bank.interest.savings=3.5
bank.interest.current=0.0
bank.interest.fd.min=5.5
bank.interest.fd.max=7.5
bank.interest.loan.personal=10.5
bank.interest.loan.home=8.5
bank.interest.loan.car=9.5
bank.interest.loan.education=9.0

# Account limits (in INR)
bank.account.minimum-balance.savings=1000
bank.account.minimum-balance.current=10000
bank.account.minimum-balance.salary=0
bank.account.overdraft-limit.max=500000

# Loan configuration (in INR)
bank.loan.max-amount.personal=2000000
bank.loan.max-amount.home=********
bank.loan.max-amount.car=2000000
bank.loan.max-amount.education=1500000
bank.loan.max-tenure=360
bank.loan.processing-fee=1.0

# Card limits (in INR)
bank.card.daily-limit.debit=50000
bank.card.monthly-limit.debit=200000
bank.card.daily-limit.credit=100000
bank.card.monthly-limit.credit=500000

# ========================================
# EMAIL CONFIGURATION (Mock for Development)
# ========================================
spring.mail.host=localhost
spring.mail.port=1025
spring.mail.username=
spring.mail.password=
spring.mail.properties.mail.smtp.auth=false
spring.mail.properties.mail.smtp.starttls.enable=false

# ========================================
# SCHEDULING CONFIGURATION
# ========================================
bank.scheduling.interest-credit.cron=0 0 1 * * *
bank.scheduling.loan-emi.cron=0 0 5 * * *
bank.scheduling.reports.cron=0 0 6 * * 1

# ========================================
# EXTERNAL SERVICES (Disabled for Development)
# ========================================
# Redis (disabled for development)
spring.data.redis.repositories.enabled=false

# RabbitMQ (disabled for development)
spring.rabbitmq.host=localhost
spring.rabbitmq.port=5672
spring.rabbitmq.username=guest
spring.rabbitmq.password=guest
