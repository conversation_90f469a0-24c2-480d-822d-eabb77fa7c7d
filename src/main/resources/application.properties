# ========================================
# SPRING PROFILES CONFIGURATION
# ========================================
# Default profile is 'dev' for development
# Use 'prod' profile for production
spring.profiles.active=dev

# ========================================
# COMMON CONFIGURATION (All Environments)
# ========================================

# Application name and description
spring.application.name=ABC Banking Enterprise
spring.application.description=Enterprise-grade banking management system

# ========================================
# COMMON BUSINESS LOGIC CONFIGURATION
# ========================================
# These values are shared across all environments
# Environment-specific overrides are in profile-specific files

# Application version
app.version=1.0.0
app.build-timestamp=@maven.build.timestamp@

# Default timezone
app.timezone=UTC

# File upload limits (can be overridden per environment)
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB

# Jackson configuration
spring.jackson.time-zone=UTC
spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
spring.jackson.serialization.write-dates-as-timestamps=false

# ========================================
# COMMON SECURITY CONFIGURATION
# ========================================
# JWT Configuration (common settings)
jwt.header=Authorization
jwt.prefix=Bearer

# ========================================
# COMMON ACTUATOR CONFIGURATION
# ========================================
management.info.env.enabled=true
management.info.java.enabled=true
management.info.os.enabled=true

# ========================================
# COMMON BUSINESS RULES
# ========================================
# These can be overridden in environment-specific files
bank.transaction.max-amount=1000000
bank.transaction.daily-limit=50000
bank.transaction.minimum-amount=1

bank.interest.savings=2.5
bank.interest.current=0.5
bank.interest.loan.min=8.0
bank.interest.loan.max=15.0

bank.account.minimum-balance.savings=1000
bank.account.minimum-balance.current=5000
bank.account.overdraft-limit.max=50000

bank.loan.max-amount=********
bank.loan.max-tenure=360
bank.loan.processing-fee=1.0