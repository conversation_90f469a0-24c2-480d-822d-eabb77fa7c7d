#!/bin/bash

# ABC Banking - Setup Verification Script
# This script verifies that the development environment is properly set up

echo "🏦 ABC Banking - Setup Verification"
echo "=================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to print status
print_status() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ $2${NC}"
    else
        echo -e "${RED}❌ $2${NC}"
    fi
}

# Check Java
echo -e "\n${YELLOW}Checking Java...${NC}"
if command_exists java; then
    JAVA_VERSION=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2)
    echo "Java version: $JAVA_VERSION"
    if [[ "$JAVA_VERSION" =~ ^(17|18|19|20|21) ]]; then
        print_status 0 "Java 17+ is installed"
    else
        print_status 1 "Java 17+ required, found $JAVA_VERSION"
    fi
else
    print_status 1 "Java not found"
fi

# Check Node.js
echo -e "\n${YELLOW}Checking Node.js...${NC}"
if command_exists node; then
    NODE_VERSION=$(node --version | cut -d'v' -f2)
    echo "Node.js version: $NODE_VERSION"
    if [[ "$NODE_VERSION" =~ ^(18|19|20|21) ]]; then
        print_status 0 "Node.js 18+ is installed"
    else
        print_status 1 "Node.js 18+ required, found $NODE_VERSION"
    fi
else
    print_status 1 "Node.js not found"
fi

# Check npm
echo -e "\n${YELLOW}Checking npm...${NC}"
if command_exists npm; then
    NPM_VERSION=$(npm --version)
    echo "npm version: $NPM_VERSION"
    print_status 0 "npm is installed"
else
    print_status 1 "npm not found"
fi

# Check MySQL
echo -e "\n${YELLOW}Checking MySQL...${NC}"
if command_exists mysql; then
    MYSQL_VERSION=$(mysql --version | cut -d' ' -f3)
    echo "MySQL version: $MYSQL_VERSION"
    print_status 0 "MySQL is installed"
    
    # Test MySQL connection
    echo "Testing MySQL connection..."
    if mysql -u root -proot -e "SELECT 1;" >/dev/null 2>&1; then
        print_status 0 "MySQL connection successful"
        
        # Check if database exists
        if mysql -u root -proot -e "USE abc_banking;" >/dev/null 2>&1; then
            print_status 0 "Database 'abc_banking' exists"
        else
            print_status 1 "Database 'abc_banking' not found"
            echo "Run: mysql -u root -proot -e 'CREATE DATABASE abc_banking;'"
        fi
    else
        print_status 1 "MySQL connection failed (check credentials)"
    fi
else
    print_status 1 "MySQL not found"
fi

# Check Git
echo -e "\n${YELLOW}Checking Git...${NC}"
if command_exists git; then
    GIT_VERSION=$(git --version | cut -d' ' -f3)
    echo "Git version: $GIT_VERSION"
    print_status 0 "Git is installed"
else
    print_status 1 "Git not found"
fi

# Check project structure
echo -e "\n${YELLOW}Checking project structure...${NC}"

if [ -f "pom.xml" ]; then
    print_status 0 "Backend (pom.xml) found"
else
    print_status 1 "Backend (pom.xml) not found"
fi

if [ -d "src/main/java" ]; then
    print_status 0 "Java source directory found"
else
    print_status 1 "Java source directory not found"
fi

if [ -d "unzl" ]; then
    print_status 0 "Frontend directory (unzl) found"
else
    print_status 1 "Frontend directory (unzl) not found"
fi

if [ -f "unzl/package.json" ]; then
    print_status 0 "Frontend package.json found"
else
    print_status 1 "Frontend package.json not found"
fi

# Check if dependencies are installed
echo -e "\n${YELLOW}Checking dependencies...${NC}"

if [ -d "unzl/node_modules" ]; then
    print_status 0 "Frontend dependencies installed"
else
    print_status 1 "Frontend dependencies not installed (run: cd unzl && npm install)"
fi

# Check ports
echo -e "\n${YELLOW}Checking ports...${NC}"

if command_exists netstat; then
    if netstat -an | grep -q ":8080"; then
        print_status 1 "Port 8080 is in use"
    else
        print_status 0 "Port 8080 is available"
    fi
    
    if netstat -an | grep -q ":3001"; then
        print_status 1 "Port 3001 is in use"
    else
        print_status 0 "Port 3001 is available"
    fi
    
    if netstat -an | grep -q ":3306"; then
        print_status 0 "Port 3306 is in use (MySQL)"
    else
        print_status 1 "Port 3306 is not in use (MySQL not running?)"
    fi
else
    echo "netstat not available, skipping port check"
fi

echo -e "\n${YELLOW}Setup verification complete!${NC}"
echo ""
echo "Next steps:"
echo "1. If any checks failed, install the missing prerequisites"
echo "2. Run: ./mvnw clean install"
echo "3. Run: cd unzl && npm install"
echo "4. Start backend: ./mvnw spring-boot:run"
echo "5. Start frontend: cd unzl && npm run dev"
echo ""
echo "For detailed setup instructions, see SETUP_GUIDE.md"
