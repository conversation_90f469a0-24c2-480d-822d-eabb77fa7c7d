"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { MainNavigation } from "@/components/navigation/MainNavigation";
import { AccountCard } from "./AccountCard";
import { TransactionList } from "./TransactionList";
import { QuickActions } from "./QuickActions";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { apiClient, notificationApi, handleApiError, formatCurrency, Account, Transaction, Notification } from "@/lib/api";
import { useAuth } from "@/contexts/AuthContext";
import { 
  Bell, 
  Search, 
  TrendingUp, 
  TrendingDown,
  DollarSign,
  CreditCard,
  Target,
  Calendar
} from "lucide-react";

// Mock data - in a real app, this would come from your API
const mockAccounts = [
  {
    id: "1",
    accountNumber: "**********",
    accountType: "Checking Account",
    balance: 15420.50,
    currency: "USD",
    status: "active" as const,
    lastTransaction: "2 hours ago"
  },
  {
    id: "2",
    accountNumber: "**********",
    accountType: "Savings Account",
    balance: 45680.75,
    currency: "USD",
    status: "active" as const,
    lastTransaction: "1 day ago"
  },
  {
    id: "3",
    accountNumber: "**********",
    accountType: "Business Account",
    balance: 125000.00,
    currency: "USD",
    status: "active" as const,
    lastTransaction: "3 hours ago"
  }
];

const mockTransactions = [
  {
    id: "1",
    type: "credit" as const,
    amount: 2500.00,
    currency: "USD",
    description: "Salary Deposit",
    date: "2024-01-15T10:30:00Z",
    status: "completed" as const,
    category: "Income"
  },
  {
    id: "2",
    type: "debit" as const,
    amount: 850.00,
    currency: "USD",
    description: "Rent Payment",
    date: "2024-01-14T14:20:00Z",
    status: "completed" as const,
    category: "Housing"
  },
  {
    id: "3",
    type: "debit" as const,
    amount: 125.50,
    currency: "USD",
    description: "Grocery Store",
    date: "2024-01-14T09:15:00Z",
    status: "completed" as const,
    category: "Food"
  },
  {
    id: "4",
    type: "transfer" as const,
    amount: 1000.00,
    currency: "USD",
    description: "Transfer to Savings",
    date: "2024-01-13T16:45:00Z",
    status: "completed" as const,
    category: "Transfer"
  },
  {
    id: "5",
    type: "debit" as const,
    amount: 75.00,
    currency: "USD",
    description: "Utility Bill",
    date: "2024-01-12T11:30:00Z",
    status: "pending" as const,
    category: "Utilities"
  }
];

  // Dynamic stats cards based on real data
  const statsCards = [
    {
      title: "Total Balance",
      value: formatCurrency(stats.totalBalance),
      change: "+12.5%",
      trend: "up" as const,
      icon: DollarSign,
      color: "text-green-500"
    },
    {
      title: "Monthly Spending",
      value: formatCurrency(stats.monthlySpending),
      change: "-8.2%",
      trend: "down" as const,
      icon: CreditCard,
      color: "text-blue-500"
    },
    {
      title: "Savings Goal",
      value: `${stats.savingsGoalProgress}%`,
      change: "+5.1%",
      trend: "up" as const,
      icon: Target,
      color: "text-purple-500"
    },
    {
      title: "Monthly Income",
      value: formatCurrency(stats.monthlyIncome),
      change: "+15.3%",
      trend: "up" as const,
      icon: Calendar,
      color: "text-orange-500"
    }
  ];

export function BankingDashboard() {
  const { user } = useAuth();
  const [accounts, setAccounts] = useState<Account[]>([]);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [notificationCount, setNotificationCount] = useState(0);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    totalBalance: 0,
    monthlySpending: 0,
    savingsGoalProgress: 68,
    monthlyIncome: 0
  });

  useEffect(() => {
    if (user) {
      loadDashboardData();
    }
  }, [user]);

  const loadDashboardData = async () => {
    try {
      setLoading(true);

      // Load accounts
      const accountsData = await apiClient.getAccounts();
      setAccounts(accountsData);

      // Load recent transactions
      const transactionsData = await apiClient.getTransactions(undefined, 10, 0);
      setTransactions(transactionsData);

      // Load notification count
      const unreadCount = await apiClient.getUnreadCount();
      setNotificationCount(unreadCount);

      // Calculate stats
      const totalBalance = accountsData.reduce((sum, account) => sum + account.balance, 0);
      const monthlySpending = transactionsData
        .filter(t => t.type === 'DEBIT' || t.type === 'WITHDRAWAL')
        .reduce((sum, t) => sum + t.amount, 0);
      const monthlyIncome = transactionsData
        .filter(t => t.type === 'CREDIT' || t.type === 'DEPOSIT')
        .reduce((sum, t) => sum + t.amount, 0);

      setStats({
        totalBalance,
        monthlySpending,
        savingsGoalProgress: 68, // This could be calculated based on user goals
        monthlyIncome
      });

    } catch (error) {
      console.error("Error loading dashboard data:", error);
      // Keep using mock data as fallback
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="flex min-h-screen bg-background">
      <MainNavigation currentPath="/dashboard" />

      <div className="flex-1 flex flex-col">
        {/* Header */}
        <motion.header
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="bg-card border-b border-border/50 p-4 lg:p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl lg:text-3xl font-bold">
                Welcome back, {user?.firstName || 'User'}!
              </h1>
              <p className="text-muted-foreground">Here's what's happening with your accounts today.</p>
            </div>
            <div className="flex items-center gap-3">
              <Button variant="outline" size="icon">
                <Search className="h-4 w-4" />
              </Button>
              <Button variant="outline" size="icon" className="relative">
                <Bell className="h-4 w-4" />
                {notificationCount > 0 && (
                  <Badge className="absolute -top-1 -right-1 h-5 w-5 p-0 text-xs">
                    {notificationCount}
                  </Badge>
                )}
              </Button>
            </div>
          </div>
        </motion.header>

        {/* Main Content */}
        <main className="flex-1 p-4 lg:p-6 space-y-6">
          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {statsCards.map((stat, index) => {
              const IconComponent = stat.icon;
              return (
                <motion.div
                  key={stat.title}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                >
                  <Card className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-muted-foreground mb-1">{stat.title}</p>
                        <p className="text-2xl font-bold">{stat.value}</p>
                        <div className="flex items-center gap-1 mt-2">
                          {stat.trend === "up" ? (
                            <TrendingUp className="h-4 w-4 text-green-500" />
                          ) : (
                            <TrendingDown className="h-4 w-4 text-red-500" />
                          )}
                          <span className={`text-sm ${
                            stat.trend === "up" ? "text-green-500" : "text-red-500"
                          }`}>
                            {stat.change}
                          </span>
                        </div>
                      </div>
                      <div className={`p-3 rounded-xl bg-muted/50 ${stat.color}`}>
                        <IconComponent className="h-6 w-6" />
                      </div>
                    </div>
                  </Card>
                </motion.div>
              );
            })}
          </div>

          {/* Accounts Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {loading ? (
              // Loading skeleton
              Array.from({ length: 3 }).map((_, index) => (
                <Card key={index} className="p-6 animate-pulse">
                  <div className="h-4 bg-muted rounded w-1/2 mb-2"></div>
                  <div className="h-8 bg-muted rounded w-3/4 mb-4"></div>
                  <div className="h-4 bg-muted rounded w-1/3"></div>
                </Card>
              ))
            ) : accounts.length > 0 ? (
              accounts.map((account, index) => (
                <AccountCard
                  key={account.id}
                  account={{
                    id: account.id.toString(),
                    accountNumber: account.accountNumber,
                    accountType: account.accountType,
                    balance: account.balance,
                    currency: account.currency,
                    status: account.status.toLowerCase() as any,
                    lastTransaction: "Recently"
                  }}
                  className={`delay-${index * 100}`}
                />
              ))
            ) : (
              // Fallback to mock data if no real accounts
              mockAccounts.map((account, index) => (
                <AccountCard
                  key={account.id}
                  account={account}
                  className={`delay-${index * 100}`}
                />
              ))
            )}
          </div>

          {/* Quick Actions */}
          <QuickActions />

          {/* Recent Transactions */}
          <TransactionList
            transactions={transactions.length > 0 ? transactions.map(t => ({
              id: t.id.toString(),
              type: t.type.toLowerCase() as any,
              amount: t.amount,
              currency: t.currency,
              description: t.description || 'Transaction',
              date: t.transactionDate,
              status: t.status.toLowerCase() as any,
              category: t.category || 'General'
            })) : mockTransactions}
          />
        </main>
      </div>
    </div>
  );
}
