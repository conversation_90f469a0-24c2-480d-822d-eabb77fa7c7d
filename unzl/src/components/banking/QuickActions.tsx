"use client";

import Link from "next/link";
import { motion } from "framer-motion";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Send,
  Download,
  CreditCard,
  PiggyBank,
  FileText,
  Settings,
  Phone,
  Zap
} from "lucide-react";

const quickActions = [
  {
    id: "transfer",
    title: "Transfer Money",
    description: "Send money to another account",
    icon: Send,
    color: "text-blue-500",
    bgColor: "bg-blue-500/10",
    href: "/transfer"
  },
  {
    id: "deposit",
    title: "Deposit Funds",
    description: "Add money to your account",
    icon: Download,
    color: "text-green-500",
    bgColor: "bg-green-500/10",
    href: "/deposit"
  },
  {
    id: "cards",
    title: "Manage Cards",
    description: "View and control your cards",
    icon: CreditCard,
    color: "text-purple-500",
    bgColor: "bg-purple-500/10",
    href: "/cards"
  },
  {
    id: "savings",
    title: "Savings Goals",
    description: "Track your savings progress",
    icon: PiggyBank,
    color: "text-pink-500",
    bgColor: "bg-pink-500/10",
    href: "/savings"
  },
  {
    id: "statements",
    title: "Statements",
    description: "Download account statements",
    icon: FileText,
    color: "text-orange-500",
    bgColor: "bg-orange-500/10",
    href: "/statements"
  },
  {
    id: "settings",
    title: "Account Settings",
    description: "Manage your preferences",
    icon: Settings,
    color: "text-gray-500",
    bgColor: "bg-gray-500/10",
    href: "/settings"
  },
  {
    id: "support",
    title: "Customer Support",
    description: "Get help when you need it",
    icon: Phone,
    color: "text-red-500",
    bgColor: "bg-red-500/10",
    href: "/support"
  },
  {
    id: "payBills",
    title: "Pay Bills",
    description: "Quick bill payments",
    icon: Zap,
    color: "text-yellow-500",
    bgColor: "bg-yellow-500/10",
    href: "/bills"
  }
];

interface QuickActionsProps {
  className?: string;
}

export function QuickActions({ className }: QuickActionsProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: 0.4 }}
      className={className}
    >
      <Card className="p-6">
        <h2 className="text-xl font-semibold mb-6">Quick Actions</h2>
        
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {quickActions.map((action, index) => {
            const IconComponent = action.icon;
            
            return (
              <motion.div
                key={action.id}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
              >
                <Button
                  variant="ghost"
                  className="h-auto p-4 flex flex-col items-center gap-3 hover:bg-muted/50 transition-all duration-300 group w-full"
                  asChild
                >
                  <Link href={action.href}>
                    <div className={`p-3 rounded-xl ${action.bgColor} ${action.color} transition-transform group-hover:scale-110`}>
                      <IconComponent className="h-6 w-6" />
                    </div>
                    <div className="text-center">
                      <div className="font-medium text-sm mb-1">{action.title}</div>
                      <div className="text-xs text-muted-foreground leading-tight">
                        {action.description}
                      </div>
                    </div>
                  </Link>
                </Button>
              </motion.div>
            );
          })}
        </div>
      </Card>
    </motion.div>
  );
}
