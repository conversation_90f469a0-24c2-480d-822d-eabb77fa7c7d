"use client";

import { motion } from "framer-motion";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { 
  CreditCard, 
  Eye, 
  EyeOff, 
  ArrowUpRight, 
  ArrowDownLeft,
  MoreHorizontal 
} from "lucide-react";
import { useState } from "react";

interface AccountCardProps {
  account: {
    id: string;
    accountNumber: string;
    accountType: string;
    balance: number;
    currency: string;
    status: "active" | "inactive" | "frozen";
    lastTransaction?: string;
  };
  className?: string;
}

export function AccountCard({ account, className }: AccountCardProps) {
  const [showBalance, setShowBalance] = useState(true);

  const formatBalance = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: account.currency,
      minimumFractionDigits: 2,
    }).format(amount);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'success';
      case 'inactive':
        return 'warning';
      case 'frozen':
        return 'destructive';
      default:
        return 'secondary';
    }
  };

  const getAccountTypeIcon = (type: string) => {
    return <CreditCard className="h-6 w-6" />;
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className={className}
    >
      <Card className="account-card group hover:shadow-lg transition-all duration-300">
        <div className="flex items-start justify-between mb-4">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-primary/10 text-primary">
              {getAccountTypeIcon(account.accountType)}
            </div>
            <div>
              <h3 className="font-semibold text-lg">{account.accountType}</h3>
              <p className="text-sm text-muted-foreground">
                ****{account.accountNumber.slice(-4)}
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Badge variant={getStatusColor(account.status) as any}>
              {account.status}
            </Badge>
            <Button variant="ghost" size="icon" className="h-8 w-8">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </div>
        </div>

        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted-foreground mb-1">Available Balance</p>
              <div className="flex items-center gap-2">
                {showBalance ? (
                  <span className="balance-display banking-gradient">
                    {formatBalance(account.balance)}
                  </span>
                ) : (
                  <span className="balance-display">••••••</span>
                )}
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-6 w-6"
                  onClick={() => setShowBalance(!showBalance)}
                >
                  {showBalance ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>
          </div>

          {account.lastTransaction && (
            <div className="text-xs text-muted-foreground">
              Last transaction: {account.lastTransaction}
            </div>
          )}

          <div className="flex gap-2 pt-2">
            <Button size="sm" className="flex-1 group">
              <ArrowUpRight className="h-4 w-4 mr-1 transition-transform group-hover:translate-x-0.5 group-hover:-translate-y-0.5" />
              Send
            </Button>
            <Button size="sm" variant="outline" className="flex-1 group">
              <ArrowDownLeft className="h-4 w-4 mr-1 transition-transform group-hover:-translate-x-0.5 group-hover:translate-y-0.5" />
              Request
            </Button>
          </div>
        </div>
      </Card>
    </motion.div>
  );
}
