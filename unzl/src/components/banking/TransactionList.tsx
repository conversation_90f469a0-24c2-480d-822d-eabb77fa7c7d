"use client";

import { motion } from "framer-motion";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { 
  ArrowUpRight, 
  ArrowDownLeft, 
  RefreshCw,
  Filter,
  Download
} from "lucide-react";

interface Transaction {
  id: string;
  type: "credit" | "debit" | "transfer";
  amount: number;
  currency: string;
  description: string;
  date: string;
  status: "completed" | "pending" | "failed";
  category?: string;
  fromAccount?: string;
  toAccount?: string;
}

interface TransactionListProps {
  transactions: Transaction[];
  className?: string;
}

export function TransactionList({ transactions, className }: TransactionListProps) {
  const formatAmount = (amount: number, currency: string, type: string) => {
    const formatted = new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2,
    }).format(Math.abs(amount));

    return type === 'credit' ? `+${formatted}` : `-${formatted}`;
  };

  const getTransactionIcon = (type: string) => {
    switch (type) {
      case 'credit':
        return <ArrowDownLeft className="h-4 w-4 text-success" />;
      case 'debit':
        return <ArrowUpRight className="h-4 w-4 text-destructive" />;
      case 'transfer':
        return <RefreshCw className="h-4 w-4 text-primary" />;
      default:
        return <RefreshCw className="h-4 w-4" />;
    }
  };

  const getAmountClass = (type: string) => {
    switch (type) {
      case 'credit':
        return 'amount-positive';
      case 'debit':
        return 'amount-negative';
      default:
        return 'amount-neutral';
    }
  };

  const getStatusVariant = (status: string) => {
    switch (status) {
      case 'completed':
        return 'success';
      case 'pending':
        return 'warning';
      case 'failed':
        return 'destructive';
      default:
        return 'secondary';
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: 0.2 }}
      className={className}
    >
      <Card className="p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold">Recent Transactions</h2>
          <div className="flex gap-2">
            <Button variant="outline" size="sm">
              <Filter className="h-4 w-4 mr-2" />
              Filter
            </Button>
            <Button variant="outline" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
          </div>
        </div>

        <div className="space-y-1">
          {transactions.map((transaction, index) => (
            <motion.div
              key={transaction.id}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
              className="transaction-row flex items-center justify-between p-4 rounded-lg"
            >
              <div className="flex items-center gap-4">
                <div className="p-2 rounded-full bg-muted/50">
                  {getTransactionIcon(transaction.type)}
                </div>
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <p className="font-medium">{transaction.description}</p>
                    <Badge variant={getStatusVariant(transaction.status) as any} className="text-xs">
                      {transaction.status}
                    </Badge>
                  </div>
                  <div className="flex items-center gap-4 text-sm text-muted-foreground">
                    <span>{new Date(transaction.date).toLocaleDateString()}</span>
                    {transaction.category && (
                      <span className="px-2 py-0.5 bg-muted rounded text-xs">
                        {transaction.category}
                      </span>
                    )}
                  </div>
                </div>
              </div>
              <div className="text-right">
                <div className={`text-lg font-semibold ${getAmountClass(transaction.type)}`}>
                  {formatAmount(transaction.amount, transaction.currency, transaction.type)}
                </div>
                <div className="text-xs text-muted-foreground">
                  {new Date(transaction.date).toLocaleTimeString([], { 
                    hour: '2-digit', 
                    minute: '2-digit' 
                  })}
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {transactions.length === 0 && (
          <div className="text-center py-12">
            <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-muted/50 flex items-center justify-center">
              <RefreshCw className="h-8 w-8 text-muted-foreground" />
            </div>
            <h3 className="text-lg font-medium mb-2">No transactions yet</h3>
            <p className="text-muted-foreground">
              Your transaction history will appear here once you start using your account.
            </p>
          </div>
        )}
      </Card>
    </motion.div>
  );
}
