"use client";

import { useState } from "react";
import Link from "next/link";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useAuth } from "@/contexts/AuthContext";
import {
  Home,
  CreditCard,
  ArrowLeftRight,
  PiggyBank,
  FileText,
  Settings,
  Bell,
  User,
  LogOut,
  Menu,
  X,
  Shield,
  UserCog
} from "lucide-react";

const navigationItems = [
  {
    id: "dashboard",
    title: "Dashboard",
    icon: Home,
    href: "/dashboard",
    badge: null
  },
  {
    id: "accounts",
    title: "Accounts",
    icon: CreditCard,
    href: "/accounts",
    badge: null
  },
  {
    id: "transfers",
    title: "Transfers",
    icon: ArrowLeftRight,
    href: "/transfers",
    badge: null
  },
  {
    id: "savings",
    title: "Savings",
    icon: PiggyBank,
    href: "/savings",
    badge: "New"
  },
  {
    id: "statements",
    title: "Statements",
    icon: FileText,
    href: "/statements",
    badge: null
  },
  {
    id: "admin",
    title: "Admin",
    icon: UserCog,
    href: "/admin",
    badge: "Admin",
    adminOnly: true
  },
  {
    id: "settings",
    title: "Settings",
    icon: Settings,
    href: "/settings",
    badge: null
  }
];

interface BankingNavigationProps {
  currentPath?: string;
  className?: string;
}

export function BankingNavigation({ currentPath = "/dashboard", className }: BankingNavigationProps) {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const { user } = useAuth();

  // Filter navigation items based on user role
  const filteredNavigationItems = navigationItems.filter(item => {
    if (item.adminOnly) {
      return user?.roles?.some(role => role.name === 'ROLE_ADMIN' || role.name === 'ROLE_BRANCH_MANAGER');
    }
    return true;
  });

  return (
    <>
      {/* Desktop Navigation */}
      <motion.nav
        initial={{ opacity: 0, x: -20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.5 }}
        className={`hidden lg:flex flex-col w-64 bg-card border-r border-border/50 ${className}`}
      >
        {/* Logo */}
        <div className="p-6 border-b border-border/50">
          <Link href="/dashboard" className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-primary/10 text-primary">
              <Shield className="h-6 w-6" />
            </div>
            <div>
              <h1 className="font-bold text-lg banking-gradient">ABC Banking</h1>
              <p className="text-xs text-muted-foreground">Enterprise</p>
            </div>
          </Link>
        </div>

        {/* Navigation Items */}
        <div className="flex-1 p-4 space-y-2">
          {filteredNavigationItems.map((item) => {
            const IconComponent = item.icon;
            const isActive = currentPath === item.href;
            
            return (
              <motion.div
                key={item.id}
                whileHover={{ x: 4 }}
                transition={{ duration: 0.2 }}
              >
                <Button
                  variant={isActive ? "default" : "ghost"}
                  className={`w-full justify-start gap-3 h-12 ${
                    isActive 
                      ? "bg-primary text-primary-foreground shadow-md" 
                      : "hover:bg-muted/50"
                  }`}
                  asChild
                >
                  <Link href={item.href}>
                    <IconComponent className="h-5 w-5" />
                    <span className="font-medium">{item.title}</span>
                    {item.badge && (
                      <Badge variant="secondary" className="ml-auto text-xs">
                        {item.badge}
                      </Badge>
                    )}
                  </Link>
                </Button>
              </motion.div>
            );
          })}
        </div>

        {/* User Section */}
        <div className="p-4 border-t border-border/50">
          <div className="flex items-center gap-3 p-3 rounded-lg bg-muted/30">
            <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center">
              <User className="h-5 w-5 text-primary" />
            </div>
            <div className="flex-1">
              <p className="font-medium text-sm">John Doe</p>
              <p className="text-xs text-muted-foreground">Premium Account</p>
            </div>
            <Button variant="ghost" size="icon" className="h-8 w-8">
              <LogOut className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </motion.nav>

      {/* Mobile Navigation */}
      <div className="lg:hidden">
        {/* Mobile Header */}
        <div className="flex items-center justify-between p-4 bg-card border-b border-border/50">
          <Link href="/dashboard" className="flex items-center gap-2">
            <div className="p-1.5 rounded-lg bg-primary/10 text-primary">
              <Shield className="h-5 w-5" />
            </div>
            <span className="font-bold banking-gradient">ABC Banking</span>
          </Link>
          
          <div className="flex items-center gap-2">
            <Button variant="ghost" size="icon">
              <Bell className="h-5 w-5" />
            </Button>
            <Button 
              variant="ghost" 
              size="icon"
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            >
              {isMobileMenuOpen ? (
                <X className="h-5 w-5" />
              ) : (
                <Menu className="h-5 w-5" />
              )}
            </Button>
          </div>
        </div>

        {/* Mobile Menu Overlay */}
        {isMobileMenuOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-50 bg-background/80 backdrop-blur-sm"
            onClick={() => setIsMobileMenuOpen(false)}
          >
            <motion.div
              initial={{ x: "-100%" }}
              animate={{ x: 0 }}
              exit={{ x: "-100%" }}
              transition={{ type: "spring", damping: 20 }}
              className="fixed left-0 top-0 h-full w-64 bg-card border-r border-border/50 p-4"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="space-y-2">
                {filteredNavigationItems.map((item) => {
                  const IconComponent = item.icon;
                  const isActive = currentPath === item.href;
                  
                  return (
                    <Button
                      key={item.id}
                      variant={isActive ? "default" : "ghost"}
                      className={`w-full justify-start gap-3 h-12 ${
                        isActive 
                          ? "bg-primary text-primary-foreground" 
                          : "hover:bg-muted/50"
                      }`}
                      asChild
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      <Link href={item.href}>
                        <IconComponent className="h-5 w-5" />
                        <span className="font-medium">{item.title}</span>
                        {item.badge && (
                          <Badge variant="secondary" className="ml-auto text-xs">
                            {item.badge}
                          </Badge>
                        )}
                      </Link>
                    </Button>
                  );
                })}
              </div>
            </motion.div>
          </motion.div>
        )}
      </div>
    </>
  );
}
