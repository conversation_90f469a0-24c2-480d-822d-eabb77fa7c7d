"use client";

import { motion } from "framer-motion";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  Shield,
  CreditCard,
  Smartphone,
  TrendingUp,
  Lock,
  CheckCircle,
  ArrowRight,
  Globe,
  Users,
  Award,
  Zap,
  DollarSign,
  PiggyBank
} from "lucide-react";

const features = [
  {
    icon: Shield,
    title: "Bank-Grade Security",
    description: "256-bit SSL encryption and multi-factor authentication protect your accounts 24/7."
  },
  {
    icon: Smartphone,
    title: "Mobile Banking",
    description: "Access your accounts anywhere with our secure mobile app and instant notifications."
  },
  {
    icon: TrendingUp,
    title: "Investment Tools",
    description: "Grow your wealth with our comprehensive investment and savings solutions."
  },
  {
    icon: CreditCard,
    title: "Digital Payments",
    description: "Send money instantly with our fast, secure transfer system and digital wallet."
  },
  {
    icon: PiggyBank,
    title: "Smart Savings",
    description: "Automated savings goals and high-yield accounts to maximize your returns."
  },
  {
    icon: Zap,
    title: "Instant Transfers",
    description: "Real-time money transfers between accounts and to other banks worldwide."
  }
];

const stats = [
  { value: "2M+", label: "Active Users", icon: Users },
  { value: "$50B+", label: "Assets Managed", icon: DollarSign },
  { value: "99.9%", label: "Uptime", icon: Award },
  { value: "150+", label: "Countries", icon: Globe }
];

export function BankingLandingPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-primary/5">
      {/* Background Effects */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <motion.div
          className="absolute top-20 left-1/4 w-40 h-40 border-2 border-primary/20 rounded-full"
          animate={{
            rotate: 360,
            scale: [1, 1.1, 1],
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "linear"
          }}
        />
        <motion.div
          className="absolute bottom-20 right-1/4 w-32 h-32 bg-primary/10 rounded-full blur-xl"
          animate={{
            scale: [1, 1.5, 1],
            opacity: [0.3, 0.6, 0.3],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
      </div>

      {/* Navigation */}
      <motion.nav
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="relative z-10 p-4 lg:p-6"
      >
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="w-8 h-8 rounded-lg bg-primary flex items-center justify-center">
              <span className="text-white font-bold text-sm">ABC</span>
            </div>
            <span className="text-xl font-bold">ABC Banking</span>
          </div>
          <div className="flex items-center gap-4">
            <Button variant="outline" asChild>
              <Link href="/login">Sign In</Link>
            </Button>
            <Button className="designer-button" asChild>
              <Link href="/register">Get Started</Link>
            </Button>
          </div>
        </div>
      </motion.nav>

      {/* Hero Section */}
      <section className="relative z-10 px-4 lg:px-6 py-12 lg:py-20">
        <div className="max-w-7xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6 }}
              className="space-y-8"
            >
              <div className="space-y-4">
                <Badge className="w-fit">
                  Trusted by 2M+ customers worldwide
                </Badge>
                <h1 className="text-4xl lg:text-6xl font-bold leading-tight">
                  Banking Made{" "}
                  <span className="banking-gradient">Simple</span>
                </h1>
                <p className="text-xl text-muted-foreground leading-relaxed">
                  Experience the future of banking with ABC Banking Enterprise. 
                  Secure, fast, and intelligent financial services designed for your success.
                </p>
              </div>

              <div className="flex flex-col sm:flex-row gap-4">
                <Button size="lg" className="designer-button" asChild>
                  <Link href="/login">
                    Access Your Account
                    <ArrowRight className="ml-2 h-5 w-5" />
                  </Link>
                </Button>
                <Button size="lg" variant="outline" asChild>
                  <Link href="/register">Open New Account</Link>
                </Button>
              </div>

              <div className="flex items-center gap-6 pt-4">
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-5 w-5 text-green-500" />
                  <span className="text-sm">FDIC Insured</span>
                </div>
                <div className="flex items-center gap-2">
                  <Lock className="h-5 w-5 text-green-500" />
                  <span className="text-sm">256-bit Encryption</span>
                </div>
                <div className="flex items-center gap-2">
                  <Shield className="h-5 w-5 text-green-500" />
                  <span className="text-sm">24/7 Monitoring</span>
                </div>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="relative"
            >
              <Card className="p-8 designer-card">
                <div className="space-y-6">
                  <div className="text-center">
                    <h3 className="text-2xl font-bold mb-2">Quick Access</h3>
                    <p className="text-muted-foreground">Sign in to your account</p>
                  </div>
                  
                  <div className="space-y-4">
                    <Button className="w-full designer-button" size="lg" asChild>
                      <Link href="/login">
                        <Shield className="mr-2 h-5 w-5" />
                        Secure Login
                      </Link>
                    </Button>
                    
                    <div className="relative">
                      <div className="absolute inset-0 flex items-center">
                        <div className="w-full border-t border-border/50" />
                      </div>
                      <div className="relative flex justify-center text-xs uppercase">
                        <span className="bg-card px-2 text-muted-foreground">New Customer?</span>
                      </div>
                    </div>
                    
                    <Button variant="outline" className="w-full" size="lg" asChild>
                      <Link href="/register">Create Account</Link>
                    </Button>
                  </div>

                  <div className="text-center text-xs text-muted-foreground">
                    Protected by industry-leading security measures
                  </div>
                </div>
              </Card>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="relative z-10 px-4 lg:px-6 py-12">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
            {stats.map((stat, index) => {
              const IconComponent = stat.icon;
              return (
                <motion.div
                  key={stat.label}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className="text-center"
                >
                  <div className="flex justify-center mb-3">
                    <div className="p-3 rounded-xl bg-primary/10">
                      <IconComponent className="h-6 w-6 text-primary" />
                    </div>
                  </div>
                  <div className="text-3xl font-bold mb-1">{stat.value}</div>
                  <div className="text-sm text-muted-foreground">{stat.label}</div>
                </motion.div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="relative z-10 px-4 lg:px-6 py-12 lg:py-20">
        <div className="max-w-7xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl lg:text-4xl font-bold mb-4">
              Why Choose ABC Banking?
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              We combine cutting-edge technology with personalized service to deliver 
              the best banking experience for individuals and businesses.
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => {
              const IconComponent = feature.icon;
              return (
                <motion.div
                  key={feature.title}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                >
                  <Card className="p-6 h-full feature-card">
                    <div className="space-y-4">
                      <div className="p-3 rounded-xl bg-primary/10 w-fit">
                        <IconComponent className="h-6 w-6 text-primary" />
                      </div>
                      <h3 className="text-xl font-semibold">{feature.title}</h3>
                      <p className="text-muted-foreground">{feature.description}</p>
                    </div>
                  </Card>
                </motion.div>
              );
            })}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="relative z-10 px-4 lg:px-6 py-12 lg:py-20">
        <div className="max-w-4xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="space-y-8"
          >
            <h2 className="text-3xl lg:text-4xl font-bold">
              Ready to Start Your Banking Journey?
            </h2>
            <p className="text-xl text-muted-foreground">
              Join millions of satisfied customers who trust ABC Banking for their financial needs.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="designer-button" asChild>
                <Link href="/register">
                  Open Account Today
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
              </Button>
              <Button size="lg" variant="outline" asChild>
                <Link href="/contact">Contact Us</Link>
              </Button>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
}
