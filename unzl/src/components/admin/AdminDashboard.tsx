"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { 
  Users, 
  CreditCard, 
  DollarSign, 
  TrendingUp, 
  TrendingDown,
  AlertTriangle,
  Shield,
  Activity,
  Building,
  FileText,
  Settings,
  Eye,
  UserCheck,
  Banknote,
  PieChart,
  BarChart3,
  Calendar,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  Pause
} from 'lucide-react';
import { formatCurrency } from "@/lib/api";
import { useAuth } from "@/contexts/AuthContext";

interface AdminDashboardData {
  totalCustomers: number;
  totalAccounts: number;
  totalTransactions: number;
  totalCards: number;
  totalLoans: number;
  totalBranches: number;
  totalDeposits: number;
  totalLoansAmount: number;
  todayTransactions: number;
  todayTransactionVolume: number;
  activeAccounts: number;
  inactiveAccounts: number;
  frozenAccounts: number;
  closedAccounts: number;
  activeCards: number;
  blockedCards: number;
  expiredCards: number;
  pendingLoans: number;
  approvedLoans: number;
  rejectedLoans: number;
  activeLoans: number;
  defaultedLoans: number;
  systemStatus: string;
  lastUpdated: string;
  recentTransactions: any[];
  recentCustomers: any[];
}

export function AdminDashboard() {
  const { user } = useAuth();
  const [dashboardData, setDashboardData] = useState<AdminDashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("overview");

  useEffect(() => {
    loadDashboardData();
    // Set up auto-refresh every 30 seconds
    const interval = setInterval(loadDashboardData, 30000);
    return () => clearInterval(interval);
  }, []);

  const loadDashboardData = async () => {
    try {
      // Mock data for now - replace with actual API call
      const mockData: AdminDashboardData = {
        totalCustomers: 1247,
        totalAccounts: 2156,
        totalTransactions: 15678,
        totalCards: 1834,
        totalLoans: 456,
        totalBranches: 12,
        totalDeposits: *********,
        totalLoansAmount: ********,
        todayTransactions: 234,
        todayTransactionVolume: 2500000,
        activeAccounts: 2089,
        inactiveAccounts: 45,
        frozenAccounts: 12,
        closedAccounts: 10,
        activeCards: 1756,
        blockedCards: 67,
        expiredCards: 11,
        pendingLoans: 23,
        approvedLoans: 398,
        rejectedLoans: 35,
        activeLoans: 387,
        defaultedLoans: 11,
        systemStatus: "HEALTHY",
        lastUpdated: new Date().toISOString(),
        recentTransactions: [],
        recentCustomers: []
      };
      
      setDashboardData(mockData);
    } catch (error) {
      console.error("Error loading dashboard data:", error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!dashboardData) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <AlertTriangle className="h-16 w-16 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold mb-2">Failed to load dashboard</h2>
          <Button onClick={loadDashboardData}>Retry</Button>
        </div>
      </div>
    );
  }

  const systemOverviewCards = [
    {
      title: "Total Customers",
      value: dashboardData.totalCustomers.toLocaleString(),
      icon: Users,
      color: "text-blue-500",
      bgColor: "bg-blue-50",
      change: "+5.2%"
    },
    {
      title: "Total Accounts",
      value: dashboardData.totalAccounts.toLocaleString(),
      icon: Building,
      color: "text-green-500",
      bgColor: "bg-green-50",
      change: "+3.8%"
    },
    {
      title: "Total Deposits",
      value: formatCurrency(dashboardData.totalDeposits),
      icon: DollarSign,
      color: "text-emerald-500",
      bgColor: "bg-emerald-50",
      change: "+7.1%"
    },
    {
      title: "Total Loans",
      value: formatCurrency(dashboardData.totalLoansAmount),
      icon: Banknote,
      color: "text-orange-500",
      bgColor: "bg-orange-50",
      change: "+2.3%"
    }
  ];

  const todayStatsCards = [
    {
      title: "Today's Transactions",
      value: dashboardData.todayTransactions.toLocaleString(),
      icon: Activity,
      color: "text-purple-500",
      bgColor: "bg-purple-50"
    },
    {
      title: "Today's Volume",
      value: formatCurrency(dashboardData.todayTransactionVolume),
      icon: TrendingUp,
      color: "text-indigo-500",
      bgColor: "bg-indigo-50"
    },
    {
      title: "Active Cards",
      value: dashboardData.activeCards.toLocaleString(),
      icon: CreditCard,
      color: "text-pink-500",
      bgColor: "bg-pink-50"
    },
    {
      title: "System Status",
      value: dashboardData.systemStatus,
      icon: Shield,
      color: "text-green-500",
      bgColor: "bg-green-50"
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Admin Dashboard</h1>
              <p className="text-gray-600 mt-1">
                Welcome back, {user?.firstName}! Here's your banking system overview.
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <Badge variant={dashboardData.systemStatus === 'HEALTHY' ? 'default' : 'destructive'}>
                <Shield className="h-4 w-4 mr-1" />
                {dashboardData.systemStatus}
              </Badge>
              <Button onClick={loadDashboardData} variant="outline" size="sm">
                <Activity className="h-4 w-4 mr-2" />
                Refresh
              </Button>
            </div>
          </div>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-6">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="customers">Customers</TabsTrigger>
            <TabsTrigger value="accounts">Accounts</TabsTrigger>
            <TabsTrigger value="transactions">Transactions</TabsTrigger>
            <TabsTrigger value="loans">Loans</TabsTrigger>
            <TabsTrigger value="reports">Reports</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            {/* System Overview Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {systemOverviewCards.map((card, index) => (
                <Card key={index} className="hover:shadow-lg transition-shadow">
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium text-gray-600">
                      {card.title}
                    </CardTitle>
                    <div className={`p-2 rounded-lg ${card.bgColor}`}>
                      <card.icon className={`h-5 w-5 ${card.color}`} />
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-gray-900">{card.value}</div>
                    {card.change && (
                      <div className="flex items-center mt-2">
                        <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
                        <span className="text-sm text-green-600">{card.change}</span>
                        <span className="text-sm text-gray-500 ml-1">from last month</span>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Today's Statistics */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {todayStatsCards.map((card, index) => (
                <Card key={index} className="hover:shadow-lg transition-shadow">
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium text-gray-600">
                      {card.title}
                    </CardTitle>
                    <div className={`p-2 rounded-lg ${card.bgColor}`}>
                      <card.icon className={`h-5 w-5 ${card.color}`} />
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-gray-900">{card.value}</div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Account Status Distribution */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <PieChart className="h-5 w-5 mr-2" />
                    Account Status Distribution
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                        <span className="text-sm">Active</span>
                      </div>
                      <span className="font-semibold">{dashboardData.activeAccounts}</span>
                    </div>
                    <Progress value={(dashboardData.activeAccounts / dashboardData.totalAccounts) * 100} className="h-2" />
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <Pause className="h-4 w-4 text-yellow-500 mr-2" />
                        <span className="text-sm">Inactive</span>
                      </div>
                      <span className="font-semibold">{dashboardData.inactiveAccounts}</span>
                    </div>
                    <Progress value={(dashboardData.inactiveAccounts / dashboardData.totalAccounts) * 100} className="h-2" />
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <AlertCircle className="h-4 w-4 text-orange-500 mr-2" />
                        <span className="text-sm">Frozen</span>
                      </div>
                      <span className="font-semibold">{dashboardData.frozenAccounts}</span>
                    </div>
                    <Progress value={(dashboardData.frozenAccounts / dashboardData.totalAccounts) * 100} className="h-2" />
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <XCircle className="h-4 w-4 text-red-500 mr-2" />
                        <span className="text-sm">Closed</span>
                      </div>
                      <span className="font-semibold">{dashboardData.closedAccounts}</span>
                    </div>
                    <Progress value={(dashboardData.closedAccounts / dashboardData.totalAccounts) * 100} className="h-2" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <BarChart3 className="h-5 w-5 mr-2" />
                    Loan Portfolio Status
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <Clock className="h-4 w-4 text-yellow-500 mr-2" />
                        <span className="text-sm">Pending</span>
                      </div>
                      <span className="font-semibold">{dashboardData.pendingLoans}</span>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                        <span className="text-sm">Approved</span>
                      </div>
                      <span className="font-semibold">{dashboardData.approvedLoans}</span>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <Activity className="h-4 w-4 text-blue-500 mr-2" />
                        <span className="text-sm">Active</span>
                      </div>
                      <span className="font-semibold">{dashboardData.activeLoans}</span>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <XCircle className="h-4 w-4 text-red-500 mr-2" />
                        <span className="text-sm">Rejected</span>
                      </div>
                      <span className="font-semibold">{dashboardData.rejectedLoans}</span>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <AlertTriangle className="h-4 w-4 text-red-600 mr-2" />
                        <span className="text-sm">Defaulted</span>
                      </div>
                      <span className="font-semibold">{dashboardData.defaultedLoans}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="customers">
            <Card>
              <CardHeader>
                <CardTitle>Customer Management</CardTitle>
                <CardDescription>
                  Manage all customer accounts and profiles
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <Users className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">Customer Management</h3>
                  <p className="text-gray-600 mb-4">
                    Customer management interface will be implemented here
                  </p>
                  <Button>View All Customers</Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="accounts">
            <Card>
              <CardHeader>
                <CardTitle>Account Management</CardTitle>
                <CardDescription>
                  Monitor and manage all bank accounts
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <Building className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">Account Management</h3>
                  <p className="text-gray-600 mb-4">
                    Account management interface will be implemented here
                  </p>
                  <Button>View All Accounts</Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="transactions">
            <Card>
              <CardHeader>
                <CardTitle>Transaction Monitoring</CardTitle>
                <CardDescription>
                  Monitor all transactions and detect suspicious activity
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <Activity className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">Transaction Monitoring</h3>
                  <p className="text-gray-600 mb-4">
                    Transaction monitoring interface will be implemented here
                  </p>
                  <Button>View All Transactions</Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="loans">
            <Card>
              <CardHeader>
                <CardTitle>Loan Management</CardTitle>
                <CardDescription>
                  Manage loan applications and portfolio
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <Banknote className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">Loan Management</h3>
                  <p className="text-gray-600 mb-4">
                    Loan management interface will be implemented here
                  </p>
                  <Button>View All Loans</Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="reports">
            <Card>
              <CardHeader>
                <CardTitle>Reports & Analytics</CardTitle>
                <CardDescription>
                  Generate comprehensive reports and analytics
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <FileText className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">Reports & Analytics</h3>
                  <p className="text-gray-600 mb-4">
                    Reporting interface will be implemented here
                  </p>
                  <Button>Generate Reports</Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Footer */}
        <div className="mt-8 text-center text-sm text-gray-500">
          Last updated: {new Date(dashboardData.lastUpdated).toLocaleString()}
        </div>
      </div>
    </div>
  );
}
