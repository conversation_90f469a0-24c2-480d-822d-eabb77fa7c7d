"use client";

import { useState } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { motion } from "framer-motion";
import { useAuth } from "@/contexts/AuthContext";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  Home,
  CreditCard,
  ArrowLeftRight,
  PiggyBank,
  FileText,
  Settings,
  Bell,
  User,
  LogOut,
  Menu,
  X,
  Shield
} from "lucide-react";

const navigationItems = [
  {
    id: "dashboard",
    title: "Dashboard",
    icon: Home,
    href: "/dashboard",
    badge: null
  },
  {
    id: "accounts",
    title: "Accounts",
    icon: CreditCard,
    href: "/accounts",
    badge: null
  },
  {
    id: "transfers",
    title: "Transfers",
    icon: ArrowLeftRight,
    href: "/transfers",
    badge: null
  },
  {
    id: "savings",
    title: "Savings",
    icon: PiggyBank,
    href: "/savings",
    badge: "New"
  },
  {
    id: "statements",
    title: "Statements",
    icon: FileText,
    href: "/statements",
    badge: null
  },
  {
    id: "settings",
    title: "Settings",
    icon: Settings,
    href: "/settings",
    badge: null
  }
];

interface MainNavigationProps {
  currentPath?: string;
}

export function MainNavigation({ currentPath }: MainNavigationProps) {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const pathname = usePathname();
  const { user, logout } = useAuth();
  
  // Don't show navigation on landing page or login page
  if (pathname === "/" || pathname === "/login" || pathname === "/register") {
    return null;
  }

  return (
    <motion.aside
      initial={{ x: -300 }}
      animate={{ x: 0 }}
      transition={{ duration: 0.3 }}
      className={`${
        isCollapsed ? "w-16" : "w-64"
      } bg-card border-r border-border/50 flex flex-col transition-all duration-300 relative z-10`}
    >
      {/* Header */}
      <div className="p-4 border-b border-border/50">
        <div className="flex items-center justify-between">
          {!isCollapsed && (
            <div className="flex items-center gap-2">
              <div className="w-8 h-8 rounded-lg bg-primary flex items-center justify-center">
                <span className="text-white font-bold text-sm">ABC</span>
              </div>
              <span className="font-bold text-lg">ABC Banking</span>
            </div>
          )}
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setIsCollapsed(!isCollapsed)}
            className="h-8 w-8"
          >
            {isCollapsed ? <Menu className="h-4 w-4" /> : <X className="h-4 w-4" />}
          </Button>
        </div>
      </div>

      {/* Navigation Items */}
      <nav className="flex-1 p-4 space-y-2">
        {navigationItems.map((item) => {
          const IconComponent = item.icon;
          const isActive = pathname === item.href || currentPath === item.href;
          
          return (
            <Link key={item.id} href={item.href}>
              <motion.div
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                className={`flex items-center gap-3 p-3 rounded-lg transition-colors relative ${
                  isActive
                    ? "bg-primary text-primary-foreground"
                    : "hover:bg-muted/50 text-muted-foreground hover:text-foreground"
                }`}
              >
                <IconComponent className="h-5 w-5 flex-shrink-0" />
                {!isCollapsed && (
                  <>
                    <span className="font-medium">{item.title}</span>
                    {item.badge && (
                      <Badge variant="secondary" className="ml-auto text-xs">
                        {item.badge}
                      </Badge>
                    )}
                  </>
                )}
                {isActive && (
                  <motion.div
                    layoutId="activeTab"
                    className="absolute inset-0 bg-primary rounded-lg -z-10"
                    initial={false}
                    transition={{ type: "spring", bounce: 0.2, duration: 0.6 }}
                  />
                )}
              </motion.div>
            </Link>
          );
        })}
      </nav>

      {/* User Section */}
      <div className="p-4 border-t border-border/50 space-y-2">
        <div className={`flex items-center gap-3 p-3 rounded-lg bg-muted/30 ${
          isCollapsed ? "justify-center" : ""
        }`}>
          <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
            <User className="h-4 w-4 text-primary" />
          </div>
          {!isCollapsed && (
            <div className="flex-1 min-w-0">
              <p className="font-medium text-sm truncate">
                {user ? `${user.firstName} ${user.lastName}` : 'User'}
              </p>
              <p className="text-xs text-muted-foreground truncate">
                {user?.email || '<EMAIL>'}
              </p>
            </div>
          )}
        </div>
        
        <div className="flex gap-2">
          <Button
            variant="ghost"
            size={isCollapsed ? "icon" : "sm"}
            className={isCollapsed ? "w-full" : "flex-1"}
          >
            <Bell className="h-4 w-4" />
            {!isCollapsed && <span className="ml-2">Notifications</span>}
          </Button>
          
          <Button
            variant="ghost"
            size={isCollapsed ? "icon" : "sm"}
            className={isCollapsed ? "w-full" : "flex-1"}
            onClick={logout}
          >
            <LogOut className="h-4 w-4" />
            {!isCollapsed && <span className="ml-2">Logout</span>}
          </Button>
        </div>

        {/* Security Badge */}
        {!isCollapsed && (
          <div className="flex items-center gap-2 p-2 rounded-lg bg-green-500/10 border border-green-500/20">
            <Shield className="h-4 w-4 text-green-500" />
            <div className="flex-1">
              <p className="text-xs font-medium text-green-500">Secure Session</p>
              <p className="text-xs text-muted-foreground">Protected by 256-bit SSL</p>
            </div>
          </div>
        )}
      </div>
    </motion.aside>
  );
}
