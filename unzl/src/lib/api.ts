// API Configuration and Service Layer
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080/api';

// API Response wrapper
export interface ApiResponse<T = any> {
  message: string;
  success: boolean;
  timestamp: string;
  data?: T;
  errorCode?: string;
}

// Types
export interface User {
  id: number;
  username: string;
  email: string;
  firstName: string;
  lastName: string;
  phoneNumber?: string;
  dateOfBirth?: string;
  address?: string;
  status: 'ACTIVE' | 'INACTIVE' | 'SUSPENDED';
  createdAt: string;
  lastLogin?: string;
}

export interface Account {
  id: number;
  accountNumber: string;
  accountHolderName: string;
  accountType: 'SAVINGS' | 'CURRENT' | 'FIXED_DEPOSIT' | 'RECURRING_DEPOSIT' | 'LOAN' | 'CREDIT_CARD' | 'DEBIT_CARD' | 'BUSINESS' | 'CORPORATE' | 'NRI';
  balance: number;
  currency: string;
  status: 'ACTIVE' | 'INACTIVE' | 'SUSPENDED' | 'CLOSED' | 'FROZEN' | 'PENDING_APPROVAL' | 'APPROVED' | 'REJECTED' | 'DORMANT';
  interestRate: number;
  minimumBalance: number;
  overdraftLimit?: number;
  availableBalance?: number;
  creditLimit?: number;
  openingDate: string;
  lastTransactionDate?: string;
  createdAt: string;
  updatedAt: string;
  branch?: {
    id: number;
    branchName: string;
    branchCode: string;
    ifscCode: string;
  };
}

export interface Transaction {
  id: number;
  transactionId: string;
  fromAccountId?: number;
  toAccountId?: number;
  type: 'DEPOSIT' | 'WITHDRAWAL' | 'TRANSFER' | 'CREDIT' | 'DEBIT' | 'LOAN_DISBURSEMENT' | 'LOAN_REPAYMENT' | 'INTEREST_CREDIT' | 'INTEREST_DEBIT' | 'FEE_DEBIT' | 'FEE_CREDIT' | 'EMI_PAYMENT' | 'CARD_PAYMENT' | 'ONLINE_PAYMENT' | 'MOBILE_PAYMENT' | 'UPI_TRANSFER' | 'NEFT_TRANSFER' | 'RTGS_TRANSFER' | 'IMPS_TRANSFER' | 'CASH_DEPOSIT' | 'CASH_WITHDRAWAL' | 'CHEQUE_DEPOSIT' | 'CHEQUE_PAYMENT' | 'STANDING_INSTRUCTION' | 'RECURRING_PAYMENT' | 'REFUND' | 'CHARGEBACK' | 'REVERSAL';
  amount: number;
  currency: string;
  description?: string;
  referenceNumber?: string;
  status: 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'FAILED' | 'CANCELLED' | 'REVERSED' | 'SUSPENDED' | 'UNDER_REVIEW' | 'APPROVED' | 'REJECTED' | 'TIMEOUT' | 'INSUFFICIENT_FUNDS' | 'ACCOUNT_FROZEN' | 'LIMIT_EXCEEDED' | 'SUSPICIOUS';
  category?: string;
  channel?: string;
  location?: string;
  transactionDate: string;
  processedDate?: string;
  createdAt: string;
  fromAccount?: Account;
  toAccount?: Account;
}

export interface Card {
  id: number;
  cardNumber: string;
  maskedCardNumber: string;
  cardHolderName: string;
  cardType: 'DEBIT' | 'CREDIT' | 'PREPAID' | 'BUSINESS_DEBIT' | 'BUSINESS_CREDIT';
  expiryDate: string;
  status: 'ACTIVE' | 'INACTIVE' | 'BLOCKED' | 'EXPIRED' | 'PENDING_ACTIVATION';
  dailyLimit: number;
  monthlyLimit: number;
  creditLimit?: number;
  availableCredit?: number;
  outstandingBalance?: number;
  isBlocked: boolean;
  blockReason?: string;
  contactlessEnabled: boolean;
  onlineTransactionsEnabled: boolean;
  internationalTransactionsEnabled: boolean;
  atmTransactionsEnabled: boolean;
  posTransactionsEnabled: boolean;
  rewardsPoints: number;
  cashbackEarned: number;
  accountId: number;
  accountName: string;
}

export interface Notification {
  id: number;
  title: string;
  message: string;
  type: 'TRANSACTION_ALERT' | 'SECURITY_ALERT' | 'ACCOUNT_UPDATE' | 'CARD_ALERT' | 'WELCOME';
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT' | 'CRITICAL';
  status: 'PENDING' | 'SENT' | 'DELIVERED' | 'FAILED';
  isRead: boolean;
  readAt?: string;
  sentAt?: string;
  category?: string;
  actionUrl?: string;
  actionText?: string;
  userId: number;
  createdAt: string;
  updatedAt: string;
}

export interface LoginRequest {
  username: string;
  password: string;
}

export interface LoginResponse {
  token: string;
  refreshToken: string;
  user: User;
  expiresIn: number;
}

export interface TransferRequest {
  fromAccountId: number;
  toAccountId?: number;
  toAccountNumber?: string;
  amount: number;
  description?: string;
  transferType: 'INTERNAL' | 'EXTERNAL' | 'INTERNATIONAL';
}

// API Client Class
class ApiClient {
  private baseURL: string;
  private token: string | null = null;

  constructor(baseURL: string) {
    this.baseURL = baseURL;
    // Try to get token from localStorage on client side
    if (typeof window !== 'undefined') {
      this.token = localStorage.getItem('auth_token');
    }
  }

  setToken(token: string) {
    this.token = token;
    if (typeof window !== 'undefined') {
      localStorage.setItem('auth_token', token);
    }
  }

  clearToken() {
    this.token = null;
    if (typeof window !== 'undefined') {
      localStorage.removeItem('auth_token');
      localStorage.removeItem('refresh_token');
    }
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseURL}${endpoint}`;
    
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      ...options.headers,
    };

    if (this.token) {
      headers.Authorization = `Bearer ${this.token}`;
    }

    const config: RequestInit = {
      ...options,
      headers,
    };

    try {
      const response = await fetch(url, config);
      
      if (!response.ok) {
        if (response.status === 401) {
          // Token expired or invalid
          this.clearToken();
          throw new Error('Authentication required');
        }
        
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
      }

      const contentType = response.headers.get('content-type');
      if (contentType && contentType.includes('application/json')) {
        return await response.json();
      }
      
      return response.text() as unknown as T;
    } catch (error) {
      console.error(`API request failed: ${endpoint}`, error);
      throw error;
    }
  }

  // Authentication
  async login(credentials: LoginRequest): Promise<LoginResponse> {
    const response = await this.request<LoginResponse>('/auth/login', {
      method: 'POST',
      body: JSON.stringify(credentials),
    });
    
    this.setToken(response.token);
    if (typeof window !== 'undefined') {
      localStorage.setItem('refresh_token', response.refreshToken);
    }
    
    return response;
  }

  async logout(): Promise<void> {
    try {
      await this.request('/auth/logout', {
        method: 'POST',
      });
    } finally {
      this.clearToken();
    }
  }

  async refreshToken(): Promise<LoginResponse> {
    const refreshToken = typeof window !== 'undefined' 
      ? localStorage.getItem('refresh_token') 
      : null;
    
    if (!refreshToken) {
      throw new Error('No refresh token available');
    }

    const response = await this.request<LoginResponse>('/auth/refresh', {
      method: 'POST',
      body: JSON.stringify({ refreshToken }),
    });
    
    this.setToken(response.token);
    return response;
  }

  // User Management
  async getCurrentUser(): Promise<User> {
    return this.request<User>('/users/me');
  }

  async updateUser(userData: Partial<User>): Promise<User> {
    return this.request<User>('/users/me', {
      method: 'PUT',
      body: JSON.stringify(userData),
    });
  }

  // Account Management
  async getAccounts(): Promise<Account[]> {
    return this.request<Account[]>('/accounts');
  }

  async getAccount(accountId: number): Promise<Account> {
    return this.request<Account>(`/accounts/${accountId}`);
  }

  async createAccount(accountData: Partial<Account>): Promise<Account> {
    return this.request<Account>('/accounts', {
      method: 'POST',
      body: JSON.stringify(accountData),
    });
  }

  // Transaction Management
  async getTransactions(accountId?: number, limit = 50, offset = 0): Promise<Transaction[]> {
    const params = new URLSearchParams({
      limit: limit.toString(),
      offset: offset.toString(),
    });
    
    if (accountId) {
      params.append('accountId', accountId.toString());
    }
    
    return this.request<Transaction[]>(`/transactions?${params}`);
  }

  async getTransaction(transactionId: string): Promise<Transaction> {
    return this.request<Transaction>(`/transactions/${transactionId}`);
  }

  async createTransfer(transferData: TransferRequest): Promise<Transaction> {
    return this.request<Transaction>('/transactions/transfer', {
      method: 'POST',
      body: JSON.stringify(transferData),
    });
  }

  // Card Management
  async getCards(): Promise<Card[]> {
    return this.request<Card[]>('/cards');
  }

  async getCard(cardId: number): Promise<Card> {
    return this.request<Card>(`/cards/${cardId}`);
  }

  async blockCard(cardId: number, reason: string): Promise<ApiResponse> {
    return this.request<ApiResponse>(`/cards/${cardId}/block`, {
      method: 'POST',
      body: JSON.stringify({ reason }),
    });
  }

  async unblockCard(cardId: number): Promise<ApiResponse> {
    return this.request<ApiResponse>(`/cards/${cardId}/unblock`, {
      method: 'POST',
    });
  }

  // Notification Management
  async getNotifications(): Promise<Notification[]> {
    return this.request<Notification[]>('/notifications');
  }

  async getUnreadNotifications(): Promise<Notification[]> {
    return this.request<Notification[]>('/notifications/unread');
  }

  async getUnreadCount(): Promise<number> {
    return this.request<number>('/notifications/unread/count');
  }

  async markNotificationAsRead(notificationId: number): Promise<ApiResponse> {
    return this.request<ApiResponse>(`/notifications/${notificationId}/read`, {
      method: 'POST',
    });
  }

  // Dashboard Data
  async getDashboardData(): Promise<{
    accounts: Account[];
    recentTransactions: Transaction[];
    totalBalance: number;
    monthlySpending: number;
    savingsGoalProgress: number;
  }> {
    return this.request('/dashboard');
  }

  // Health Check
  async healthCheck(): Promise<{ status: string; timestamp: string }> {
    return this.request('/test/health');
  }
}

// Create and export API client instance
export const apiClient = new ApiClient(API_BASE_URL);

// Card API functions
export const cardApi = {
  getCards: (userId?: number) =>
    apiClient.request<Card[]>(`/cards${userId ? `?userId=${userId}` : ''}`),

  getCard: (cardId: number) =>
    apiClient.request<Card>(`/cards/${cardId}`),

  getCardsByAccount: (accountId: number) =>
    apiClient.request<Card[]>(`/cards/account/${accountId}`),

  createCard: (cardData: any) =>
    apiClient.request<Card>('/cards', { method: 'POST', body: JSON.stringify(cardData) }),

  updateCard: (cardId: number, cardData: any) =>
    apiClient.request<Card>(`/cards/${cardId}`, { method: 'PUT', body: JSON.stringify(cardData) }),

  blockCard: (cardId: number, reason: string, blockedBy?: string) =>
    apiClient.request<ApiResponse>(`/cards/${cardId}/block`, {
      method: 'POST',
      body: JSON.stringify({ reason, blockedBy })
    }),

  unblockCard: (cardId: number) =>
    apiClient.request<ApiResponse>(`/cards/${cardId}/unblock`, { method: 'POST' }),

  activateCard: (cardId: number, pin: string) =>
    apiClient.request<ApiResponse>(`/cards/${cardId}/activate`, {
      method: 'POST',
      body: JSON.stringify({ pin })
    }),

  changePin: (cardId: number, oldPin: string, newPin: string) =>
    apiClient.request<ApiResponse>(`/cards/${cardId}/change-pin`, {
      method: 'POST',
      body: JSON.stringify({ oldPin, newPin })
    }),

  setLimits: (cardId: number, limits: any) =>
    apiClient.request<ApiResponse>(`/cards/${cardId}/limits`, {
      method: 'POST',
      body: JSON.stringify(limits)
    }),

  requestReplacement: (cardId: number, reason: string) =>
    apiClient.request<Card>(`/cards/${cardId}/replace`, {
      method: 'POST',
      body: JSON.stringify({ reason })
    }),

  reportLostOrStolen: (cardId: number, reason: string) =>
    apiClient.request<ApiResponse>(`/cards/${cardId}/report`, {
      method: 'POST',
      body: JSON.stringify({ reason })
    }),

  getCardStats: (userId?: number) =>
    apiClient.request<any>(`/cards/stats${userId ? `?userId=${userId}` : ''}`),
};

// Notification API functions
export const notificationApi = {
  getNotifications: (userId?: number, page = 0, size = 20) =>
    apiClient.request<Notification[]>(`/notifications${userId ? `?userId=${userId}` : ''}&page=${page}&size=${size}`),

  getUnreadNotifications: (userId?: number) =>
    apiClient.request<Notification[]>(`/notifications/unread${userId ? `?userId=${userId}` : ''}`),

  getUnreadCount: (userId?: number) =>
    apiClient.request<number>(`/notifications/unread/count${userId ? `?userId=${userId}` : ''}`),

  getNotification: (notificationId: number) =>
    apiClient.request<Notification>(`/notifications/${notificationId}`),

  markAsRead: (notificationId: number) =>
    apiClient.request<ApiResponse>(`/notifications/${notificationId}/read`, { method: 'POST' }),

  markAllAsRead: (userId?: number) =>
    apiClient.request<ApiResponse>(`/notifications/read-all${userId ? `?userId=${userId}` : ''}`, { method: 'POST' }),

  getNotificationsByType: (type: string, userId?: number) =>
    apiClient.request<Notification[]>(`/notifications/type/${type}${userId ? `?userId=${userId}` : ''}`),

  sendTestNotification: (userId?: number) =>
    apiClient.request<ApiResponse>(`/notifications/test${userId ? `?userId=${userId}` : ''}`, { method: 'POST' }),
};

// Utility functions
export const formatCurrency = (amount: number, currency = 'INR'): string => {
  return new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency,
  }).format(amount);
};

export const formatDate = (dateString: string): string => {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  }).format(new Date(dateString));
};

export const getTransactionTypeColor = (type: Transaction['transactionType']): string => {
  switch (type) {
    case 'CREDIT':
    case 'DEPOSIT':
      return 'text-green-500';
    case 'DEBIT':
    case 'WITHDRAWAL':
      return 'text-red-500';
    case 'TRANSFER':
      return 'text-blue-500';
    case 'FEE':
      return 'text-orange-500';
    case 'INTEREST':
      return 'text-purple-500';
    default:
      return 'text-gray-500';
  }
};

export const getStatusColor = (status: string): string => {
  switch (status.toUpperCase()) {
    case 'ACTIVE':
    case 'COMPLETED':
      return 'text-green-500';
    case 'PENDING':
      return 'text-yellow-500';
    case 'INACTIVE':
    case 'FAILED':
    case 'CANCELLED':
      return 'text-red-500';
    case 'FROZEN':
    case 'SUSPENDED':
      return 'text-orange-500';
    default:
      return 'text-gray-500';
  }
};

// Error handling utility
export const handleApiError = (error: any): string => {
  if (error.message) {
    return error.message;
  }
  if (typeof error === 'string') {
    return error;
  }
  return 'An unexpected error occurred';
};
