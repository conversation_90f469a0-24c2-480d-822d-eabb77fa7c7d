// Indian Currency Formatting Utilities

/**
 * Formats a number as Indian Rupees with proper comma placement
 * @param amount - The amount to format
 * @param showSymbol - Whether to show the ₹ symbol (default: true)
 * @param showDecimals - Whether to show decimal places (default: true)
 * @returns Formatted currency string
 */
export function formatINR(
  amount: number | string, 
  showSymbol: boolean = true, 
  showDecimals: boolean = true
): string {
  const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
  
  if (isNaN(numAmount)) {
    return showSymbol ? '₹0' : '0';
  }

  // Format with Indian number system (lakhs and crores)
  const formatter = new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: 'INR',
    minimumFractionDigits: showDecimals ? 2 : 0,
    maximumFractionDigits: showDecimals ? 2 : 0,
  });

  let formatted = formatter.format(numAmount);
  
  if (!showSymbol) {
    // Remove the ₹ symbol
    formatted = formatted.replace('₹', '').trim();
  }

  return formatted;
}

/**
 * Formats amount in Indian number system with words (lakhs, crores)
 * @param amount - The amount to format
 * @param showSymbol - Whether to show the ₹ symbol
 * @returns Formatted string with Indian number words
 */
export function formatINRWithWords(amount: number | string, showSymbol: boolean = true): string {
  const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
  
  if (isNaN(numAmount)) {
    return showSymbol ? '₹0' : '0';
  }

  const symbol = showSymbol ? '₹' : '';
  
  if (numAmount >= ********) { // 1 crore
    const crores = numAmount / ********;
    return `${symbol}${crores.toFixed(2)} Cr`;
  } else if (numAmount >= 100000) { // 1 lakh
    const lakhs = numAmount / 100000;
    return `${symbol}${lakhs.toFixed(2)} L`;
  } else if (numAmount >= 1000) { // 1 thousand
    const thousands = numAmount / 1000;
    return `${symbol}${thousands.toFixed(2)} K`;
  } else {
    return formatINR(numAmount, showSymbol, true);
  }
}

/**
 * Formats amount for display in cards and summaries
 * @param amount - The amount to format
 * @param compact - Whether to use compact format (K, L, Cr)
 * @returns Formatted currency string
 */
export function formatCurrency(amount: number | string, compact: boolean = false): string {
  if (compact) {
    return formatINRWithWords(amount, true);
  }
  return formatINR(amount, true, true);
}

/**
 * Parses Indian formatted currency string to number
 * @param currencyString - The currency string to parse
 * @returns Parsed number or 0 if invalid
 */
export function parseINR(currencyString: string): number {
  if (!currencyString) return 0;
  
  // Remove currency symbol and commas
  const cleanString = currencyString
    .replace(/₹/g, '')
    .replace(/,/g, '')
    .trim();
  
  const parsed = parseFloat(cleanString);
  return isNaN(parsed) ? 0 : parsed;
}

/**
 * Validates if a string is a valid Indian currency amount
 * @param value - The value to validate
 * @returns True if valid currency amount
 */
export function isValidINRAmount(value: string): boolean {
  const cleaned = value.replace(/₹|,/g, '').trim();
  const num = parseFloat(cleaned);
  return !isNaN(num) && num >= 0;
}

/**
 * Formats amount for input fields (without symbol, with proper decimals)
 * @param amount - The amount to format
 * @returns Formatted string for input
 */
export function formatForInput(amount: number | string): string {
  const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
  
  if (isNaN(numAmount)) {
    return '';
  }

  return numAmount.toFixed(2);
}

/**
 * Currency constants for Indian banking
 */
export const CURRENCY_CONFIG = {
  symbol: '₹',
  code: 'INR',
  name: 'Indian Rupee',
  locale: 'en-IN',
  minAmount: 1,
  maxTransactionAmount: ********, // 1 Crore
  maxDailyLimit: 200000, // 2 Lakhs
  maxMonthlyLimit: 1000000, // 10 Lakhs
};

/**
 * Common Indian banking amounts for quick selection
 */
export const QUICK_AMOUNTS = [
  { label: '₹500', value: 500 },
  { label: '₹1,000', value: 1000 },
  { label: '₹2,000', value: 2000 },
  { label: '₹5,000', value: 5000 },
  { label: '₹10,000', value: 10000 },
  { label: '₹25,000', value: 25000 },
  { label: '₹50,000', value: 50000 },
  { label: '₹1,00,000', value: 100000 },
];

/**
 * Formats transaction amount with appropriate color coding
 * @param amount - The transaction amount
 * @param type - Transaction type (CREDIT/DEBIT)
 * @returns Object with formatted amount and color class
 */
export function formatTransactionAmount(amount: number, type: 'CREDIT' | 'DEBIT') {
  const formatted = formatINR(amount, true, true);
  const colorClass = type === 'CREDIT' ? 'text-green-600' : 'text-red-600';
  const prefix = type === 'CREDIT' ? '+' : '-';
  
  return {
    amount: `${prefix}${formatted}`,
    colorClass,
    formatted
  };
}

/**
 * Formats account balance with status indication
 * @param balance - Account balance
 * @param minBalance - Minimum balance requirement
 * @returns Object with formatted balance and status
 */
export function formatAccountBalance(balance: number, minBalance: number = 0) {
  const formatted = formatINR(balance, true, true);
  const isLow = balance < minBalance * 2; // Warning when balance is less than 2x minimum
  const isCritical = balance < minBalance;
  
  let status: 'healthy' | 'low' | 'critical' = 'healthy';
  let colorClass = 'text-green-600';
  
  if (isCritical) {
    status = 'critical';
    colorClass = 'text-red-600';
  } else if (isLow) {
    status = 'low';
    colorClass = 'text-yellow-600';
  }
  
  return {
    formatted,
    status,
    colorClass,
    isLow,
    isCritical
  };
}
