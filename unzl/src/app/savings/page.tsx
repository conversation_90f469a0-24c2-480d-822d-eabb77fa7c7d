"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import { MainNavigation } from "@/components/navigation/MainNavigation";
import { ProtectedRoute } from "@/contexts/AuthContext";
import { Card } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { 
  PiggyBank,
  Target,
  Plus,
  TrendingUp,
  Calendar,
  DollarSign,
  Edit,
  Trash2,
  CheckCircle
} from "lucide-react";

const savingsGoals = [
  {
    id: "1",
    title: "Emergency Fund",
    description: "6 months of expenses",
    targetAmount: 25000,
    currentAmount: 18750,
    deadline: "2024-12-31",
    category: "Emergency",
    color: "bg-red-500",
    progress: 75
  },
  {
    id: "2", 
    title: "Vacation to Europe",
    description: "Dream vacation with family",
    targetAmount: 8000,
    currentAmount: 3200,
    deadline: "2024-08-15",
    category: "Travel",
    color: "bg-blue-500",
    progress: 40
  },
  {
    id: "3",
    title: "New Car",
    description: "Down payment for new vehicle",
    targetAmount: 15000,
    currentAmount: 12000,
    deadline: "2024-06-30",
    category: "Vehicle",
    color: "bg-green-500",
    progress: 80
  },
  {
    id: "4",
    title: "Home Renovation",
    description: "Kitchen and bathroom upgrade",
    targetAmount: 30000,
    currentAmount: 5000,
    deadline: "2025-03-01",
    category: "Home",
    color: "bg-purple-500",
    progress: 17
  }
];

export default function SavingsPage() {
  const [showNewGoalForm, setShowNewGoalForm] = useState(false);
  const [newGoal, setNewGoal] = useState({
    title: "",
    description: "",
    targetAmount: "",
    deadline: "",
    category: ""
  });

  const totalSavings = savingsGoals.reduce((sum, goal) => sum + goal.currentAmount, 0);
  const totalTargets = savingsGoals.reduce((sum, goal) => sum + goal.targetAmount, 0);
  const overallProgress = (totalSavings / totalTargets) * 100;

  const handleCreateGoal = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle goal creation logic
    console.log("Creating goal:", newGoal);
    setShowNewGoalForm(false);
    setNewGoal({ title: "", description: "", targetAmount: "", deadline: "", category: "" });
  };

  return (
    <ProtectedRoute>
      <div className="flex min-h-screen bg-background">
        <MainNavigation currentPath="/savings" />
        
        <div className="flex-1 flex flex-col">
          {/* Header */}
          <motion.header
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="bg-card border-b border-border/50 p-4 lg:p-6"
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="p-2 rounded-lg bg-pink-500/10">
                  <PiggyBank className="h-6 w-6 text-pink-500" />
                </div>
                <div>
                  <h1 className="text-2xl lg:text-3xl font-bold">Savings Goals</h1>
                  <p className="text-muted-foreground">Track and achieve your financial goals</p>
                </div>
              </div>
              <Button 
                onClick={() => setShowNewGoalForm(true)}
                className="designer-button"
              >
                <Plus className="h-4 w-4 mr-2" />
                New Goal
              </Button>
            </div>
          </motion.header>

          {/* Main Content */}
          <main className="flex-1 p-4 lg:p-6 space-y-6">
            {/* Summary Cards */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
              >
                <Card className="p-6">
                  <div className="space-y-2">
                    <p className="text-sm text-muted-foreground">Total Saved</p>
                    <p className="text-3xl font-bold">${totalSavings.toLocaleString()}</p>
                    <div className="flex items-center gap-1">
                      <TrendingUp className="h-4 w-4 text-green-500" />
                      <span className="text-sm text-green-500">+12.5% this month</span>
                    </div>
                  </div>
                </Card>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.1 }}
              >
                <Card className="p-6">
                  <div className="space-y-2">
                    <p className="text-sm text-muted-foreground">Target Amount</p>
                    <p className="text-3xl font-bold">${totalTargets.toLocaleString()}</p>
                    <div className="flex items-center gap-1">
                      <Target className="h-4 w-4 text-blue-500" />
                      <span className="text-sm text-blue-500">{savingsGoals.length} active goals</span>
                    </div>
                  </div>
                </Card>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
              >
                <Card className="p-6">
                  <div className="space-y-2">
                    <p className="text-sm text-muted-foreground">Overall Progress</p>
                    <p className="text-3xl font-bold">{overallProgress.toFixed(1)}%</p>
                    <Progress value={overallProgress} className="h-2" />
                  </div>
                </Card>
              </motion.div>
            </div>

            {/* New Goal Form */}
            {showNewGoalForm && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
              >
                <Card className="p-6">
                  <h2 className="text-xl font-semibold mb-4">Create New Savings Goal</h2>
                  <form onSubmit={handleCreateGoal} className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <label className="text-sm font-medium">Goal Title</label>
                        <Input
                          placeholder="e.g., Emergency Fund"
                          value={newGoal.title}
                          onChange={(e) => setNewGoal({...newGoal, title: e.target.value})}
                          required
                        />
                      </div>
                      <div className="space-y-2">
                        <label className="text-sm font-medium">Category</label>
                        <select 
                          className="w-full p-3 rounded-lg border border-border bg-background"
                          value={newGoal.category}
                          onChange={(e) => setNewGoal({...newGoal, category: e.target.value})}
                          required
                        >
                          <option value="">Select category</option>
                          <option value="Emergency">Emergency</option>
                          <option value="Travel">Travel</option>
                          <option value="Vehicle">Vehicle</option>
                          <option value="Home">Home</option>
                          <option value="Education">Education</option>
                          <option value="Investment">Investment</option>
                        </select>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Description</label>
                      <Input
                        placeholder="Brief description of your goal"
                        value={newGoal.description}
                        onChange={(e) => setNewGoal({...newGoal, description: e.target.value})}
                      />
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <label className="text-sm font-medium">Target Amount ($)</label>
                        <Input
                          type="number"
                          placeholder="10000"
                          value={newGoal.targetAmount}
                          onChange={(e) => setNewGoal({...newGoal, targetAmount: e.target.value})}
                          required
                        />
                      </div>
                      <div className="space-y-2">
                        <label className="text-sm font-medium">Target Date</label>
                        <Input
                          type="date"
                          value={newGoal.deadline}
                          onChange={(e) => setNewGoal({...newGoal, deadline: e.target.value})}
                          required
                        />
                      </div>
                    </div>
                    <div className="flex gap-3">
                      <Button type="submit" className="designer-button">
                        Create Goal
                      </Button>
                      <Button 
                        type="button" 
                        variant="outline" 
                        onClick={() => setShowNewGoalForm(false)}
                      >
                        Cancel
                      </Button>
                    </div>
                  </form>
                </Card>
              </motion.div>
            )}

            {/* Savings Goals Grid */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {savingsGoals.map((goal, index) => (
                <motion.div
                  key={goal.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                >
                  <Card className="p-6 designer-card">
                    <div className="space-y-4">
                      <div className="flex items-start justify-between">
                        <div className="flex items-center gap-3">
                          <div className={`w-3 h-3 rounded-full ${goal.color}`} />
                          <div>
                            <h3 className="font-semibold text-lg">{goal.title}</h3>
                            <p className="text-sm text-muted-foreground">{goal.description}</p>
                          </div>
                        </div>
                        <Badge variant="secondary">{goal.category}</Badge>
                      </div>

                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span>${goal.currentAmount.toLocaleString()}</span>
                          <span>${goal.targetAmount.toLocaleString()}</span>
                        </div>
                        <Progress value={goal.progress} className="h-2" />
                        <p className="text-sm text-muted-foreground text-center">
                          {goal.progress}% complete
                        </p>
                      </div>

                      <div className="flex items-center justify-between pt-2">
                        <div className="flex items-center gap-1 text-sm text-muted-foreground">
                          <Calendar className="h-4 w-4" />
                          <span>Due: {new Date(goal.deadline).toLocaleDateString()}</span>
                        </div>
                        <div className="flex gap-2">
                          <Button variant="ghost" size="icon">
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="icon">
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  </Card>
                </motion.div>
              ))}
            </div>
          </main>
        </div>
      </div>
    </ProtectedRoute>
  );
}
