"use client";

import { motion } from "framer-motion";
import { MainNavigation } from "@/components/navigation/MainNavigation";
import { AccountCard } from "@/components/banking/AccountCard";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  Plus,
  Search,
  Filter,
  Download,
  Eye,
  EyeOff,
  TrendingUp,
  TrendingDown,
  MoreHorizontal
} from "lucide-react";

// Mock data
const accounts = [
  {
    id: "1",
    accountNumber: "**********",
    accountType: "Checking Account",
    balance: 15420.50,
    currency: "USD",
    status: "active" as const,
    lastTransaction: "2 hours ago",
    interestRate: 0.5,
    monthlyChange: 12.5
  },
  {
    id: "2",
    accountNumber: "**********",
    accountType: "Savings Account",
    balance: 45680.75,
    currency: "USD",
    status: "active" as const,
    lastTransaction: "1 day ago",
    interestRate: 2.5,
    monthlyChange: 8.3
  },
  {
    id: "3",
    accountNumber: "**********",
    accountType: "Business Account",
    balance: 125000.00,
    currency: "USD",
    status: "active" as const,
    lastTransaction: "3 hours ago",
    interestRate: 1.2,
    monthlyChange: -2.1
  },
  {
    id: "4",
    accountNumber: "**********",
    accountType: "Investment Account",
    balance: 78950.25,
    currency: "USD",
    status: "active" as const,
    lastTransaction: "5 hours ago",
    interestRate: 4.8,
    monthlyChange: 15.7
  }
];

export default function AccountsPage() {
  return (
    <div className="flex min-h-screen bg-background">
      <MainNavigation currentPath="/accounts" />
      
      <div className="flex-1 flex flex-col">
        {/* Header */}
        <motion.header
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="bg-card border-b border-border/50 p-4 lg:p-6"
        >
          <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
            <div>
              <h1 className="text-2xl lg:text-3xl font-bold">My Accounts</h1>
              <p className="text-muted-foreground">Manage and monitor all your accounts</p>
            </div>
            <div className="flex items-center gap-3">
              <Button variant="outline" size="icon">
                <Search className="h-4 w-4" />
              </Button>
              <Button variant="outline" size="icon">
                <Filter className="h-4 w-4" />
              </Button>
              <Button variant="outline" size="icon">
                <Download className="h-4 w-4" />
              </Button>
              <Button className="designer-button">
                <Plus className="h-4 w-4 mr-2" />
                Open New Account
              </Button>
            </div>
          </div>
        </motion.header>

        {/* Main Content */}
        <main className="flex-1 p-4 lg:p-6 space-y-6">
          {/* Summary Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Card className="p-6">
                <div className="space-y-2">
                  <p className="text-sm text-muted-foreground">Total Balance</p>
                  <p className="text-3xl font-bold">$265,051.50</p>
                  <div className="flex items-center gap-1">
                    <TrendingUp className="h-4 w-4 text-green-500" />
                    <span className="text-sm text-green-500">+8.7% this month</span>
                  </div>
                </div>
              </Card>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
            >
              <Card className="p-6">
                <div className="space-y-2">
                  <p className="text-sm text-muted-foreground">Active Accounts</p>
                  <p className="text-3xl font-bold">{accounts.length}</p>
                  <div className="flex items-center gap-1">
                    <Badge variant="secondary">All Active</Badge>
                  </div>
                </div>
              </Card>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              <Card className="p-6">
                <div className="space-y-2">
                  <p className="text-sm text-muted-foreground">Monthly Interest</p>
                  <p className="text-3xl font-bold">$1,247.83</p>
                  <div className="flex items-center gap-1">
                    <TrendingUp className="h-4 w-4 text-green-500" />
                    <span className="text-sm text-green-500">+12.3% vs last month</span>
                  </div>
                </div>
              </Card>
            </motion.div>
          </div>

          {/* Accounts Grid */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-semibold">Account Details</h2>
              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm">
                  <Eye className="h-4 w-4 mr-2" />
                  Show Balances
                </Button>
              </div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {accounts.map((account, index) => (
                <motion.div
                  key={account.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                >
                  <Card className="p-6 designer-card">
                    <div className="space-y-4">
                      <div className="flex items-start justify-between">
                        <div>
                          <h3 className="font-semibold text-lg">{account.accountType}</h3>
                          <p className="text-sm text-muted-foreground">
                            ****{account.accountNumber.slice(-4)}
                          </p>
                        </div>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </div>

                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-muted-foreground">Available Balance</span>
                          <Badge variant={account.status === "active" ? "default" : "secondary"}>
                            {account.status}
                          </Badge>
                        </div>
                        <p className="text-2xl font-bold">
                          ${account.balance.toLocaleString('en-US', { minimumFractionDigits: 2 })}
                        </p>
                      </div>

                      <div className="grid grid-cols-2 gap-4 pt-4 border-t border-border/50">
                        <div>
                          <p className="text-xs text-muted-foreground">Interest Rate</p>
                          <p className="font-medium">{account.interestRate}% APY</p>
                        </div>
                        <div>
                          <p className="text-xs text-muted-foreground">Monthly Change</p>
                          <div className="flex items-center gap-1">
                            {account.monthlyChange > 0 ? (
                              <TrendingUp className="h-3 w-3 text-green-500" />
                            ) : (
                              <TrendingDown className="h-3 w-3 text-red-500" />
                            )}
                            <span className={`text-sm font-medium ${
                              account.monthlyChange > 0 ? "text-green-500" : "text-red-500"
                            }`}>
                              {account.monthlyChange > 0 ? "+" : ""}{account.monthlyChange}%
                            </span>
                          </div>
                        </div>
                      </div>

                      <div className="flex gap-2 pt-2">
                        <Button variant="outline" size="sm" className="flex-1">
                          View Details
                        </Button>
                        <Button size="sm" className="flex-1">
                          Transfer
                        </Button>
                      </div>
                    </div>
                  </Card>
                </motion.div>
              ))}
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}
