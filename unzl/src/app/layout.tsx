import "./globals.css";
import type { <PERSON>adata } from "next";
import { ThemeProvider } from "@/components/ThemeProvider";
import { AuthProvider } from "@/contexts/AuthContext";

export const metadata: Metadata = {
  title: {
    default: "ABC Banking Enterprise - Secure Digital Banking",
    template: "%s | ABC Banking",
  },
  description:
    "ABC Banking Enterprise provides secure, modern digital banking services with advanced account management, seamless transfers, loan services, and comprehensive financial tools for individuals and businesses.",
  keywords: ["Digital Banking", "Online Banking", "Secure Banking", "Account Management", "Money Transfer", "Loans", "Financial Services", "Enterprise Banking"],
  authors: [{ name: "ABC Banking Development Team" }],
  creator: "ABC Banking Enterprise",
  icons: {
    icon: [
      { url: "/images/abc-banking-logo.png", sizes: "32x32", type: "image/png" },
      { url: "/images/abc-banking-logo.png", sizes: "16x16", type: "image/png" },
    ],
    apple: [
      { url: "/images/abc-banking-logo.png", sizes: "180x180", type: "image/png" },
    ],
    shortcut: "/images/abc-banking-logo.png",
  },
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://abcbanking.com",
    title: "ABC Banking Enterprise - Secure Digital Banking",
    description: "Secure, modern digital banking services with advanced account management, seamless transfers, and comprehensive financial tools.",
    siteName: "ABC Banking Enterprise",
    images: [
      {
        url: "/images/abc-banking-logo.png",
        width: 1200,
        height: 630,
        alt: "ABC Banking Enterprise - Secure Digital Banking",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "ABC Banking Enterprise - Secure Digital Banking",
    description: "Secure, modern digital banking services with advanced account management, seamless transfers, and comprehensive financial tools.",
    images: ["/images/abc-banking-logo.png"],
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body>
        <ThemeProvider attribute="class" defaultTheme="dark" enableSystem>
          <AuthProvider>
            {children}
          </AuthProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
