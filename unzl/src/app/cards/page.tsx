"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { MainNavigation } from "@/components/navigation/MainNavigation";
import { ProtectedRoute } from "@/contexts/AuthContext";
import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { cardApi, handleApiError, Card as CardType } from "@/lib/api";
import { 
  CreditCard,
  Plus,
  Lock,
  Unlock,
  Eye,
  EyeOff,
  Settings,
  ShoppingCart,
  Plane,
  Car,
  MoreHorizontal,
  AlertTriangle,
  CheckCircle,
  Calendar,
  DollarSign
} from "lucide-react";

const cards = [
  {
    id: "1",
    type: "Debit",
    name: "Primary Checking Card",
    number: "4532 **** **** 1234",
    fullNumber: "4532 1234 5678 1234",
    expiryDate: "12/26",
    cvv: "123",
    status: "Active",
    balance: 15420.50,
    accountName: "Primary Checking",
    isBlocked: false,
    dailyLimit: 2500,
    monthlySpent: 1850.75,
    cardColor: "bg-gradient-to-br from-blue-600 to-blue-800"
  },
  {
    id: "2", 
    type: "Credit",
    name: "ABC Rewards Credit Card",
    number: "5555 **** **** 5678",
    fullNumber: "5555 1234 5678 5678",
    expiryDate: "08/27",
    cvv: "456",
    status: "Active",
    balance: 8750.25,
    creditLimit: 15000,
    availableCredit: 6249.75,
    isBlocked: false,
    monthlySpent: 2340.50,
    cardColor: "bg-gradient-to-br from-purple-600 to-purple-800"
  },
  {
    id: "3",
    type: "Debit",
    name: "Business Account Card", 
    number: "4111 **** **** 9012",
    fullNumber: "4111 1234 5678 9012",
    expiryDate: "03/25",
    cvv: "789",
    status: "Blocked",
    balance: 28750.00,
    accountName: "Business Account",
    isBlocked: true,
    dailyLimit: 5000,
    monthlySpent: 4250.30,
    cardColor: "bg-gradient-to-br from-gray-600 to-gray-800"
  }
];

const recentTransactions = [
  { id: "1", merchant: "Amazon", amount: 89.99, date: "2024-01-15", category: "Shopping" },
  { id: "2", merchant: "Starbucks", amount: 12.50, date: "2024-01-15", category: "Food" },
  { id: "3", merchant: "Shell Gas Station", amount: 45.20, date: "2024-01-14", category: "Gas" },
  { id: "4", merchant: "Netflix", amount: 15.99, date: "2024-01-13", category: "Entertainment" }
];

export default function CardsPage() {
  const [apiCards, setApiCards] = useState<CardType[]>([]);
  const [selectedCard, setSelectedCard] = useState<any>(null);
  const [showCardDetails, setShowCardDetails] = useState(false);
  const [showNewCardForm, setShowNewCardForm] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load cards on component mount
  useEffect(() => {
    loadCards();
  }, []);

  const loadCards = async () => {
    try {
      setLoading(true);
      setError(null);
      const userCards = await cardApi.getCards(1); // Using userId 1 for demo
      setApiCards(userCards);
      if (userCards.length > 0) {
        setSelectedCard(userCards[0]);
      }
    } catch (err) {
      setError(handleApiError(err));
      // Fallback to static data if API fails
      setSelectedCard(cards[0]);
    } finally {
      setLoading(false);
    }
  };

  const handleToggleCard = async (cardId: string) => {
    try {
      const card = apiCards.find(c => c.id.toString() === cardId);
      if (card) {
        if (card.isBlocked) {
          await cardApi.unblockCard(card.id);
        } else {
          await cardApi.blockCard(card.id, "User requested block");
        }
        await loadCards(); // Reload cards
      }
    } catch (err) {
      setError(handleApiError(err));
    }
  };

  const handleCardSettings = (cardId: string) => {
    console.log(`Opening settings for card: ${cardId}`);
  };

  // Use API cards if available, otherwise fallback to static data
  const displayCards = apiCards.length > 0 ? apiCards.map(card => ({
    id: card.id.toString(),
    type: card.cardType,
    name: `${card.cardType} Card`,
    number: card.maskedCardNumber,
    fullNumber: card.cardNumber,
    expiryDate: card.expiryDate,
    status: card.status,
    balance: card.outstandingBalance || 0,
    creditLimit: card.creditLimit,
    availableCredit: card.availableCredit,
    isBlocked: card.isBlocked,
    dailyLimit: card.dailyLimit,
    monthlySpent: 0, // This would come from transaction data
    cardColor: card.cardType === 'CREDIT' ? "bg-gradient-to-br from-purple-600 to-purple-800" : "bg-gradient-to-br from-blue-600 to-blue-800"
  })) : cards;

  return (
    <ProtectedRoute>
      <div className="flex min-h-screen bg-background">
        <MainNavigation currentPath="/cards" />
        
        <div className="flex-1 flex flex-col">
          {/* Header */}
          <motion.header
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="bg-card border-b border-border/50 p-4 lg:p-6"
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="p-2 rounded-lg bg-purple-500/10">
                  <CreditCard className="h-6 w-6 text-purple-500" />
                </div>
                <div>
                  <h1 className="text-2xl lg:text-3xl font-bold">Manage Cards</h1>
                  <p className="text-muted-foreground">Control your debit and credit cards</p>
                </div>
              </div>
              <Button 
                onClick={() => setShowNewCardForm(true)}
                className="designer-button"
              >
                <Plus className="h-4 w-4 mr-2" />
                Request New Card
              </Button>
            </div>
          </motion.header>

          {/* Main Content */}
          <main className="flex-1 p-4 lg:p-6 space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Cards List */}
              <div className="lg:col-span-2 space-y-4">
                <h2 className="text-xl font-semibold">Your Cards</h2>

                {loading && (
                  <div className="flex justify-center items-center py-12">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                    <span className="ml-2">Loading cards...</span>
                  </div>
                )}

                {error && (
                  <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
                    <p className="text-red-700">{error}</p>
                    <Button onClick={loadCards} variant="outline" className="mt-2">
                      Retry
                    </Button>
                  </div>
                )}

                {displayCards.map((card, index) => (
                  <motion.div
                    key={card.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    className={`cursor-pointer ${selectedCard.id === card.id ? 'ring-2 ring-primary' : ''}`}
                    onClick={() => setSelectedCard(card)}
                  >
                    <Card className="p-6 designer-card">
                      <div className="space-y-4">
                        {/* Card Visual */}
                        <div className={`relative h-48 rounded-xl ${card.cardColor} text-white p-6 overflow-hidden`}>
                          <div className="absolute top-0 right-0 w-32 h-32 rounded-full bg-white/10 -translate-y-16 translate-x-16" />
                          <div className="absolute bottom-0 left-0 w-24 h-24 rounded-full bg-white/5 translate-y-12 -translate-x-12" />
                          
                          <div className="relative z-10 h-full flex flex-col justify-between">
                            <div className="flex justify-between items-start">
                              <div>
                                <p className="text-sm opacity-80">{card.type} Card</p>
                                <p className="font-semibold">{card.name}</p>
                              </div>
                              <Badge 
                                variant={card.status === "Active" ? "default" : "destructive"}
                                className={card.status === "Active" ? "bg-green-500" : ""}
                              >
                                {card.status}
                              </Badge>
                            </div>
                            
                            <div>
                              <p className="text-lg font-mono tracking-wider mb-2">
                                {showCardDetails ? card.fullNumber : card.number}
                              </p>
                              <div className="flex justify-between items-end">
                                <div>
                                  <p className="text-xs opacity-60">EXPIRES</p>
                                  <p className="text-sm">{card.expiryDate}</p>
                                </div>
                                {showCardDetails && (
                                  <div>
                                    <p className="text-xs opacity-60">CVV</p>
                                    <p className="text-sm">{card.cvv}</p>
                                  </div>
                                )}
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* Card Info */}
                        <div className="space-y-3">
                          <div className="flex items-center justify-between">
                            <div className="space-y-1">
                              {card.type === "Debit" ? (
                                <>
                                  <p className="text-sm text-muted-foreground">Available Balance</p>
                                  <p className="text-xl font-bold">${card.balance?.toLocaleString()}</p>
                                </>
                              ) : (
                                <>
                                  <p className="text-sm text-muted-foreground">Available Credit</p>
                                  <p className="text-xl font-bold">${card.availableCredit?.toLocaleString()}</p>
                                  <p className="text-xs text-muted-foreground">
                                    of ${card.creditLimit?.toLocaleString()} limit
                                  </p>
                                </>
                              )}
                            </div>
                            
                            <div className="flex gap-2">
                              <Button
                                variant="outline"
                                size="icon"
                                onClick={() => setShowCardDetails(!showCardDetails)}
                              >
                                {showCardDetails ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                              </Button>
                              <Button
                                variant="outline"
                                size="icon"
                                onClick={() => handleCardSettings(card.id)}
                              >
                                <Settings className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>

                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              {card.isBlocked ? (
                                <Lock className="h-4 w-4 text-red-500" />
                              ) : (
                                <Unlock className="h-4 w-4 text-green-500" />
                              )}
                              <span className="text-sm">
                                {card.isBlocked ? "Card Blocked" : "Card Active"}
                              </span>
                            </div>
                            <Switch
                              checked={!card.isBlocked}
                              onCheckedChange={() => handleToggleCard(card.id)}
                            />
                          </div>

                          {card.type === "Debit" && (
                            <div className="space-y-2">
                              <div className="flex justify-between text-sm">
                                <span>Daily Limit</span>
                                <span>${card.dailyLimit?.toLocaleString()}</span>
                              </div>
                              <div className="flex justify-between text-sm">
                                <span>Monthly Spent</span>
                                <span>${card.monthlySpent?.toLocaleString()}</span>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    </Card>
                  </motion.div>
                ))}
              </div>

              {/* Card Details & Actions */}
              <div className="space-y-6">
                {/* Quick Actions */}
                <motion.div
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5 }}
                >
                  <Card className="p-6">
                    <h3 className="font-semibold mb-4">Quick Actions</h3>
                    
                    <div className="space-y-3">
                      <Button variant="outline" className="w-full justify-start">
                        <Lock className="h-4 w-4 mr-2" />
                        {selectedCard.isBlocked ? "Unblock Card" : "Block Card"}
                      </Button>
                      
                      <Button variant="outline" className="w-full justify-start">
                        <Settings className="h-4 w-4 mr-2" />
                        Card Settings
                      </Button>
                      
                      <Button variant="outline" className="w-full justify-start">
                        <DollarSign className="h-4 w-4 mr-2" />
                        Set Limits
                      </Button>
                      
                      <Button variant="outline" className="w-full justify-start">
                        <AlertTriangle className="h-4 w-4 mr-2" />
                        Report Lost/Stolen
                      </Button>
                      
                      <Button variant="outline" className="w-full justify-start">
                        <Calendar className="h-4 w-4 mr-2" />
                        Request Replacement
                      </Button>
                    </div>
                  </Card>
                </motion.div>

                {/* Recent Transactions */}
                <motion.div
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5, delay: 0.1 }}
                >
                  <Card className="p-6">
                    <h3 className="font-semibold mb-4">Recent Transactions</h3>
                    
                    <div className="space-y-3">
                      {recentTransactions.map((transaction) => (
                        <div key={transaction.id} className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <div className="w-8 h-8 rounded-full bg-muted/50 flex items-center justify-center">
                              {transaction.category === "Shopping" && <ShoppingCart className="h-4 w-4" />}
                              {transaction.category === "Food" && <DollarSign className="h-4 w-4" />}
                              {transaction.category === "Gas" && <Car className="h-4 w-4" />}
                              {transaction.category === "Entertainment" && <Plane className="h-4 w-4" />}
                            </div>
                            <div>
                              <p className="font-medium text-sm">{transaction.merchant}</p>
                              <p className="text-xs text-muted-foreground">{transaction.date}</p>
                            </div>
                          </div>
                          <p className="font-medium text-sm">-${transaction.amount}</p>
                        </div>
                      ))}
                    </div>
                    
                    <Button variant="outline" className="w-full mt-4">
                      View All Transactions
                    </Button>
                  </Card>
                </motion.div>

                {/* Card Benefits */}
                <motion.div
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5, delay: 0.2 }}
                >
                  <Card className="p-6">
                    <h3 className="font-semibold mb-4">Card Benefits</h3>
                    
                    <div className="space-y-3 text-sm">
                      <div className="flex items-center gap-2">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        <span>24/7 Fraud Protection</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        <span>Zero Liability Protection</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        <span>Contactless Payments</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        <span>Global ATM Access</span>
                      </div>
                      {selectedCard.type === "Credit" && (
                        <>
                          <div className="flex items-center gap-2">
                            <CheckCircle className="h-4 w-4 text-green-500" />
                            <span>Cashback Rewards</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <CheckCircle className="h-4 w-4 text-green-500" />
                            <span>Travel Insurance</span>
                          </div>
                        </>
                      )}
                    </div>
                  </Card>
                </motion.div>
              </div>
            </div>
          </main>
        </div>
      </div>
    </ProtectedRoute>
  );
}
