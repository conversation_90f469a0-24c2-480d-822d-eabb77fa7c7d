"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import { MainNavigation } from "@/components/navigation/MainNavigation";
import { Card } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { 
  ArrowLeftRight,
  Send,
  Clock,
  CheckCircle,
  AlertCircle,
  Plus,
  Search,
  Filter,
  Download
} from "lucide-react";

// Mock data
const recentTransfers = [
  {
    id: "1",
    type: "sent",
    recipient: "<PERSON>",
    amount: 500.00,
    date: "2024-01-15T10:30:00Z",
    status: "completed",
    reference: "Rent payment"
  },
  {
    id: "2",
    type: "received",
    sender: "ABC Corp",
    amount: 2500.00,
    date: "2024-01-14T14:20:00Z",
    status: "completed",
    reference: "Salary"
  },
  {
    id: "3",
    type: "sent",
    recipient: "Electric Company",
    amount: 125.50,
    date: "2024-01-13T09:15:00Z",
    status: "pending",
    reference: "Utility bill"
  }
];

const quickTransferContacts = [
  { id: "1", name: "<PERSON>", account: "****1234", avatar: "J<PERSON>" },
  { id: "2", name: "<PERSON>", account: "****5678", avatar: "SW" },
  { id: "3", name: "Mike <PERSON>", account: "****9012", avatar: "MJ" },
  { id: "4", name: "Emily <PERSON>", account: "****3456", avatar: "ED" }
];

export default function TransfersPage() {
  const [transferForm, setTransferForm] = useState({
    fromAccount: "",
    toAccount: "",
    amount: "",
    reference: "",
    transferType: "internal"
  });

  const handleTransfer = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle transfer logic
    console.log("Transfer:", transferForm);
  };

  return (
    <div className="flex min-h-screen bg-background">
      <MainNavigation currentPath="/transfers" />
      
      <div className="flex-1 flex flex-col">
        {/* Header */}
        <motion.header
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="bg-card border-b border-border/50 p-4 lg:p-6"
        >
          <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
            <div>
              <h1 className="text-2xl lg:text-3xl font-bold">Transfers</h1>
              <p className="text-muted-foreground">Send money quickly and securely</p>
            </div>
            <div className="flex items-center gap-3">
              <Button variant="outline" size="icon">
                <Search className="h-4 w-4" />
              </Button>
              <Button variant="outline" size="icon">
                <Filter className="h-4 w-4" />
              </Button>
              <Button variant="outline" size="icon">
                <Download className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </motion.header>

        {/* Main Content */}
        <main className="flex-1 p-4 lg:p-6 space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Transfer Form */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Card className="p-6 designer-card">
                <div className="space-y-6">
                  <div className="flex items-center gap-3">
                    <div className="p-2 rounded-lg bg-primary/10">
                      <Send className="h-5 w-5 text-primary" />
                    </div>
                    <div>
                      <h2 className="text-xl font-semibold">New Transfer</h2>
                      <p className="text-sm text-muted-foreground">Send money to anyone</p>
                    </div>
                  </div>

                  <form onSubmit={handleTransfer} className="space-y-4">
                    <div className="space-y-2">
                      <label className="text-sm font-medium">From Account</label>
                      <select 
                        className="w-full p-3 rounded-lg border border-border bg-background"
                        value={transferForm.fromAccount}
                        onChange={(e) => setTransferForm({...transferForm, fromAccount: e.target.value})}
                      >
                        <option value="">Select account</option>
                        <option value="checking">Checking Account (****1234)</option>
                        <option value="savings">Savings Account (****5678)</option>
                        <option value="business">Business Account (****9012)</option>
                      </select>
                    </div>

                    <div className="space-y-2">
                      <label className="text-sm font-medium">To Account/Email</label>
                      <Input
                        placeholder="Account number or email"
                        value={transferForm.toAccount}
                        onChange={(e) => setTransferForm({...transferForm, toAccount: e.target.value})}
                      />
                    </div>

                    <div className="space-y-2">
                      <label className="text-sm font-medium">Amount</label>
                      <div className="relative">
                        <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground">$</span>
                        <Input
                          type="number"
                          placeholder="0.00"
                          className="pl-8"
                          value={transferForm.amount}
                          onChange={(e) => setTransferForm({...transferForm, amount: e.target.value})}
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <label className="text-sm font-medium">Reference (Optional)</label>
                      <Input
                        placeholder="What's this for?"
                        value={transferForm.reference}
                        onChange={(e) => setTransferForm({...transferForm, reference: e.target.value})}
                      />
                    </div>

                    <div className="flex gap-2">
                      <Button
                        type="button"
                        variant={transferForm.transferType === "internal" ? "default" : "outline"}
                        size="sm"
                        onClick={() => setTransferForm({...transferForm, transferType: "internal"})}
                      >
                        Internal
                      </Button>
                      <Button
                        type="button"
                        variant={transferForm.transferType === "external" ? "default" : "outline"}
                        size="sm"
                        onClick={() => setTransferForm({...transferForm, transferType: "external"})}
                      >
                        External
                      </Button>
                      <Button
                        type="button"
                        variant={transferForm.transferType === "international" ? "default" : "outline"}
                        size="sm"
                        onClick={() => setTransferForm({...transferForm, transferType: "international"})}
                      >
                        International
                      </Button>
                    </div>

                    <Button type="submit" className="w-full designer-button">
                      <Send className="h-4 w-4 mr-2" />
                      Send Transfer
                    </Button>
                  </form>
                </div>
              </Card>
            </motion.div>

            {/* Quick Transfer & Recent */}
            <div className="space-y-6">
              {/* Quick Transfer */}
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: 0.1 }}
              >
                <Card className="p-6">
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold">Quick Transfer</h3>
                    <div className="grid grid-cols-2 gap-3">
                      {quickTransferContacts.map((contact) => (
                        <div
                          key={contact.id}
                          className="p-3 rounded-lg border border-border/50 hover:border-primary/50 cursor-pointer transition-colors"
                        >
                          <div className="flex items-center gap-3">
                            <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center text-xs font-medium">
                              {contact.avatar}
                            </div>
                            <div className="flex-1 min-w-0">
                              <p className="font-medium text-sm truncate">{contact.name}</p>
                              <p className="text-xs text-muted-foreground">{contact.account}</p>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                    <Button variant="outline" className="w-full">
                      <Plus className="h-4 w-4 mr-2" />
                      Add Contact
                    </Button>
                  </div>
                </Card>
              </motion.div>

              {/* Recent Transfers */}
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
              >
                <Card className="p-6">
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold">Recent Transfers</h3>
                    <div className="space-y-3">
                      {recentTransfers.map((transfer) => (
                        <div
                          key={transfer.id}
                          className="flex items-center justify-between p-3 rounded-lg bg-muted/30"
                        >
                          <div className="flex items-center gap-3">
                            <div className={`p-2 rounded-lg ${
                              transfer.type === "sent" ? "bg-red-500/10" : "bg-green-500/10"
                            }`}>
                              <ArrowLeftRight className={`h-4 w-4 ${
                                transfer.type === "sent" ? "text-red-500" : "text-green-500"
                              }`} />
                            </div>
                            <div>
                              <p className="font-medium text-sm">
                                {transfer.type === "sent" ? transfer.recipient : transfer.sender}
                              </p>
                              <p className="text-xs text-muted-foreground">{transfer.reference}</p>
                            </div>
                          </div>
                          <div className="text-right">
                            <p className={`font-medium text-sm ${
                              transfer.type === "sent" ? "text-red-500" : "text-green-500"
                            }`}>
                              {transfer.type === "sent" ? "-" : "+"}${transfer.amount.toFixed(2)}
                            </p>
                            <div className="flex items-center gap-1">
                              {transfer.status === "completed" ? (
                                <CheckCircle className="h-3 w-3 text-green-500" />
                              ) : transfer.status === "pending" ? (
                                <Clock className="h-3 w-3 text-yellow-500" />
                              ) : (
                                <AlertCircle className="h-3 w-3 text-red-500" />
                              )}
                              <span className="text-xs text-muted-foreground capitalize">
                                {transfer.status}
                              </span>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </Card>
              </motion.div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}
