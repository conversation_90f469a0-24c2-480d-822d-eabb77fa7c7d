"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import { MainNavigation } from "@/components/navigation/MainNavigation";
import { ProtectedRoute } from "@/contexts/AuthContext";
import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { 
  Download,
  Camera,
  Upload,
  CreditCard,
  Building,
  Smartphone,
  CheckCircle,
  AlertCircle,
  Clock,
  DollarSign,
  FileImage
} from "lucide-react";

const depositMethods = [
  {
    id: "mobile",
    title: "Mobile Check Deposit",
    description: "Take photos of your check with your phone",
    icon: Smartphone,
    color: "text-blue-500",
    bgColor: "bg-blue-500/10",
    available: true,
    processingTime: "1-2 business days"
  },
  {
    id: "atm",
    title: "ATM Deposit",
    description: "Deposit cash or checks at any ABC Banking ATM",
    icon: Building,
    color: "text-green-500", 
    bgColor: "bg-green-500/10",
    available: true,
    processingTime: "Immediate for cash"
  },
  {
    id: "wire",
    title: "Wire Transfer",
    description: "Receive money via wire transfer",
    icon: CreditCard,
    color: "text-purple-500",
    bgColor: "bg-purple-500/10",
    available: true,
    processingTime: "Same day"
  },
  {
    id: "ach",
    title: "ACH Transfer",
    description: "Transfer from external bank account",
    icon: Download,
    color: "text-orange-500",
    bgColor: "bg-orange-500/10",
    available: true,
    processingTime: "3-5 business days"
  }
];

const myAccounts = [
  { id: "1", name: "Primary Checking", number: "****1234", balance: 15420.50, type: "Checking" },
  { id: "2", name: "Savings Account", number: "****5678", balance: 45230.75, type: "Savings" },
  { id: "3", name: "Business Account", number: "****9012", balance: 28750.00, type: "Business" }
];

export default function DepositPage() {
  const [selectedMethod, setSelectedMethod] = useState<string>("");
  const [depositData, setDepositData] = useState({
    account: "",
    amount: "",
    description: "",
    checkFront: null as File | null,
    checkBack: null as File | null
  });
  const [step, setStep] = useState(1); // 1: Method, 2: Details, 3: Confirmation

  const handleMethodSelect = (methodId: string) => {
    setSelectedMethod(methodId);
    setStep(2);
  };

  const handleFileUpload = (type: 'front' | 'back', file: File) => {
    setDepositData({
      ...depositData,
      [type === 'front' ? 'checkFront' : 'checkBack']: file
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log("Processing deposit:", { method: selectedMethod, ...depositData });
    setStep(3);
  };

  const resetForm = () => {
    setStep(1);
    setSelectedMethod("");
    setDepositData({
      account: "",
      amount: "",
      description: "",
      checkFront: null,
      checkBack: null
    });
  };

  return (
    <ProtectedRoute>
      <div className="flex min-h-screen bg-background">
        <MainNavigation currentPath="/deposit" />
        
        <div className="flex-1 flex flex-col">
          {/* Header */}
          <motion.header
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="bg-card border-b border-border/50 p-4 lg:p-6"
          >
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-lg bg-green-500/10">
                <Download className="h-6 w-6 text-green-500" />
              </div>
              <div>
                <h1 className="text-2xl lg:text-3xl font-bold">Deposit Funds</h1>
                <p className="text-muted-foreground">Add money to your account quickly and securely</p>
              </div>
            </div>
          </motion.header>

          {/* Main Content */}
          <main className="flex-1 p-4 lg:p-6">
            <div className="max-w-4xl mx-auto space-y-6">
              {/* Progress Steps */}
              <div className="flex items-center justify-center space-x-4 mb-8">
                {[1, 2, 3].map((stepNum) => (
                  <div key={stepNum} className="flex items-center">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                      step >= stepNum 
                        ? "bg-primary text-primary-foreground" 
                        : "bg-muted text-muted-foreground"
                    }`}>
                      {step > stepNum ? <CheckCircle className="h-4 w-4" /> : stepNum}
                    </div>
                    {stepNum < 3 && (
                      <div className={`w-16 h-1 mx-2 ${
                        step > stepNum ? "bg-primary" : "bg-muted"
                      }`} />
                    )}
                  </div>
                ))}
              </div>

              {step === 1 && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5 }}
                >
                  <Card className="p-6">
                    <h2 className="text-xl font-semibold mb-6">Choose Deposit Method</h2>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {depositMethods.map((method) => {
                        const IconComponent = method.icon;
                        
                        return (
                          <motion.div
                            key={method.id}
                            initial={{ opacity: 0, scale: 0.95 }}
                            animate={{ opacity: 1, scale: 1 }}
                            transition={{ duration: 0.3 }}
                            className={`p-6 rounded-lg border cursor-pointer transition-all ${
                              method.available 
                                ? "hover:border-primary/50 hover:shadow-md" 
                                : "opacity-50 cursor-not-allowed"
                            }`}
                            onClick={() => method.available && handleMethodSelect(method.id)}
                          >
                            <div className="space-y-4">
                              <div className="flex items-start justify-between">
                                <div className={`p-3 rounded-xl ${method.bgColor}`}>
                                  <IconComponent className={`h-6 w-6 ${method.color}`} />
                                </div>
                                {method.available ? (
                                  <Badge className="bg-green-500">Available</Badge>
                                ) : (
                                  <Badge variant="secondary">Coming Soon</Badge>
                                )}
                              </div>
                              
                              <div>
                                <h3 className="font-semibold text-lg mb-2">{method.title}</h3>
                                <p className="text-sm text-muted-foreground mb-3">
                                  {method.description}
                                </p>
                                <div className="flex items-center gap-1 text-sm text-muted-foreground">
                                  <Clock className="h-4 w-4" />
                                  <span>{method.processingTime}</span>
                                </div>
                              </div>
                            </div>
                          </motion.div>
                        );
                      })}
                    </div>
                  </Card>
                </motion.div>
              )}

              {step === 2 && (
                <motion.div
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5 }}
                >
                  <Card className="p-6 max-w-2xl mx-auto">
                    <h2 className="text-xl font-semibold mb-6">
                      {depositMethods.find(m => m.id === selectedMethod)?.title} Details
                    </h2>
                    
                    <form onSubmit={handleSubmit} className="space-y-6">
                      <div className="space-y-2">
                        <label className="text-sm font-medium">Deposit To Account</label>
                        <select 
                          className="w-full p-3 rounded-lg border border-border bg-background"
                          value={depositData.account}
                          onChange={(e) => setDepositData({...depositData, account: e.target.value})}
                          required
                        >
                          <option value="">Select account</option>
                          {myAccounts.map((account) => (
                            <option key={account.id} value={account.id}>
                              {account.name} ({account.number}) - ${account.balance.toLocaleString()}
                            </option>
                          ))}
                        </select>
                      </div>

                      <div className="space-y-2">
                        <label className="text-sm font-medium">Amount ($)</label>
                        <Input
                          type="number"
                          step="0.01"
                          placeholder="0.00"
                          value={depositData.amount}
                          onChange={(e) => setDepositData({...depositData, amount: e.target.value})}
                          required
                        />
                      </div>

                      <div className="space-y-2">
                        <label className="text-sm font-medium">Description (Optional)</label>
                        <Input
                          placeholder="Deposit description"
                          value={depositData.description}
                          onChange={(e) => setDepositData({...depositData, description: e.target.value})}
                        />
                      </div>

                      {selectedMethod === "mobile" && (
                        <div className="space-y-4">
                          <div className="space-y-2">
                            <label className="text-sm font-medium">Check Front Image</label>
                            <div className="border-2 border-dashed border-border rounded-lg p-6 text-center">
                              <input
                                type="file"
                                accept="image/*"
                                onChange={(e) => e.target.files?.[0] && handleFileUpload('front', e.target.files[0])}
                                className="hidden"
                                id="check-front"
                              />
                              <label htmlFor="check-front" className="cursor-pointer">
                                {depositData.checkFront ? (
                                  <div className="space-y-2">
                                    <FileImage className="h-8 w-8 text-green-500 mx-auto" />
                                    <p className="text-sm text-green-500">Front image uploaded</p>
                                  </div>
                                ) : (
                                  <div className="space-y-2">
                                    <Camera className="h-8 w-8 text-muted-foreground mx-auto" />
                                    <p className="text-sm text-muted-foreground">
                                      Click to upload or take photo of check front
                                    </p>
                                  </div>
                                )}
                              </label>
                            </div>
                          </div>

                          <div className="space-y-2">
                            <label className="text-sm font-medium">Check Back Image</label>
                            <div className="border-2 border-dashed border-border rounded-lg p-6 text-center">
                              <input
                                type="file"
                                accept="image/*"
                                onChange={(e) => e.target.files?.[0] && handleFileUpload('back', e.target.files[0])}
                                className="hidden"
                                id="check-back"
                              />
                              <label htmlFor="check-back" className="cursor-pointer">
                                {depositData.checkBack ? (
                                  <div className="space-y-2">
                                    <FileImage className="h-8 w-8 text-green-500 mx-auto" />
                                    <p className="text-sm text-green-500">Back image uploaded</p>
                                  </div>
                                ) : (
                                  <div className="space-y-2">
                                    <Camera className="h-8 w-8 text-muted-foreground mx-auto" />
                                    <p className="text-sm text-muted-foreground">
                                      Click to upload or take photo of check back
                                    </p>
                                  </div>
                                )}
                              </label>
                            </div>
                          </div>

                          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                            <div className="flex items-start gap-2">
                              <AlertCircle className="h-5 w-5 text-blue-500 mt-0.5" />
                              <div className="text-sm">
                                <p className="font-medium text-blue-900 mb-1">Photo Tips:</p>
                                <ul className="text-blue-700 space-y-1">
                                  <li>• Ensure all four corners are visible</li>
                                  <li>• Use good lighting and avoid shadows</li>
                                  <li>• Keep the check flat and straight</li>
                                  <li>• Make sure all text is clearly readable</li>
                                </ul>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}

                      <div className="flex gap-3">
                        <Button 
                          type="button"
                          variant="outline" 
                          onClick={() => setStep(1)}
                          className="flex-1"
                        >
                          Back
                        </Button>
                        <Button 
                          type="submit"
                          className="flex-1 designer-button"
                          disabled={selectedMethod === "mobile" && (!depositData.checkFront || !depositData.checkBack)}
                        >
                          Process Deposit
                        </Button>
                      </div>
                    </form>
                  </Card>
                </motion.div>
              )}

              {step === 3 && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.95 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.5 }}
                >
                  <Card className="p-8 max-w-2xl mx-auto text-center">
                    <div className="w-16 h-16 rounded-full bg-green-500/10 flex items-center justify-center mx-auto mb-4">
                      <CheckCircle className="h-8 w-8 text-green-500" />
                    </div>
                    
                    <h2 className="text-2xl font-bold mb-2">Deposit Submitted!</h2>
                    <p className="text-muted-foreground mb-6">
                      Your deposit of ${depositData.amount} has been submitted for processing.
                    </p>
                    
                    <div className="space-y-3 mb-6">
                      <div className="flex justify-between">
                        <span>Deposit ID:</span>
                        <span className="font-mono">DEP-{Date.now()}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Processing Time:</span>
                        <span>{depositMethods.find(m => m.id === selectedMethod)?.processingTime}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Status:</span>
                        <Badge className="bg-yellow-500">Processing</Badge>
                      </div>
                    </div>

                    <div className="flex gap-3">
                      <Button variant="outline" className="flex-1">
                        View Receipt
                      </Button>
                      <Button onClick={resetForm} className="flex-1 designer-button">
                        New Deposit
                      </Button>
                    </div>
                  </Card>
                </motion.div>
              )}
            </div>
          </main>
        </div>
      </div>
    </ProtectedRoute>
  );
}
