"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import { MainNavigation } from "@/components/navigation/MainNavigation";
import { ProtectedRoute } from "@/contexts/AuthContext";
import { Card } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { 
  Zap,
  Plus,
  Calendar,
  DollarSign,
  Clock,
  CheckCircle,
  AlertCircle,
  Wifi,
  Car,
  Home,
  Phone,
  CreditCard,
  Trash2,
  Edit,
  Search
} from "lucide-react";

const billCategories = [
  { id: "utilities", name: "Utilities", icon: Zap, color: "text-yellow-500", bgColor: "bg-yellow-500/10" },
  { id: "internet", name: "Internet & Cable", icon: Wifi, color: "text-blue-500", bgColor: "bg-blue-500/10" },
  { id: "insurance", name: "Insurance", icon: Car, color: "text-green-500", bgColor: "bg-green-500/10" },
  { id: "mortgage", name: "Mortgage & Rent", icon: Home, color: "text-purple-500", bgColor: "bg-purple-500/10" },
  { id: "phone", name: "Phone", icon: Phone, color: "text-orange-500", bgColor: "bg-orange-500/10" },
  { id: "credit", name: "Credit Cards", icon: CreditCard, color: "text-red-500", bgColor: "bg-red-500/10" }
];

const savedBills = [
  {
    id: "1",
    name: "Electric Company",
    category: "utilities",
    accountNumber: "*********",
    amount: 125.50,
    dueDate: "2024-01-25",
    status: "pending",
    autopay: true,
    lastPaid: "2023-12-25"
  },
  {
    id: "2",
    name: "Internet Provider",
    category: "internet", 
    accountNumber: "*********",
    amount: 89.99,
    dueDate: "2024-01-20",
    status: "overdue",
    autopay: false,
    lastPaid: "2023-11-20"
  },
  {
    id: "3",
    name: "Car Insurance",
    category: "insurance",
    accountNumber: "INS-456789",
    amount: 245.00,
    dueDate: "2024-02-01",
    status: "upcoming",
    autopay: true,
    lastPaid: "2024-01-01"
  },
  {
    id: "4",
    name: "Mortgage Payment",
    category: "mortgage",
    accountNumber: "MTG-789123",
    amount: 1850.00,
    dueDate: "2024-02-01",
    status: "upcoming",
    autopay: true,
    lastPaid: "2024-01-01"
  }
];

const recentPayments = [
  { id: "1", name: "Electric Company", amount: 118.75, date: "2023-12-25", status: "completed" },
  { id: "2", name: "Phone Bill", amount: 65.00, date: "2023-12-20", status: "completed" },
  { id: "3", name: "Water Bill", amount: 45.30, date: "2023-12-15", status: "completed" }
];

const myAccounts = [
  { id: "1", name: "Primary Checking", number: "****1234", balance: 15420.50 },
  { id: "2", name: "Savings Account", number: "****5678", balance: 45230.75 },
  { id: "3", name: "Business Account", number: "****9012", balance: 28750.00 }
];

export default function BillsPage() {
  const [activeTab, setActiveTab] = useState("pay");
  const [selectedCategory, setSelectedCategory] = useState("");
  const [searchQuery, setSearchQuery] = useState("");
  const [paymentData, setPaymentData] = useState({
    payee: "",
    accountNumber: "",
    amount: "",
    fromAccount: "",
    paymentDate: "",
    memo: ""
  });

  const handleQuickPay = (bill: any) => {
    console.log("Quick paying bill:", bill);
  };

  const handlePayBill = (e: React.FormEvent) => {
    e.preventDefault();
    console.log("Processing bill payment:", paymentData);
  };

  const filteredBills = savedBills.filter(bill => {
    const matchesSearch = bill.name.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = !selectedCategory || bill.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const getCategoryInfo = (categoryId: string) => {
    return billCategories.find(cat => cat.id === categoryId);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "overdue": return "bg-red-500";
      case "pending": return "bg-yellow-500";
      case "upcoming": return "bg-blue-500";
      case "completed": return "bg-green-500";
      default: return "bg-gray-500";
    }
  };

  return (
    <ProtectedRoute>
      <div className="flex min-h-screen bg-background">
        <MainNavigation currentPath="/bills" />
        
        <div className="flex-1 flex flex-col">
          {/* Header */}
          <motion.header
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="bg-card border-b border-border/50 p-4 lg:p-6"
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="p-2 rounded-lg bg-yellow-500/10">
                  <Zap className="h-6 w-6 text-yellow-500" />
                </div>
                <div>
                  <h1 className="text-2xl lg:text-3xl font-bold">Pay Bills</h1>
                  <p className="text-muted-foreground">Manage and pay your bills easily</p>
                </div>
              </div>
              <Button className="designer-button">
                <Plus className="h-4 w-4 mr-2" />
                Add New Payee
              </Button>
            </div>
          </motion.header>

          {/* Main Content */}
          <main className="flex-1 p-4 lg:p-6">
            {/* Summary Cards */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8"
            >
              <Card className="p-6">
                <div className="space-y-2">
                  <p className="text-sm text-muted-foreground">Total Due This Month</p>
                  <p className="text-2xl font-bold">$2,310.49</p>
                  <div className="flex items-center gap-1">
                    <Calendar className="h-4 w-4 text-blue-500" />
                    <span className="text-sm text-blue-500">4 bills due</span>
                  </div>
                </div>
              </Card>

              <Card className="p-6">
                <div className="space-y-2">
                  <p className="text-sm text-muted-foreground">Overdue Bills</p>
                  <p className="text-2xl font-bold text-red-500">$89.99</p>
                  <div className="flex items-center gap-1">
                    <AlertCircle className="h-4 w-4 text-red-500" />
                    <span className="text-sm text-red-500">1 overdue</span>
                  </div>
                </div>
              </Card>

              <Card className="p-6">
                <div className="space-y-2">
                  <p className="text-sm text-muted-foreground">AutoPay Enabled</p>
                  <p className="text-2xl font-bold text-green-500">3</p>
                  <div className="flex items-center gap-1">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span className="text-sm text-green-500">Active</span>
                  </div>
                </div>
              </Card>

              <Card className="p-6">
                <div className="space-y-2">
                  <p className="text-sm text-muted-foreground">This Month Paid</p>
                  <p className="text-2xl font-bold">$229.05</p>
                  <div className="flex items-center gap-1">
                    <DollarSign className="h-4 w-4 text-green-500" />
                    <span className="text-sm text-green-500">3 payments</span>
                  </div>
                </div>
              </Card>
            </motion.div>

            {/* Tabs */}
            <div className="flex gap-4 mb-6">
              <Button
                variant={activeTab === "pay" ? "default" : "outline"}
                onClick={() => setActiveTab("pay")}
              >
                <Zap className="h-4 w-4 mr-2" />
                Pay Bills
              </Button>
              <Button
                variant={activeTab === "manage" ? "default" : "outline"}
                onClick={() => setActiveTab("manage")}
              >
                <Calendar className="h-4 w-4 mr-2" />
                Manage Bills
              </Button>
              <Button
                variant={activeTab === "history" ? "default" : "outline"}
                onClick={() => setActiveTab("history")}
              >
                <Clock className="h-4 w-4 mr-2" />
                Payment History
              </Button>
            </div>

            {/* Tab Content */}
            {activeTab === "pay" && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
                className="grid grid-cols-1 lg:grid-cols-2 gap-6"
              >
                {/* Quick Pay */}
                <Card className="p-6">
                  <h2 className="text-xl font-semibold mb-6">Quick Pay</h2>
                  
                  <div className="space-y-4">
                    {savedBills.filter(bill => bill.status === "pending" || bill.status === "overdue").map((bill) => {
                      const categoryInfo = getCategoryInfo(bill.category);
                      const IconComponent = categoryInfo?.icon || Zap;
                      
                      return (
                        <div key={bill.id} className="p-4 border rounded-lg">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                              <div className={`p-2 rounded-lg ${categoryInfo?.bgColor}`}>
                                <IconComponent className={`h-5 w-5 ${categoryInfo?.color}`} />
                              </div>
                              <div>
                                <h3 className="font-medium">{bill.name}</h3>
                                <p className="text-sm text-muted-foreground">Due: {bill.dueDate}</p>
                              </div>
                            </div>
                            <div className="text-right">
                              <p className="font-bold">${bill.amount}</p>
                              <Badge className={getStatusColor(bill.status)}>
                                {bill.status}
                              </Badge>
                            </div>
                          </div>
                          <Button 
                            className="w-full mt-3 designer-button"
                            onClick={() => handleQuickPay(bill)}
                          >
                            Pay Now
                          </Button>
                        </div>
                      );
                    })}
                  </div>
                </Card>

                {/* New Payment Form */}
                <Card className="p-6">
                  <h2 className="text-xl font-semibold mb-6">Pay New Bill</h2>
                  
                  <form onSubmit={handlePayBill} className="space-y-4">
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Payee Name</label>
                      <Input
                        placeholder="Enter payee name"
                        value={paymentData.payee}
                        onChange={(e) => setPaymentData({...paymentData, payee: e.target.value})}
                        required
                      />
                    </div>

                    <div className="space-y-2">
                      <label className="text-sm font-medium">Account Number</label>
                      <Input
                        placeholder="Enter account number"
                        value={paymentData.accountNumber}
                        onChange={(e) => setPaymentData({...paymentData, accountNumber: e.target.value})}
                        required
                      />
                    </div>

                    <div className="space-y-2">
                      <label className="text-sm font-medium">Amount ($)</label>
                      <Input
                        type="number"
                        step="0.01"
                        placeholder="0.00"
                        value={paymentData.amount}
                        onChange={(e) => setPaymentData({...paymentData, amount: e.target.value})}
                        required
                      />
                    </div>

                    <div className="space-y-2">
                      <label className="text-sm font-medium">Pay From Account</label>
                      <select 
                        className="w-full p-3 rounded-lg border border-border bg-background"
                        value={paymentData.fromAccount}
                        onChange={(e) => setPaymentData({...paymentData, fromAccount: e.target.value})}
                        required
                      >
                        <option value="">Select account</option>
                        {myAccounts.map((account) => (
                          <option key={account.id} value={account.id}>
                            {account.name} ({account.number}) - ${account.balance.toLocaleString()}
                          </option>
                        ))}
                      </select>
                    </div>

                    <div className="space-y-2">
                      <label className="text-sm font-medium">Payment Date</label>
                      <Input
                        type="date"
                        value={paymentData.paymentDate}
                        onChange={(e) => setPaymentData({...paymentData, paymentDate: e.target.value})}
                        required
                      />
                    </div>

                    <div className="space-y-2">
                      <label className="text-sm font-medium">Memo (Optional)</label>
                      <Input
                        placeholder="Payment memo"
                        value={paymentData.memo}
                        onChange={(e) => setPaymentData({...paymentData, memo: e.target.value})}
                      />
                    </div>

                    <Button type="submit" className="w-full designer-button">
                      Schedule Payment
                    </Button>
                  </form>
                </Card>
              </motion.div>
            )}

            {activeTab === "manage" && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
                className="space-y-6"
              >
                {/* Filters */}
                <Card className="p-6">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Search Bills</label>
                      <div className="relative">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                        <Input
                          placeholder="Search by name..."
                          value={searchQuery}
                          onChange={(e) => setSearchQuery(e.target.value)}
                          className="pl-10"
                        />
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Category</label>
                      <select 
                        className="w-full p-3 rounded-lg border border-border bg-background"
                        value={selectedCategory}
                        onChange={(e) => setSelectedCategory(e.target.value)}
                      >
                        <option value="">All Categories</option>
                        {billCategories.map((category) => (
                          <option key={category.id} value={category.id}>
                            {category.name}
                          </option>
                        ))}
                      </select>
                    </div>

                    <div className="space-y-2">
                      <label className="text-sm font-medium">Actions</label>
                      <Button variant="outline" className="w-full">
                        <Plus className="h-4 w-4 mr-2" />
                        Add New Payee
                      </Button>
                    </div>
                  </div>
                </Card>

                {/* Bills List */}
                <div className="space-y-4">
                  {filteredBills.map((bill) => {
                    const categoryInfo = getCategoryInfo(bill.category);
                    const IconComponent = categoryInfo?.icon || Zap;
                    
                    return (
                      <motion.div
                        key={bill.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.5 }}
                      >
                        <Card className="p-6">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-4">
                              <div className={`p-3 rounded-lg ${categoryInfo?.bgColor}`}>
                                <IconComponent className={`h-6 w-6 ${categoryInfo?.color}`} />
                              </div>
                              
                              <div>
                                <h3 className="font-semibold text-lg">{bill.name}</h3>
                                <p className="text-sm text-muted-foreground">
                                  Account: {bill.accountNumber}
                                </p>
                                <div className="flex items-center gap-4 mt-2">
                                  <div className="flex items-center gap-1">
                                    <Calendar className="h-4 w-4 text-muted-foreground" />
                                    <span className="text-sm">Due: {bill.dueDate}</span>
                                  </div>
                                  <div className="flex items-center gap-1">
                                    <DollarSign className="h-4 w-4 text-muted-foreground" />
                                    <span className="text-sm">${bill.amount}</span>
                                  </div>
                                </div>
                              </div>
                            </div>

                            <div className="flex items-center gap-3">
                              <Badge className={getStatusColor(bill.status)}>
                                {bill.status}
                              </Badge>
                              {bill.autopay && (
                                <Badge variant="outline">AutoPay</Badge>
                              )}
                              <div className="flex gap-2">
                                <Button variant="outline" size="icon">
                                  <Edit className="h-4 w-4" />
                                </Button>
                                <Button variant="outline" size="icon">
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </div>
                            </div>
                          </div>
                        </Card>
                      </motion.div>
                    );
                  })}
                </div>
              </motion.div>
            )}

            {activeTab === "history" && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
              >
                <Card className="p-6">
                  <h2 className="text-xl font-semibold mb-6">Recent Payments</h2>
                  
                  <div className="space-y-4">
                    {recentPayments.map((payment) => (
                      <div key={payment.id} className="flex items-center justify-between p-4 border rounded-lg">
                        <div className="flex items-center gap-3">
                          <div className="w-10 h-10 rounded-full bg-green-500/10 flex items-center justify-center">
                            <CheckCircle className="h-5 w-5 text-green-500" />
                          </div>
                          <div>
                            <h3 className="font-medium">{payment.name}</h3>
                            <p className="text-sm text-muted-foreground">{payment.date}</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="font-bold">${payment.amount}</p>
                          <Badge className="bg-green-500">Completed</Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                </Card>
              </motion.div>
            )}
          </main>
        </div>
      </div>
    </ProtectedRoute>
  );
}
