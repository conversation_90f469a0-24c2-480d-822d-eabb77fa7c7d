"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import { MainNavigation } from "@/components/navigation/MainNavigation";
import { ProtectedRoute } from "@/contexts/AuthContext";
import { Card } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { 
  Phone,
  MessageCircle,
  Mail,
  Clock,
  Search,
  HelpCircle,
  FileText,
  CreditCard,
  Shield,
  Settings,
  AlertCircle,
  CheckCircle,
  Send,
  Paperclip
} from "lucide-react";

const supportOptions = [
  {
    id: "chat",
    title: "Live Chat",
    description: "Chat with our support team in real-time",
    icon: MessageCircle,
    color: "text-blue-500",
    bgColor: "bg-blue-500/10",
    available: true,
    responseTime: "< 2 minutes"
  },
  {
    id: "phone",
    title: "Phone Support",
    description: "Speak directly with a support representative",
    icon: Phone,
    color: "text-green-500",
    bgColor: "bg-green-500/10",
    available: true,
    responseTime: "< 5 minutes",
    number: "1-800-ABC-BANK"
  },
  {
    id: "email",
    title: "Email Support",
    description: "Send us a detailed message",
    icon: Mail,
    color: "text-purple-500",
    bgColor: "bg-purple-500/10",
    available: true,
    responseTime: "< 24 hours"
  },
  {
    id: "appointment",
    title: "Schedule Appointment",
    description: "Book a meeting with a banking specialist",
    icon: Clock,
    color: "text-orange-500",
    bgColor: "bg-orange-500/10",
    available: true,
    responseTime: "Next available slot"
  }
];

const faqCategories = [
  {
    id: "account",
    title: "Account Management",
    icon: Settings,
    questions: [
      "How do I change my account password?",
      "How to update my contact information?",
      "What are the account fees?",
      "How to close my account?"
    ]
  },
  {
    id: "cards",
    title: "Cards & Payments",
    icon: CreditCard,
    questions: [
      "How to report a lost or stolen card?",
      "How to increase my credit limit?",
      "Why was my transaction declined?",
      "How to set up automatic payments?"
    ]
  },
  {
    id: "security",
    title: "Security & Fraud",
    icon: Shield,
    questions: [
      "How to report suspicious activity?",
      "What is two-factor authentication?",
      "How to secure my online banking?",
      "What to do if I suspect fraud?"
    ]
  },
  {
    id: "transfers",
    title: "Transfers & Transactions",
    icon: FileText,
    questions: [
      "How long do transfers take?",
      "What are the transfer limits?",
      "How to cancel a pending transfer?",
      "Why is my transfer delayed?"
    ]
  }
];

const tickets = [
  {
    id: "TKT-001",
    subject: "Unable to access mobile app",
    status: "Open",
    priority: "High",
    created: "2024-01-15",
    lastUpdate: "2024-01-15"
  },
  {
    id: "TKT-002", 
    subject: "Question about interest rates",
    status: "Resolved",
    priority: "Low",
    created: "2024-01-10",
    lastUpdate: "2024-01-12"
  }
];

export default function SupportPage() {
  const [activeTab, setActiveTab] = useState("help");
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("");
  const [chatMessage, setChatMessage] = useState("");
  const [newTicket, setNewTicket] = useState({
    subject: "",
    category: "",
    priority: "Medium",
    description: ""
  });

  const handleSendMessage = () => {
    if (chatMessage.trim()) {
      console.log("Sending message:", chatMessage);
      setChatMessage("");
    }
  };

  const handleCreateTicket = (e: React.FormEvent) => {
    e.preventDefault();
    console.log("Creating ticket:", newTicket);
    setNewTicket({ subject: "", category: "", priority: "Medium", description: "" });
  };

  return (
    <ProtectedRoute>
      <div className="flex min-h-screen bg-background">
        <MainNavigation currentPath="/support" />
        
        <div className="flex-1 flex flex-col">
          {/* Header */}
          <motion.header
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="bg-card border-b border-border/50 p-4 lg:p-6"
          >
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-lg bg-red-500/10">
                <Phone className="h-6 w-6 text-red-500" />
              </div>
              <div>
                <h1 className="text-2xl lg:text-3xl font-bold">Customer Support</h1>
                <p className="text-muted-foreground">We're here to help you 24/7</p>
              </div>
            </div>
          </motion.header>

          {/* Main Content */}
          <main className="flex-1 p-4 lg:p-6">
            {/* Support Options */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="mb-8"
            >
              <h2 className="text-xl font-semibold mb-4">How can we help you today?</h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {supportOptions.map((option) => {
                  const IconComponent = option.icon;
                  
                  return (
                    <Card key={option.id} className="p-6 cursor-pointer hover:shadow-md transition-all designer-card">
                      <div className="space-y-4">
                        <div className={`p-3 rounded-xl ${option.bgColor} w-fit`}>
                          <IconComponent className={`h-6 w-6 ${option.color}`} />
                        </div>
                        
                        <div>
                          <h3 className="font-semibold mb-2">{option.title}</h3>
                          <p className="text-sm text-muted-foreground mb-3">
                            {option.description}
                          </p>
                          <div className="flex items-center gap-1 text-sm text-muted-foreground">
                            <Clock className="h-4 w-4" />
                            <span>{option.responseTime}</span>
                          </div>
                          {option.number && (
                            <p className="text-sm font-medium mt-2">{option.number}</p>
                          )}
                        </div>
                        
                        <Button className="w-full designer-button">
                          {option.title === "Live Chat" ? "Start Chat" : 
                           option.title === "Phone Support" ? "Call Now" :
                           option.title === "Email Support" ? "Send Email" : "Book Now"}
                        </Button>
                      </div>
                    </Card>
                  );
                })}
              </div>
            </motion.div>

            {/* Tabs */}
            <div className="flex gap-4 mb-6">
              <Button
                variant={activeTab === "help" ? "default" : "outline"}
                onClick={() => setActiveTab("help")}
              >
                <HelpCircle className="h-4 w-4 mr-2" />
                Help Center
              </Button>
              <Button
                variant={activeTab === "chat" ? "default" : "outline"}
                onClick={() => setActiveTab("chat")}
              >
                <MessageCircle className="h-4 w-4 mr-2" />
                Live Chat
              </Button>
              <Button
                variant={activeTab === "tickets" ? "default" : "outline"}
                onClick={() => setActiveTab("tickets")}
              >
                <FileText className="h-4 w-4 mr-2" />
                My Tickets
              </Button>
            </div>

            {/* Tab Content */}
            {activeTab === "help" && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
                className="space-y-6"
              >
                {/* Search */}
                <Card className="p-6">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search for help articles..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </Card>

                {/* FAQ Categories */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {faqCategories.map((category) => {
                    const IconComponent = category.icon;
                    
                    return (
                      <motion.div
                        key={category.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.5 }}
                      >
                        <Card className="p-6">
                          <div className="flex items-center gap-3 mb-4">
                            <div className="p-2 rounded-lg bg-muted/50">
                              <IconComponent className="h-5 w-5" />
                            </div>
                            <h3 className="font-semibold">{category.title}</h3>
                          </div>
                          
                          <div className="space-y-2">
                            {category.questions.map((question, index) => (
                              <button
                                key={index}
                                className="w-full text-left p-2 rounded-lg hover:bg-muted/50 transition-colors text-sm"
                              >
                                {question}
                              </button>
                            ))}
                          </div>
                        </Card>
                      </motion.div>
                    );
                  })}
                </div>
              </motion.div>
            )}

            {activeTab === "chat" && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
              >
                <Card className="p-6 max-w-4xl mx-auto">
                  <div className="space-y-4">
                    <div className="flex items-center gap-3 pb-4 border-b">
                      <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center">
                        <MessageCircle className="h-5 w-5 text-primary" />
                      </div>
                      <div>
                        <h3 className="font-semibold">Live Chat Support</h3>
                        <div className="flex items-center gap-1">
                          <div className="w-2 h-2 rounded-full bg-green-500" />
                          <span className="text-sm text-muted-foreground">Agent available</span>
                        </div>
                      </div>
                    </div>

                    {/* Chat Messages */}
                    <div className="h-96 overflow-y-auto space-y-4 p-4 bg-muted/20 rounded-lg">
                      <div className="flex gap-3">
                        <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
                          <span className="text-xs font-medium">CS</span>
                        </div>
                        <div className="bg-card p-3 rounded-lg max-w-xs">
                          <p className="text-sm">Hello! I'm Sarah from ABC Banking support. How can I help you today?</p>
                          <p className="text-xs text-muted-foreground mt-1">2:30 PM</p>
                        </div>
                      </div>
                    </div>

                    {/* Chat Input */}
                    <div className="flex gap-3">
                      <Input
                        placeholder="Type your message..."
                        value={chatMessage}
                        onChange={(e) => setChatMessage(e.target.value)}
                        onKeyPress={(e) => e.key === "Enter" && handleSendMessage()}
                        className="flex-1"
                      />
                      <Button size="icon" variant="outline">
                        <Paperclip className="h-4 w-4" />
                      </Button>
                      <Button onClick={handleSendMessage} className="designer-button">
                        <Send className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </Card>
              </motion.div>
            )}

            {activeTab === "tickets" && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
                className="space-y-6"
              >
                {/* Create New Ticket */}
                <Card className="p-6">
                  <h3 className="font-semibold mb-4">Create New Support Ticket</h3>
                  
                  <form onSubmit={handleCreateTicket} className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <label className="text-sm font-medium">Subject</label>
                        <Input
                          placeholder="Brief description of your issue"
                          value={newTicket.subject}
                          onChange={(e) => setNewTicket({...newTicket, subject: e.target.value})}
                          required
                        />
                      </div>
                      <div className="space-y-2">
                        <label className="text-sm font-medium">Category</label>
                        <select 
                          className="w-full p-3 rounded-lg border border-border bg-background"
                          value={newTicket.category}
                          onChange={(e) => setNewTicket({...newTicket, category: e.target.value})}
                          required
                        >
                          <option value="">Select category</option>
                          <option value="account">Account Management</option>
                          <option value="cards">Cards & Payments</option>
                          <option value="security">Security & Fraud</option>
                          <option value="transfers">Transfers & Transactions</option>
                          <option value="technical">Technical Issues</option>
                          <option value="other">Other</option>
                        </select>
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Priority</label>
                      <select 
                        className="w-full p-3 rounded-lg border border-border bg-background"
                        value={newTicket.priority}
                        onChange={(e) => setNewTicket({...newTicket, priority: e.target.value})}
                      >
                        <option value="Low">Low</option>
                        <option value="Medium">Medium</option>
                        <option value="High">High</option>
                        <option value="Urgent">Urgent</option>
                      </select>
                    </div>
                    
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Description</label>
                      <textarea
                        className="w-full p-3 rounded-lg border border-border bg-background min-h-[100px]"
                        placeholder="Please provide detailed information about your issue..."
                        value={newTicket.description}
                        onChange={(e) => setNewTicket({...newTicket, description: e.target.value})}
                        required
                      />
                    </div>
                    
                    <Button type="submit" className="designer-button">
                      Create Ticket
                    </Button>
                  </form>
                </Card>

                {/* Existing Tickets */}
                <Card className="p-6">
                  <h3 className="font-semibold mb-4">Your Support Tickets</h3>
                  
                  <div className="space-y-4">
                    {tickets.map((ticket) => (
                      <div key={ticket.id} className="p-4 border rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center gap-3">
                            <span className="font-mono text-sm">{ticket.id}</span>
                            <Badge 
                              variant={ticket.status === "Open" ? "destructive" : "default"}
                              className={ticket.status === "Resolved" ? "bg-green-500" : ""}
                            >
                              {ticket.status}
                            </Badge>
                            <Badge variant="outline">{ticket.priority}</Badge>
                          </div>
                          <span className="text-sm text-muted-foreground">
                            Created: {ticket.created}
                          </span>
                        </div>
                        <h4 className="font-medium mb-1">{ticket.subject}</h4>
                        <p className="text-sm text-muted-foreground">
                          Last updated: {ticket.lastUpdate}
                        </p>
                      </div>
                    ))}
                  </div>
                </Card>
              </motion.div>
            )}
          </main>
        </div>
      </div>
    </ProtectedRoute>
  );
}
