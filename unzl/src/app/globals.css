@tailwind base;
@tailwind components;
@tailwind utilities;

@font-face {
  font-family: 'DM Sans';
  src: url('https://ext.same-assets.com/209745279/3307048321.woff2') format('woff2');
  font-weight: 400 700;
  font-style: normal;
  font-display: swap;
}

@layer base {
  :root {
    --background: 220 15% 5%;
    --foreground: 210 10% 90%;

    --card: 220 15% 7%;
    --card-foreground: 210 10% 95%;

    --popover: 220 15% 8%;
    --popover-foreground: 210 10% 95%;

    --primary: 210 100% 50%;
    --primary-foreground: 0 0% 100%;

    --secondary: 210 60% 25%;
    --secondary-foreground: 0 10% 95%;

    --muted: 210 30% 12%;
    --muted-foreground: 210 10% 75%;

    --accent: 210 50% 20%;
    --accent-foreground: 0 10% 95%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 10% 95%;

    --success: 142 76% 36%;
    --success-foreground: 0 0% 100%;

    --warning: 38 92% 50%;
    --warning-foreground: 0 0% 100%;

    --border: 210 25% 18%;
    --input: 210 25% 22%;
    --ring: 210 100% 50%;

    --radius: 0.75rem;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  html {
    scroll-behavior: smooth;
    cursor: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIHZpZXdCb3g9IjAgMCAxMiAxMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48Y2lyY2xlIGN4PSI2IiBjeT0iNiIgcj0iNiIgZmlsbD0icmdiYSgwLCAxMjAsIDI1NSwgMC41KSIvPjwvc3ZnPg==") 6 6, auto;
  }

  a, button, [role="button"], input, select, textarea, [tabindex]:not([tabindex="-1"]) {
    cursor: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgc3Ryb2tlPSJyZ2JhKDAsIDEyMCwgMjU1LCAwLjgpIiBzdHJva2Utd2lkdGg9IjIiIGZpbGw9InJnYmEoMCwgMTIwLCAyNTUsIDAuMikiLz48L3N2Zz4=") 12 12, pointer;
  }

  body {
    @apply bg-background text-foreground;
    font-family: 'DM Sans', sans-serif;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-bold tracking-tight;
  }

  h1 {
    @apply text-4xl md:text-5xl lg:text-6xl;
    line-height: 1.1;
  }

  h2 {
    @apply text-3xl md:text-4xl lg:text-5xl;
    line-height: 1.2;
  }

  h3 {
    @apply text-2xl md:text-3xl;
    line-height: 1.3;
  }

  h4 {
    @apply text-xl md:text-2xl;
    line-height: 1.4;
  }
}

@layer utilities {
  .gradient-bg {
    background: radial-gradient(circle at center, rgba(0, 120, 255, 0.15) 0%, rgba(0, 100, 200, 0.05) 50%, transparent 80%);
  }

  .section-padding {
    @apply py-16 md:py-24 lg:py-32;
  }

  .container-padding {
    @apply px-4 sm:px-6 lg:px-8;
  }
}

.badge {
  @apply inline-flex items-center justify-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  background: linear-gradient(90deg, rgba(0, 120, 255, 0.2) 0%, rgba(0, 100, 200, 0.2) 100%);
  color: hsl(var(--primary));
  border: 1px solid rgba(0, 120, 255, 0.3);
}

.hero-glow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 100vh;
  background: radial-gradient(circle at 50% 30%, rgba(0, 120, 255, 0.2) 0%, rgba(0, 100, 200, 0.1) 25%, transparent 50%);
  z-index: -1;
  pointer-events: none;
}

/* Buttons */
.btn-primary {
  @apply bg-primary hover:bg-primary/90 text-primary-foreground font-medium rounded-md transition-colors;
  background: linear-gradient(90deg, hsl(220, 100%, 60%) 0%, hsl(220, 100%, 50%) 100%);
  box-shadow: 0 2px 10px rgba(0, 120, 255, 0.3);
}

.btn-outline {
  @apply bg-transparent border border-border/70 hover:border-primary/50 text-foreground hover:text-primary-foreground hover:bg-primary/10 font-medium rounded-md transition-all;
}

/* Cards */
.feature-card {
  @apply bg-card rounded-xl p-8 border border-border/50 relative overflow-hidden;
  background: linear-gradient(145deg, hsl(220, 15%, 8%) 0%, hsl(220, 15%, 5%) 100%);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

.service-card {
  @apply bg-card rounded-xl overflow-hidden border border-border/50 h-full flex flex-col relative;
  background: linear-gradient(145deg, hsl(220, 15%, 8%) 0%, hsl(220, 15%, 5%) 100%);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

/* Animations */
@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes fadeIn {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 5px 2px rgba(0, 120, 255, 0.3);
  }
  50% {
    box-shadow: 0 0 15px 5px rgba(0, 120, 255, 0.5);
  }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-pulse {
  animation: pulse 3s ease-in-out infinite;
}

.animate-fadeIn {
  animation: fadeIn 0.5s ease-out forwards;
}

.animate-glow {
  animation: glow 3s ease-in-out infinite;
}

.delay-100 {
  animation-delay: 0.1s;
}

.delay-200 {
  animation-delay: 0.2s;
}

.delay-300 {
  animation-delay: 0.3s;
}

.delay-400 {
  animation-delay: 0.4s;
}

.delay-500 {
  animation-delay: 0.5s;
}

/* Designer-quality effects */
.glass-effect {
  @apply backdrop-blur-md bg-white/5 border border-white/10;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.noise-bg {
  position: relative;
}

.noise-bg::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0.03;
  z-index: 1;
  pointer-events: none;
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 200 200' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.65' numOctaves='3' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E");
}

.text-gradient {
  @apply text-transparent bg-clip-text;
  background-image: linear-gradient(135deg, hsl(220, 100%, 60%) 0%, hsl(0, 0%, 100%) 100%);
}

.text-outline {
  -webkit-text-stroke: 1px rgba(255, 255, 255, 0.1);
}

.morph-shadow {
  box-shadow:
    10px 10px 30px rgba(0, 0, 0, 0.15),
    -10px -10px 30px rgba(255, 255, 255, 0.05);
}

.designer-card {
  @apply relative overflow-hidden rounded-2xl;
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.05) 0%, rgba(0, 0, 0, 0.1) 100%);
  box-shadow:
    0 4px 24px -1px rgba(0, 0, 0, 0.2),
    0 1px 2px 0 rgba(0, 0, 0, 0.1),
    inset 0 1px 1px 0 rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.designer-button {
  @apply relative overflow-hidden rounded-xl transition-all duration-300;
  background: linear-gradient(135deg, hsl(220, 100%, 60%) 0%, hsl(220, 100%, 50%) 100%);
  box-shadow:
    0 4px 12px rgba(0, 120, 255, 0.3),
    0 1px 2px rgba(0, 0, 0, 0.2),
    inset 0 1px 1px rgba(255, 255, 255, 0.1);
}

.designer-button:hover {
  transform: translateY(-2px);
  box-shadow:
    0 6px 16px rgba(0, 120, 255, 0.4),
    0 2px 4px rgba(0, 0, 0, 0.3),
    inset 0 1px 1px rgba(255, 255, 255, 0.2);
}

.designer-button:active {
  transform: translateY(1px);
  box-shadow:
    0 2px 8px rgba(0, 120, 255, 0.2),
    0 1px 1px rgba(0, 0, 0, 0.1),
    inset 0 1px 1px rgba(255, 255, 255, 0.05);
}

/* Parallax effect */
.parallax {
  transform-style: preserve-3d;
  perspective: 1000px;
}

.parallax-layer {
  transform: translateZ(0);
  transition: transform 0.2s ease-out;
}

.parallax-layer-back {
  transform: translateZ(-100px) scale(1.1);
}

.parallax-layer-deep {
  transform: translateZ(-200px) scale(1.2);
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--muted));
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--primary));
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--primary) / 0.8);
}

/* ABC Banking Brand Colors - Professional Banking Theme */
.banking-gradient {
  background: linear-gradient(90deg, #0066CC 0%, #FFFFFF 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.banking-glow {
  box-shadow: 0 0 20px rgba(0, 102, 204, 0.3);
}

.banking-border {
  border-color: #0066CC;
}

.banking-bg {
  background: linear-gradient(135deg, rgba(0, 102, 204, 0.1) 0%, rgba(0, 80, 160, 0.05) 100%);
}

/* Banking-specific styles */
.banking-success {
  color: #00C851;
}

.banking-warning {
  color: #FFB300;
}

.banking-error {
  color: #FF3547;
}

/* Banking-specific card styles */
.account-card {
  @apply bg-card rounded-xl p-6 border border-border/50 relative overflow-hidden;
  background: linear-gradient(145deg, hsl(220, 15%, 8%) 0%, hsl(220, 15%, 5%) 100%);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

.transaction-card {
  @apply bg-card rounded-lg p-4 border border-border/30 hover:border-primary/50 transition-colors;
}

.balance-card {
  @apply bg-gradient-to-br from-primary/10 to-primary/5 rounded-xl p-6 border border-primary/20;
}

/* Banking status indicators */
.status-active {
  @apply bg-green-500/10 text-green-500 border-green-500/20;
}

.status-pending {
  @apply bg-yellow-500/10 text-yellow-500 border-yellow-500/20;
}

.status-inactive {
  @apply bg-red-500/10 text-red-500 border-red-500/20;
}

/* Banking animations */
@keyframes money-flow {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translateX(100%);
    opacity: 0;
  }
}

.animate-money-flow {
  animation: money-flow 2s ease-in-out infinite;
}

/* Security indicators */
.security-badge {
  @apply inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium;
  background: linear-gradient(90deg, rgba(0, 200, 81, 0.2) 0%, rgba(0, 120, 255, 0.2) 100%);
  color: hsl(var(--primary));
  border: 1px solid rgba(0, 200, 81, 0.3);
}

/* Banking-specific components */
.account-card {
  @apply bg-card rounded-xl p-6 border border-border/50 relative overflow-hidden;
  background: linear-gradient(145deg, hsl(220, 15%, 8%) 0%, hsl(220, 15%, 5%) 100%);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

.transaction-row {
  @apply border-b border-border/30 py-4 hover:bg-muted/20 transition-colors;
}

.balance-display {
  @apply text-2xl md:text-3xl font-bold tracking-tight;
}

.status-badge {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.status-success {
  @apply status-badge bg-success/20 text-success border border-success/30;
}

.status-pending {
  @apply status-badge bg-warning/20 text-warning border border-warning/30;
}

.status-failed {
  @apply status-badge bg-destructive/20 text-destructive border border-destructive/30;
}

.amount-positive {
  @apply text-success font-semibold;
}

.amount-negative {
  @apply text-destructive font-semibold;
}

.amount-neutral {
  @apply text-foreground font-semibold;
}
