"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import { MainNavigation } from "@/components/navigation/MainNavigation";
import { ProtectedRoute } from "@/contexts/AuthContext";
import { Card } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { 
  FileText,
  Download,
  Search,
  Filter,
  Calendar,
  Eye,
  Mail,
  CreditCard,
  Clock
} from "lucide-react";

const statements = [
  {
    id: "1",
    accountType: "Checking Account",
    accountNumber: "****1234",
    period: "December 2023",
    startDate: "2023-12-01",
    endDate: "2023-12-31",
    status: "Available",
    fileSize: "2.4 MB",
    transactions: 45,
    downloadUrl: "/statements/checking-dec-2023.pdf"
  },
  {
    id: "2",
    accountType: "Savings Account", 
    accountNumber: "****5678",
    period: "December 2023",
    startDate: "2023-12-01",
    endDate: "2023-12-31",
    status: "Available",
    fileSize: "1.8 MB",
    transactions: 12,
    downloadUrl: "/statements/savings-dec-2023.pdf"
  },
  {
    id: "3",
    accountType: "Checking Account",
    accountNumber: "****1234", 
    period: "November 2023",
    startDate: "2023-11-01",
    endDate: "2023-11-30",
    status: "Available",
    fileSize: "2.1 MB",
    transactions: 38,
    downloadUrl: "/statements/checking-nov-2023.pdf"
  },
  {
    id: "4",
    accountType: "Credit Card",
    accountNumber: "****9012",
    period: "December 2023",
    startDate: "2023-12-01", 
    endDate: "2023-12-31",
    status: "Processing",
    fileSize: "1.5 MB",
    transactions: 28,
    downloadUrl: null
  },
  {
    id: "5",
    accountType: "Business Account",
    accountNumber: "****3456",
    period: "December 2023",
    startDate: "2023-12-01",
    endDate: "2023-12-31",
    status: "Available",
    fileSize: "3.2 MB", 
    transactions: 67,
    downloadUrl: "/statements/business-dec-2023.pdf"
  }
];

export default function StatementsPage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedAccount, setSelectedAccount] = useState("all");
  const [selectedPeriod, setSelectedPeriod] = useState("all");

  const filteredStatements = statements.filter(statement => {
    const matchesSearch = statement.accountType.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         statement.period.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesAccount = selectedAccount === "all" || statement.accountType === selectedAccount;
    const matchesPeriod = selectedPeriod === "all" || statement.period.includes(selectedPeriod);
    
    return matchesSearch && matchesAccount && matchesPeriod;
  });

  const handleDownload = (statement: any) => {
    if (statement.downloadUrl) {
      // Simulate download
      console.log(`Downloading statement: ${statement.downloadUrl}`);
      // In real implementation, this would trigger actual file download
    }
  };

  const handleEmailStatement = (statement: any) => {
    console.log(`Emailing statement: ${statement.id}`);
    // Handle email functionality
  };

  return (
    <ProtectedRoute>
      <div className="flex min-h-screen bg-background">
        <MainNavigation currentPath="/statements" />
        
        <div className="flex-1 flex flex-col">
          {/* Header */}
          <motion.header
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="bg-card border-b border-border/50 p-4 lg:p-6"
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="p-2 rounded-lg bg-orange-500/10">
                  <FileText className="h-6 w-6 text-orange-500" />
                </div>
                <div>
                  <h1 className="text-2xl lg:text-3xl font-bold">Account Statements</h1>
                  <p className="text-muted-foreground">Download and view your account statements</p>
                </div>
              </div>
              <Button className="designer-button">
                <Mail className="h-4 w-4 mr-2" />
                Email Preferences
              </Button>
            </div>
          </motion.header>

          {/* Main Content */}
          <main className="flex-1 p-4 lg:p-6 space-y-6">
            {/* Filters */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Card className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Search</label>
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                      <Input
                        placeholder="Search statements..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10"
                      />
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Account Type</label>
                    <select 
                      className="w-full p-3 rounded-lg border border-border bg-background"
                      value={selectedAccount}
                      onChange={(e) => setSelectedAccount(e.target.value)}
                    >
                      <option value="all">All Accounts</option>
                      <option value="Checking Account">Checking Account</option>
                      <option value="Savings Account">Savings Account</option>
                      <option value="Credit Card">Credit Card</option>
                      <option value="Business Account">Business Account</option>
                    </select>
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">Period</label>
                    <select 
                      className="w-full p-3 rounded-lg border border-border bg-background"
                      value={selectedPeriod}
                      onChange={(e) => setSelectedPeriod(e.target.value)}
                    >
                      <option value="all">All Periods</option>
                      <option value="2023">2023</option>
                      <option value="2022">2022</option>
                      <option value="2021">2021</option>
                    </select>
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">Actions</label>
                    <Button variant="outline" className="w-full">
                      <Filter className="h-4 w-4 mr-2" />
                      Advanced Filters
                    </Button>
                  </div>
                </div>
              </Card>
            </motion.div>

            {/* Statements List */}
            <div className="space-y-4">
              {filteredStatements.map((statement, index) => (
                <motion.div
                  key={statement.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                >
                  <Card className="p-6 designer-card">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4">
                        <div className="p-3 rounded-lg bg-muted/50">
                          {statement.accountType.includes("Credit") ? (
                            <CreditCard className="h-6 w-6 text-purple-500" />
                          ) : (
                            <FileText className="h-6 w-6 text-blue-500" />
                          )}
                        </div>
                        
                        <div className="space-y-1">
                          <div className="flex items-center gap-2">
                            <h3 className="font-semibold text-lg">{statement.accountType}</h3>
                            <Badge variant="outline">{statement.accountNumber}</Badge>
                          </div>
                          <p className="text-sm text-muted-foreground">
                            Statement Period: {statement.period}
                          </p>
                          <div className="flex items-center gap-4 text-sm text-muted-foreground">
                            <span>{statement.transactions} transactions</span>
                            <span>{statement.fileSize}</span>
                            <div className="flex items-center gap-1">
                              <Calendar className="h-4 w-4" />
                              <span>{new Date(statement.startDate).toLocaleDateString()} - {new Date(statement.endDate).toLocaleDateString()}</span>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center gap-3">
                        <Badge 
                          variant={statement.status === "Available" ? "default" : "secondary"}
                          className={statement.status === "Available" ? "bg-green-500" : ""}
                        >
                          {statement.status === "Processing" && <Clock className="h-3 w-3 mr-1" />}
                          {statement.status}
                        </Badge>
                        
                        <div className="flex gap-2">
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => handleEmailStatement(statement)}
                          >
                            <Mail className="h-4 w-4 mr-2" />
                            Email
                          </Button>
                          
                          <Button 
                            variant="outline" 
                            size="sm"
                            disabled={!statement.downloadUrl}
                          >
                            <Eye className="h-4 w-4 mr-2" />
                            View
                          </Button>
                          
                          <Button 
                            size="sm"
                            onClick={() => handleDownload(statement)}
                            disabled={!statement.downloadUrl}
                            className="designer-button"
                          >
                            <Download className="h-4 w-4 mr-2" />
                            Download
                          </Button>
                        </div>
                      </div>
                    </div>
                  </Card>
                </motion.div>
              ))}
            </div>

            {filteredStatements.length === 0 && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.5 }}
                className="text-center py-12"
              >
                <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">No statements found</h3>
                <p className="text-muted-foreground">
                  Try adjusting your search criteria or filters.
                </p>
              </motion.div>
            )}
          </main>
        </div>
      </div>
    </ProtectedRoute>
  );
}
