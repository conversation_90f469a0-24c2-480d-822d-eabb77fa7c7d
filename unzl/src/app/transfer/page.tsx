"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import { MainNavigation } from "@/components/navigation/MainNavigation";
import { ProtectedRoute } from "@/contexts/AuthContext";
import { Card } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { 
  Send,
  ArrowLeftRight,
  User,
  CreditCard,
  Clock,
  CheckCircle,
  AlertTriangle,
  DollarSign
} from "lucide-react";

const quickContacts = [
  { id: "1", name: "<PERSON>", account: "****1234", avatar: "JS", bank: "ABC Banking" },
  { id: "2", name: "<PERSON>", account: "****5678", avatar: "SW", bank: "XYZ Bank" },
  { id: "3", name: "<PERSON>", account: "****9012", avatar: "M<PERSON>", bank: "ABC Banking" },
  { id: "4", name: "<PERSON>", account: "****3456", avatar: "ED", bank: "First National" }
];

const myAccounts = [
  { id: "1", name: "Primary Checking", number: "****1234", balance: 15420.50, type: "Checking" },
  { id: "2", name: "Savings Account", number: "****5678", balance: 45230.75, type: "Savings" },
  { id: "3", name: "Business Account", number: "****9012", balance: 28750.00, type: "Business" }
];

export default function TransferPage() {
  const [transferData, setTransferData] = useState({
    fromAccount: "",
    toAccount: "",
    recipientName: "",
    recipientBank: "",
    amount: "",
    reference: "",
    transferType: "internal",
    priority: "standard"
  });

  const [step, setStep] = useState(1); // 1: Form, 2: Review, 3: Confirmation
  const [selectedContact, setSelectedContact] = useState<any>(null);

  const handleContactSelect = (contact: any) => {
    setSelectedContact(contact);
    setTransferData({
      ...transferData,
      toAccount: contact.account,
      recipientName: contact.name,
      recipientBank: contact.bank,
      transferType: contact.bank === "ABC Banking" ? "internal" : "external"
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (step === 1) {
      setStep(2);
    } else if (step === 2) {
      // Process transfer
      console.log("Processing transfer:", transferData);
      setStep(3);
    }
  };

  const resetForm = () => {
    setStep(1);
    setTransferData({
      fromAccount: "",
      toAccount: "",
      recipientName: "",
      recipientBank: "",
      amount: "",
      reference: "",
      transferType: "internal",
      priority: "standard"
    });
    setSelectedContact(null);
  };

  return (
    <ProtectedRoute>
      <div className="flex min-h-screen bg-background">
        <MainNavigation currentPath="/transfer" />
        
        <div className="flex-1 flex flex-col">
          {/* Header */}
          <motion.header
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="bg-card border-b border-border/50 p-4 lg:p-6"
          >
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-lg bg-blue-500/10">
                <Send className="h-6 w-6 text-blue-500" />
              </div>
              <div>
                <h1 className="text-2xl lg:text-3xl font-bold">Quick Transfer</h1>
                <p className="text-muted-foreground">Send money instantly to anyone</p>
              </div>
            </div>
          </motion.header>

          {/* Main Content */}
          <main className="flex-1 p-4 lg:p-6">
            <div className="max-w-4xl mx-auto space-y-6">
              {/* Progress Steps */}
              <div className="flex items-center justify-center space-x-4 mb-8">
                {[1, 2, 3].map((stepNum) => (
                  <div key={stepNum} className="flex items-center">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                      step >= stepNum 
                        ? "bg-primary text-primary-foreground" 
                        : "bg-muted text-muted-foreground"
                    }`}>
                      {step > stepNum ? <CheckCircle className="h-4 w-4" /> : stepNum}
                    </div>
                    {stepNum < 3 && (
                      <div className={`w-16 h-1 mx-2 ${
                        step > stepNum ? "bg-primary" : "bg-muted"
                      }`} />
                    )}
                  </div>
                ))}
              </div>

              {step === 1 && (
                <motion.div
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5 }}
                  className="grid grid-cols-1 lg:grid-cols-2 gap-6"
                >
                  {/* Transfer Form */}
                  <Card className="p-6">
                    <h2 className="text-xl font-semibold mb-6">Transfer Details</h2>
                    
                    <form onSubmit={handleSubmit} className="space-y-4">
                      <div className="space-y-2">
                        <label className="text-sm font-medium">From Account</label>
                        <select 
                          className="w-full p-3 rounded-lg border border-border bg-background"
                          value={transferData.fromAccount}
                          onChange={(e) => setTransferData({...transferData, fromAccount: e.target.value})}
                          required
                        >
                          <option value="">Select account</option>
                          {myAccounts.map((account) => (
                            <option key={account.id} value={account.id}>
                              {account.name} ({account.number}) - ${account.balance.toLocaleString()}
                            </option>
                          ))}
                        </select>
                      </div>

                      <div className="space-y-2">
                        <label className="text-sm font-medium">Recipient Name</label>
                        <Input
                          placeholder="Enter recipient name"
                          value={transferData.recipientName}
                          onChange={(e) => setTransferData({...transferData, recipientName: e.target.value})}
                          required
                        />
                      </div>

                      <div className="space-y-2">
                        <label className="text-sm font-medium">Recipient Account</label>
                        <Input
                          placeholder="Enter account number"
                          value={transferData.toAccount}
                          onChange={(e) => setTransferData({...transferData, toAccount: e.target.value})}
                          required
                        />
                      </div>

                      <div className="space-y-2">
                        <label className="text-sm font-medium">Bank Name</label>
                        <Input
                          placeholder="Enter bank name"
                          value={transferData.recipientBank}
                          onChange={(e) => setTransferData({...transferData, recipientBank: e.target.value})}
                          required
                        />
                      </div>

                      <div className="space-y-2">
                        <label className="text-sm font-medium">Amount ($)</label>
                        <Input
                          type="number"
                          step="0.01"
                          placeholder="0.00"
                          value={transferData.amount}
                          onChange={(e) => setTransferData({...transferData, amount: e.target.value})}
                          required
                        />
                      </div>

                      <div className="space-y-2">
                        <label className="text-sm font-medium">Reference (Optional)</label>
                        <Input
                          placeholder="Payment reference"
                          value={transferData.reference}
                          onChange={(e) => setTransferData({...transferData, reference: e.target.value})}
                        />
                      </div>

                      <div className="space-y-2">
                        <label className="text-sm font-medium">Priority</label>
                        <select 
                          className="w-full p-3 rounded-lg border border-border bg-background"
                          value={transferData.priority}
                          onChange={(e) => setTransferData({...transferData, priority: e.target.value})}
                        >
                          <option value="standard">Standard (Free - 1-2 business days)</option>
                          <option value="express">Express ($5.00 - Same day)</option>
                          <option value="instant">Instant ($10.00 - Within minutes)</option>
                        </select>
                      </div>

                      <Button type="submit" className="w-full designer-button">
                        Review Transfer
                      </Button>
                    </form>
                  </Card>

                  {/* Quick Contacts */}
                  <Card className="p-6">
                    <h2 className="text-xl font-semibold mb-6">Quick Contacts</h2>
                    
                    <div className="space-y-3">
                      {quickContacts.map((contact) => (
                        <div
                          key={contact.id}
                          className={`p-4 rounded-lg border cursor-pointer transition-all ${
                            selectedContact?.id === contact.id
                              ? "border-primary bg-primary/5"
                              : "border-border hover:border-primary/50"
                          }`}
                          onClick={() => handleContactSelect(contact)}
                        >
                          <div className="flex items-center gap-3">
                            <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center">
                              <span className="text-sm font-medium">{contact.avatar}</span>
                            </div>
                            <div className="flex-1">
                              <p className="font-medium">{contact.name}</p>
                              <p className="text-sm text-muted-foreground">{contact.account}</p>
                              <p className="text-xs text-muted-foreground">{contact.bank}</p>
                            </div>
                            {selectedContact?.id === contact.id && (
                              <CheckCircle className="h-5 w-5 text-primary" />
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </Card>
                </motion.div>
              )}

              {step === 2 && (
                <motion.div
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5 }}
                >
                  <Card className="p-6 max-w-2xl mx-auto">
                    <h2 className="text-xl font-semibold mb-6">Review Transfer</h2>
                    
                    <div className="space-y-6">
                      <div className="grid grid-cols-2 gap-6">
                        <div className="space-y-3">
                          <h3 className="font-medium text-muted-foreground">From</h3>
                          <div className="p-4 rounded-lg bg-muted/50">
                            <p className="font-medium">My Account</p>
                            <p className="text-sm text-muted-foreground">
                              {myAccounts.find(acc => acc.id === transferData.fromAccount)?.name}
                            </p>
                          </div>
                        </div>
                        
                        <div className="space-y-3">
                          <h3 className="font-medium text-muted-foreground">To</h3>
                          <div className="p-4 rounded-lg bg-muted/50">
                            <p className="font-medium">{transferData.recipientName}</p>
                            <p className="text-sm text-muted-foreground">{transferData.toAccount}</p>
                            <p className="text-sm text-muted-foreground">{transferData.recipientBank}</p>
                          </div>
                        </div>
                      </div>

                      <div className="text-center py-6">
                        <div className="flex items-center justify-center gap-2 mb-2">
                          <DollarSign className="h-6 w-6 text-primary" />
                          <span className="text-3xl font-bold">${transferData.amount}</span>
                        </div>
                        {transferData.reference && (
                          <p className="text-sm text-muted-foreground">Ref: {transferData.reference}</p>
                        )}
                      </div>

                      <div className="flex items-center justify-center gap-2 text-sm text-muted-foreground">
                        <Clock className="h-4 w-4" />
                        <span>
                          {transferData.priority === "standard" && "1-2 business days"}
                          {transferData.priority === "express" && "Same day delivery"}
                          {transferData.priority === "instant" && "Within minutes"}
                        </span>
                      </div>

                      <div className="flex gap-3">
                        <Button 
                          variant="outline" 
                          onClick={() => setStep(1)}
                          className="flex-1"
                        >
                          Back to Edit
                        </Button>
                        <Button 
                          onClick={handleSubmit}
                          className="flex-1 designer-button"
                        >
                          Confirm Transfer
                        </Button>
                      </div>
                    </div>
                  </Card>
                </motion.div>
              )}

              {step === 3 && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.95 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.5 }}
                >
                  <Card className="p-8 max-w-2xl mx-auto text-center">
                    <div className="w-16 h-16 rounded-full bg-green-500/10 flex items-center justify-center mx-auto mb-4">
                      <CheckCircle className="h-8 w-8 text-green-500" />
                    </div>
                    
                    <h2 className="text-2xl font-bold mb-2">Transfer Successful!</h2>
                    <p className="text-muted-foreground mb-6">
                      Your transfer of ${transferData.amount} to {transferData.recipientName} has been processed.
                    </p>
                    
                    <div className="space-y-3 mb-6">
                      <div className="flex justify-between">
                        <span>Transaction ID:</span>
                        <span className="font-mono">TXN-{Date.now()}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Status:</span>
                        <Badge className="bg-green-500">Completed</Badge>
                      </div>
                    </div>

                    <div className="flex gap-3">
                      <Button variant="outline" className="flex-1">
                        View Receipt
                      </Button>
                      <Button onClick={resetForm} className="flex-1 designer-button">
                        New Transfer
                      </Button>
                    </div>
                  </Card>
                </motion.div>
              )}
            </div>
          </main>
        </div>
      </div>
    </ProtectedRoute>
  );
}
