{"name": "abc-banking-frontend", "version": "1.0.0", "private": true, "description": "ABC Banking Enterprise - Modern banking application with advanced UI components and security features", "scripts": {"dev": "next dev -p 3001", "build": "next build", "start": "next start -p 3001", "lint": "next lint", "lint:biome": "biome lint .", "format": "biome format . --write", "type-check": "tsc --noEmit"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@radix-ui/react-accordion": "^1.2.8", "@radix-ui/react-avatar": "^1.1.7", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-dropdown-menu": "^2.1.12", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-slot": "^1.2.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.11.0", "lucide-react": "^0.475.0", "next": "^15.2.0", "next-themes": "^0.4.6", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.56.2", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.4"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.1.7", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}