# 🚀 ABC Banking - Deployment Checklist

## 📋 **Pre-Deployment Checklist**

### ✅ **Environment Setup**
- [ ] Java 17+ installed and configured
- [ ] Node.js 18+ installed
- [ ] MySQL 8.0+ installed and running
- [ ] Git installed
- [ ] All required ports available (3001, 8080, 3306)

### ✅ **Project Setup**
- [ ] Repository cloned successfully
- [ ] Backend dependencies installed (`./mvnw clean install`)
- [ ] Frontend dependencies installed (`cd unzl && npm install`)
- [ ] Database created (`abc_banking`)
- [ ] Database user configured (root/root)

### ✅ **Configuration Verification**
- [ ] Database connection working
- [ ] Backend starts without errors
- [ ] Frontend starts without errors
- [ ] API endpoints responding
- [ ] Authentication working

### ✅ **Testing**
- [ ] Backend tests pass (`./mvnw test`)
- [ ] Frontend builds successfully (`cd unzl && npm run build`)
- [ ] Login functionality works
- [ ] Database operations work
- [ ] API integration works

## 🔧 **Configuration Files to Check**

### Backend Configuration
- `src/main/resources/application-dev.properties`
- `src/main/resources/application-prod.properties`

### Frontend Configuration
- `unzl/src/lib/api.ts` - API base URL
- `unzl/package.json` - Port configuration

## 🌐 **Git Repository Setup**

### Files to Include in Git
```
✅ Include:
- src/ (all source code)
- unzl/ (frontend code, excluding node_modules)
- database/ (initialization scripts)
- pom.xml
- README.md
- SETUP_GUIDE.md
- .gitignore
- mvnw, mvnw.cmd (Maven wrapper)

❌ Exclude (via .gitignore):
- target/
- unzl/node_modules/
- unzl/.next/
- .idea/
- *.log
- .env files
```

### Git Commands for Initial Setup
```bash
# Initialize repository (if not already done)
git init

# Add all files
git add .

# Commit
git commit -m "Initial commit: ABC Banking application"

# Add remote repository
git remote add origin <your-repository-url>

# Push to repository
git push -u origin main
```

## 🖥️ **New Laptop Setup Process**

### Step 1: Install Prerequisites
```bash
# Install Java 17
# Install Node.js 18+
# Install MySQL 8.0
# Install Git
```

### Step 2: Clone and Setup
```bash
# Clone repository
git clone https://github.com/vedantheda/abcbanking.git
cd abcbanking

# Setup MySQL
mysql -u root -p
CREATE DATABASE abc_banking;
ALTER USER 'root'@'localhost' IDENTIFIED BY 'root';
FLUSH PRIVILEGES;
EXIT;

# Setup backend
./mvnw clean install

# Setup frontend
cd unzl
npm install
cd ..
```

### Step 3: Run Application
```bash
# Terminal 1: Backend
./mvnw spring-boot:run

# Terminal 2: Frontend
cd unzl && npm run dev
```

### Step 4: Verify Setup
- [ ] Backend: http://localhost:8080/api/auth/test
- [ ] Frontend: http://localhost:3001
- [ ] Login with admin/admin123

## 🔍 **Troubleshooting Common Issues**

### Port Conflicts
```bash
# Check what's using ports
netstat -ano | findstr :8080
netstat -ano | findstr :3001
netstat -ano | findstr :3306

# Kill processes if needed
taskkill /PID <PID> /F
```

### MySQL Issues
```bash
# Start MySQL service
net start mysql  # Windows
brew services start mysql  # macOS
sudo systemctl start mysql  # Linux

# Reset MySQL password
mysql -u root -p
ALTER USER 'root'@'localhost' IDENTIFIED BY 'root';
```

### Java Issues
```bash
# Check Java version
java -version

# Set JAVA_HOME if needed
export JAVA_HOME=/path/to/java17
```

### Node.js Issues
```bash
# Check Node version
node --version

# Clear npm cache
npm cache clean --force

# Reinstall dependencies
rm -rf node_modules package-lock.json
npm install
```

## 📦 **Production Deployment**

### Build for Production
```bash
# Backend
./mvnw clean package -Pprod

# Frontend
cd unzl
npm run build
```

### Environment Variables (Production)
```bash
export SPRING_PROFILES_ACTIVE=prod
export DATABASE_URL=******************************************
export DATABASE_USERNAME=your_db_user
export DATABASE_PASSWORD=your_db_password
export JWT_SECRET=your-super-secret-jwt-key
```

### Run Production
```bash
java -jar target/ABCBanking-1.0-SNAPSHOT.jar
```

## ✅ **Final Verification**

### Functionality Tests
- [ ] User registration works
- [ ] User login works
- [ ] Dashboard loads correctly
- [ ] Account operations work
- [ ] Transaction history displays
- [ ] Money transfers work
- [ ] Logout works

### Security Tests
- [ ] JWT tokens are generated
- [ ] Protected routes require authentication
- [ ] Role-based access works
- [ ] Password encryption works

### Performance Tests
- [ ] Application starts in reasonable time
- [ ] Database queries are fast
- [ ] Frontend loads quickly
- [ ] No memory leaks

## 📞 **Support**

If you encounter issues during deployment:

1. **Check Prerequisites**: Ensure all required software is installed
2. **Verify Configuration**: Check database settings and API URLs
3. **Review Logs**: Check application logs for error messages
4. **Test Components**: Test backend and frontend separately
5. **Consult Documentation**: Refer to SETUP_GUIDE.md for detailed instructions

**Remember**: The application should work identically on any laptop that meets the prerequisites! 🎯
