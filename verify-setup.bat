@echo off
REM ABC Banking - Setup Verification Script (Windows)
REM This script verifies that the development environment is properly set up

echo 🏦 ABC Banking - Setup Verification
echo ==================================

REM Check Java
echo.
echo Checking Java...
java -version >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ Java is installed
    java -version
) else (
    echo ❌ Java not found - Please install Java 17+
)

REM Check Node.js
echo.
echo Checking Node.js...
node --version >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ Node.js is installed
    node --version
) else (
    echo ❌ Node.js not found - Please install Node.js 18+
)

REM Check npm
echo.
echo Checking npm...
npm --version >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ npm is installed
    npm --version
) else (
    echo ❌ npm not found
)

REM Check MySQL
echo.
echo Checking MySQL...
mysql --version >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ MySQL is installed
    mysql --version
    
    REM Test MySQL connection
    echo Testing MySQL connection...
    mysql -u root -proot -e "SELECT 1;" >nul 2>&1
    if %errorlevel% == 0 (
        echo ✅ MySQL connection successful
        
        REM Check if database exists
        mysql -u root -proot -e "USE abc_banking;" >nul 2>&1
        if %errorlevel% == 0 (
            echo ✅ Database 'abc_banking' exists
        ) else (
            echo ❌ Database 'abc_banking' not found
            echo Run: mysql -u root -proot -e "CREATE DATABASE abc_banking;"
        )
    ) else (
        echo ❌ MySQL connection failed (check credentials)
    )
) else (
    echo ❌ MySQL not found - Please install MySQL 8.0+
)

REM Check Git
echo.
echo Checking Git...
git --version >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ Git is installed
    git --version
) else (
    echo ❌ Git not found - Please install Git
)

REM Check project structure
echo.
echo Checking project structure...

if exist "pom.xml" (
    echo ✅ Backend (pom.xml) found
) else (
    echo ❌ Backend (pom.xml) not found
)

if exist "src\main\java" (
    echo ✅ Java source directory found
) else (
    echo ❌ Java source directory not found
)

if exist "unzl" (
    echo ✅ Frontend directory (unzl) found
) else (
    echo ❌ Frontend directory (unzl) not found
)

if exist "unzl\package.json" (
    echo ✅ Frontend package.json found
) else (
    echo ❌ Frontend package.json not found
)

REM Check if dependencies are installed
echo.
echo Checking dependencies...

if exist "unzl\node_modules" (
    echo ✅ Frontend dependencies installed
) else (
    echo ❌ Frontend dependencies not installed (run: cd unzl && npm install)
)

REM Check ports
echo.
echo Checking ports...
netstat -an | findstr ":8080" >nul 2>&1
if %errorlevel% == 0 (
    echo ❌ Port 8080 is in use
) else (
    echo ✅ Port 8080 is available
)

netstat -an | findstr ":3001" >nul 2>&1
if %errorlevel% == 0 (
    echo ❌ Port 3001 is in use
) else (
    echo ✅ Port 3001 is available
)

netstat -an | findstr ":3306" >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ Port 3306 is in use (MySQL)
) else (
    echo ❌ Port 3306 is not in use (MySQL not running?)
)

echo.
echo Setup verification complete!
echo.
echo Next steps:
echo 1. If any checks failed, install the missing prerequisites
echo 2. Run: .\mvnw.cmd clean install
echo 3. Run: cd unzl && npm install
echo 4. Start backend: .\mvnw.cmd spring-boot:run
echo 5. Start frontend: cd unzl && npm run dev
echo.
echo For detailed setup instructions, see SETUP_GUIDE.md

pause
