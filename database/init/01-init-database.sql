-- ABC Banking Database Initialization Script
-- This script creates the initial database structure and sample data

USE abc_banking;

-- Create users table
CREATE TABLE IF NOT EXISTS users (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VA<PERSON>HAR(50) NOT NULL,
    last_name VA<PERSON><PERSON><PERSON>(50) NOT NULL,
    phone_number VARCHAR(20),
    date_of_birth DATE,
    address TEXT,
    status ENUM('ACTIVE', 'INACTIVE', 'SUSPENDED') DEFAULT 'ACTIVE',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    last_login TIMESTAMP NULL,
    failed_login_attempts INT DEFAULT 0,
    account_locked_until TIMESTAMP NULL
);

-- Create roles table
CREATE TABLE IF NOT EXISTS roles (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create user_roles junction table
CREATE TABLE IF NOT EXISTS user_roles (
    user_id BIGINT,
    role_id BIGINT,
    PRIMARY KEY (user_id, role_id),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE
);

-- Create accounts table
CREATE TABLE IF NOT EXISTS accounts (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    account_number VARCHAR(20) UNIQUE NOT NULL,
    account_type ENUM('CHECKING', 'SAVINGS', 'BUSINESS', 'INVESTMENT') NOT NULL,
    user_id BIGINT NOT NULL,
    balance DECIMAL(15, 2) DEFAULT 0.00,
    currency VARCHAR(3) DEFAULT 'USD',
    status ENUM('ACTIVE', 'INACTIVE', 'FROZEN', 'CLOSED') DEFAULT 'ACTIVE',
    interest_rate DECIMAL(5, 4) DEFAULT 0.0000,
    minimum_balance DECIMAL(15, 2) DEFAULT 0.00,
    overdraft_limit DECIMAL(15, 2) DEFAULT 0.00,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    closed_at TIMESTAMP NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_account_number (account_number),
    INDEX idx_status (status)
);

-- Create transactions table
CREATE TABLE IF NOT EXISTS transactions (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    transaction_id VARCHAR(50) UNIQUE NOT NULL,
    from_account_id BIGINT,
    to_account_id BIGINT,
    transaction_type ENUM('DEBIT', 'CREDIT', 'TRANSFER', 'DEPOSIT', 'WITHDRAWAL', 'FEE', 'INTEREST') NOT NULL,
    amount DECIMAL(15, 2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'USD',
    description TEXT,
    reference_number VARCHAR(100),
    status ENUM('PENDING', 'COMPLETED', 'FAILED', 'CANCELLED') DEFAULT 'PENDING',
    category VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMP NULL,
    FOREIGN KEY (from_account_id) REFERENCES accounts(id) ON DELETE SET NULL,
    FOREIGN KEY (to_account_id) REFERENCES accounts(id) ON DELETE SET NULL,
    INDEX idx_from_account (from_account_id),
    INDEX idx_to_account (to_account_id),
    INDEX idx_transaction_id (transaction_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);

-- Create loans table
CREATE TABLE IF NOT EXISTS loans (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    loan_number VARCHAR(20) UNIQUE NOT NULL,
    user_id BIGINT NOT NULL,
    loan_type ENUM('PERSONAL', 'HOME', 'AUTO', 'BUSINESS', 'EDUCATION') NOT NULL,
    principal_amount DECIMAL(15, 2) NOT NULL,
    outstanding_balance DECIMAL(15, 2) NOT NULL,
    interest_rate DECIMAL(5, 4) NOT NULL,
    term_months INT NOT NULL,
    monthly_payment DECIMAL(15, 2) NOT NULL,
    status ENUM('PENDING', 'APPROVED', 'ACTIVE', 'PAID_OFF', 'DEFAULTED') DEFAULT 'PENDING',
    start_date DATE,
    end_date DATE,
    next_payment_date DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_loan_number (loan_number),
    INDEX idx_status (status)
);

-- Create cards table
CREATE TABLE IF NOT EXISTS cards (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    card_number VARCHAR(20) UNIQUE NOT NULL,
    account_id BIGINT NOT NULL,
    card_type ENUM('DEBIT', 'CREDIT', 'PREPAID') NOT NULL,
    card_holder_name VARCHAR(100) NOT NULL,
    expiry_date DATE NOT NULL,
    cvv_hash VARCHAR(255) NOT NULL,
    status ENUM('ACTIVE', 'INACTIVE', 'BLOCKED', 'EXPIRED') DEFAULT 'ACTIVE',
    daily_limit DECIMAL(15, 2) DEFAULT 1000.00,
    monthly_limit DECIMAL(15, 2) DEFAULT 10000.00,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (account_id) REFERENCES accounts(id) ON DELETE CASCADE,
    INDEX idx_account_id (account_id),
    INDEX idx_card_number (card_number),
    INDEX idx_status (status)
);

-- Create audit_logs table
CREATE TABLE IF NOT EXISTS audit_logs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT,
    action VARCHAR(100) NOT NULL,
    entity_type VARCHAR(50) NOT NULL,
    entity_id BIGINT,
    old_values JSON,
    new_values JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_action (action),
    INDEX idx_entity (entity_type, entity_id),
    INDEX idx_created_at (created_at)
);

-- Insert default roles
INSERT INTO roles (name, description) VALUES
('ADMIN', 'System administrator with full access'),
('CUSTOMER', 'Regular banking customer'),
('MANAGER', 'Bank manager with elevated privileges'),
('TELLER', 'Bank teller with transaction privileges')
ON DUPLICATE KEY UPDATE description = VALUES(description);

-- Insert sample admin user (password: admin123)
INSERT INTO users (username, email, password_hash, first_name, last_name, phone_number, date_of_birth, address, status) VALUES
('admin', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Admin', 'User', '******-0001', '1980-01-01', '123 Admin St, Banking City, BC 12345', 'ACTIVE'),
('john.doe', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'John', 'Doe', '******-0002', '1990-05-15', '456 Customer Ave, User City, UC 67890', 'ACTIVE'),
('jane.smith', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Jane', 'Smith', '******-0003', '1985-08-22', '789 Business Blvd, Commerce City, CC 54321', 'ACTIVE')
ON DUPLICATE KEY UPDATE email = VALUES(email);

-- Assign roles to users
INSERT INTO user_roles (user_id, role_id) VALUES
(1, 1), -- Admin user gets ADMIN role
(1, 2), -- Admin user also gets CUSTOMER role
(2, 2), -- John Doe gets CUSTOMER role
(3, 2)  -- Jane Smith gets CUSTOMER role
ON DUPLICATE KEY UPDATE user_id = VALUES(user_id);

-- Insert sample accounts
INSERT INTO accounts (account_number, account_type, user_id, balance, interest_rate, minimum_balance, overdraft_limit) VALUES
('**********', 'CHECKING', 2, 15420.50, 0.0050, 100.00, 500.00),
('**********', 'SAVINGS', 2, 45680.75, 0.0250, 1000.00, 0.00),
('**********', 'BUSINESS', 3, 125000.00, 0.0120, 5000.00, 10000.00),
('**********', 'INVESTMENT', 2, 78950.25, 0.0480, 5000.00, 0.00)
ON DUPLICATE KEY UPDATE balance = VALUES(balance);

-- Insert sample transactions
INSERT INTO transactions (transaction_id, from_account_id, to_account_id, transaction_type, amount, description, reference_number, status, category, created_at, processed_at) VALUES
('TXN001', NULL, 1, 'DEPOSIT', 2500.00, 'Salary Deposit', 'SAL2024001', 'COMPLETED', 'Income', '2024-01-15 10:30:00', '2024-01-15 10:30:00'),
('TXN002', 1, NULL, 'WITHDRAWAL', 850.00, 'Rent Payment', 'RENT2024001', 'COMPLETED', 'Housing', '2024-01-14 14:20:00', '2024-01-14 14:20:00'),
('TXN003', 1, NULL, 'WITHDRAWAL', 125.50, 'Grocery Store', 'GRC2024001', 'COMPLETED', 'Food', '2024-01-14 09:15:00', '2024-01-14 09:15:00'),
('TXN004', 1, 2, 'TRANSFER', 1000.00, 'Transfer to Savings', 'TRF2024001', 'COMPLETED', 'Transfer', '2024-01-13 16:45:00', '2024-01-13 16:45:00'),
('TXN005', 1, NULL, 'WITHDRAWAL', 75.00, 'Utility Bill', 'UTL2024001', 'PENDING', 'Utilities', '2024-01-12 11:30:00', NULL)
ON DUPLICATE KEY UPDATE status = VALUES(status);

-- Insert sample cards
INSERT INTO cards (card_number, account_id, card_type, card_holder_name, expiry_date, cvv_hash, daily_limit, monthly_limit) VALUES
('****************', 1, 'DEBIT', 'JOHN DOE', '2026-12-31', '$2a$10$hash1', 1000.00, 10000.00),
('****************', 1, 'CREDIT', 'JOHN DOE', '2025-08-31', '$2a$10$hash2', 2000.00, 25000.00),
('****************', 3, 'DEBIT', 'JANE SMITH', '2027-03-31', '$2a$10$hash3', 5000.00, 50000.00)
ON DUPLICATE KEY UPDATE card_holder_name = VALUES(card_holder_name);

COMMIT;
