# 🏦 ABC Banking Enterprise

A comprehensive banking management system built with **Spring Boot** (Backend) and **Next.js** (Frontend).

## ✨ **Features**

- 🔐 **Secure Authentication** - JWT-based authentication with role-based access
- 💰 **Account Management** - Create and manage multiple account types
- 💸 **Money Transfers** - Secure fund transfers between accounts
- 📊 **Transaction History** - Detailed transaction tracking and reporting
- 🏢 **Branch Management** - Multi-branch banking operations
- 💳 **Loan Management** - Loan applications and EMI tracking
- 📱 **Responsive UI** - Modern, mobile-friendly interface
- 🛡️ **Enterprise Security** - Advanced security features and audit trails

## 🚀 **Quick Start**

### For New Setup (First Time)
📖 **[Complete Setup Guide](SETUP_GUIDE.md)** - Follow this for detailed installation instructions

### Clone Repository
```bash
git clone https://github.com/vedantheda/abcbanking.git
cd abcbanking
```

### For Existing Setup
```bash
# Start Backend
./mvnw spring-boot:run

# Start Frontend (in new terminal)
cd unzl && npm run dev
```

## 🌐 **Access Points**

- **Frontend**: http://localhost:3001
- **Backend API**: http://localhost:8080/api
- **API Documentation**: http://localhost:8080/api/swagger-ui.html

## 👥 **Default Login**

- **Admin**: `admin` / `admin123`
- **Customer**: `john.doe` / `password123`

## 🏗️ **Tech Stack**

### Backend
- **Spring Boot 3.2** - Enterprise Java framework
- **Spring Security** - Authentication and authorization
- **Spring Data JPA** - Database abstraction
- **MySQL 8.0** - Primary database
- **JWT** - Token-based authentication
- **Maven** - Dependency management

### Frontend
- **Next.js 14** - React framework with App Router
- **TypeScript** - Type-safe JavaScript
- **Tailwind CSS** - Utility-first CSS framework
- **Shadcn/ui** - Modern UI components
- **React Hook Form** - Form management
- **Zustand** - State management

## 📁 **Project Structure**

```
ABCBanking/
├── src/                    # Spring Boot Backend
│   ├── main/java/         # Java source code
│   └── main/resources/    # Configuration files
├── unzl/                  # Next.js Frontend
│   ├── src/               # React components and pages
│   └── public/            # Static assets
├── database/              # Database initialization scripts
├── pom.xml               # Maven configuration
├── SETUP_GUIDE.md        # Detailed setup instructions
└── README.md             # This file
```

## 🔧 **Configuration**

### Database Settings
- **Development**: MySQL (localhost:3306)
- **Database**: abc_banking
- **Credentials**: root/root

### Environment Profiles
- **`dev`** (default): Development mode with MySQL
- **`prod`**: Production mode with MySQL

### Running with Different Profiles
```bash
# Development (default)
./mvnw spring-boot:run

# Production
./mvnw spring-boot:run -Dspring-boot.run.profiles=prod
```

## 🔐 **Security Features**

- 🔑 JWT-based authentication
- 👥 Role-based access control (RBAC)
- 🔒 Password encryption with BCrypt
- 🛡️ CORS configuration
- 🚫 SQL injection prevention
- 📝 Audit logging

## 🧪 **Testing**

```bash
# Run backend tests
./mvnw test

# Run frontend tests
cd unzl && npm test
```

## 🚀 **Deployment**

### Production Build
```bash
# Build backend
./mvnw clean package -Pprod

# Build frontend
cd unzl && npm run build
```

### Manual Deployment
```bash
# Set production profile
export SPRING_PROFILES_ACTIVE=prod

# Run the application
java -jar target/ABCBanking-1.0-SNAPSHOT.jar
```

## 📚 **API Documentation**

When running in development mode:
- **Swagger UI**: http://localhost:8080/api/swagger-ui.html
- **OpenAPI JSON**: http://localhost:8080/api/api-docs

## 🐛 **Troubleshooting**

### Common Issues

1. **Port already in use**: Change port in `application.properties`
2. **Database connection**: Ensure MySQL is running and credentials are correct
3. **Build issues**: Run `./mvnw clean install`
4. **Frontend issues**: Delete `node_modules` and run `npm install`

For detailed troubleshooting, see [SETUP_GUIDE.md](SETUP_GUIDE.md)

## 📝 **License**

This project is licensed under the MIT License.

## 🤝 **Contributing**

1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## 📞 **Support**

For support and questions:
- Check [SETUP_GUIDE.md](SETUP_GUIDE.md) for detailed instructions
- Create an issue in the repository
- Contact the development team
