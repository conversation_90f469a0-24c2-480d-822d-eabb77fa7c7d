# ========================================
# JAVA / SPRING BOOT
# ========================================
target/
!.mvn/wrapper/maven-wrapper.jar
!**/src/main/**/target/
!**/src/test/**/target/

# ========================================
# IDE
# ========================================
### IntelliJ IDEA ###
.idea/
*.iws
*.iml
*.ipr

### Eclipse ###
.apt_generated
.classpath
.factorypath
.project
.settings
.springBeans
.sts4-cache

### NetBeans ###
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/
build/
!**/src/main/**/build/
!**/src/test/**/build/

### VS Code ###
.vscode/

# ========================================
# OS
# ========================================
### Mac OS ###
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes

### Windows ###
ehthumbs.db
Thumbs.db

# ========================================
# FRONTEND (Next.js)
# ========================================
unzl/node_modules/
unzl/.next/
unzl/out/
unzl/build/
unzl/.env*.local
unzl/.vercel
unzl/*.tgz
unzl/*.tar.gz

# ========================================
# LOGS
# ========================================
*.log
logs/
log/

# ========================================
# TEMPORARY FILES
# ========================================
*.tmp
*.temp
*.bak
*.backup
*~

# ========================================
# DATABASE
# ========================================
*.db
*.sqlite
*.sqlite3

# ========================================
# SECURITY
# ========================================
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
*.key
*.pem
*.p12
*.jks

# ========================================
# MAVEN
# ========================================
.mvn/timing.properties

# ========================================
# MISC
# ========================================
*.pid
*.seed
*.pid.lock
.npm
.eslintcache
.nyc_output
coverage/
.coverage
.pytest_cache/