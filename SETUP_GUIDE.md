# 🏦 ABC Banking - Complete Setup Guide

This guide will help you set up the ABC Banking application on any laptop from scratch.

## 📋 **Prerequisites**

Before starting, ensure you have the following installed:

### 1. **Java 17 or Higher**
- **Download**: https://adoptium.net/
- **Windows**: `choco install openjdk17` (if you have Chocolate<PERSON>)
- **macOS**: `brew install openjdk@17` (if you have Homebrew)
- **Linux**: `sudo apt install openjdk-17-jdk`

**Verify Installation:**
```bash
java -version
# Should show Java 17 or higher
```

### 2. **Node.js 18 or Higher**
- **Download**: https://nodejs.org/
- **Windows**: `choco install nodejs`
- **macOS**: `brew install node`
- **Linux**: `sudo apt install nodejs npm`

**Verify Installation:**
```bash
node --version
npm --version
# Should show Node 18+ and npm 8+
```

### 3. **MySQL 8.0 or Higher**
- **Download**: https://dev.mysql.com/downloads/mysql/
- **Windows**: `choco install mysql`
- **macOS**: `brew install mysql`
- **Linux**: `sudo apt install mysql-server`

**Verify Installation:**
```bash
mysql --version
# Should show MySQL 8.0+
```

### 4. **Git**
- **Download**: https://git-scm.com/
- **Windows**: `choco install git`
- **macOS**: `brew install git`
- **Linux**: `sudo apt install git`

## 🚀 **Step-by-Step Setup**

### Step 1: Configure MySQL

1. **Start MySQL Service:**
   ```bash
   # Windows
   net start mysql
   
   # macOS
   brew services start mysql
   
   # Linux
   sudo systemctl start mysql
   ```

2. **Create Database and User:**
   ```bash
   mysql -u root -p
   ```
   
   In MySQL console:
   ```sql
   CREATE DATABASE abc_banking;
   ALTER USER 'root'@'localhost' IDENTIFIED BY 'root';
   FLUSH PRIVILEGES;
   EXIT;
   ```

### Step 2: Clone and Setup Project

1. **Clone Repository:**
   ```bash
   git clone https://github.com/vedantheda/abcbanking.git
   cd abcbanking
   ```

2. **Setup Backend (Spring Boot):**
   ```bash
   # Clean and install dependencies
   ./mvnw clean install
   
   # On Windows, use:
   ./mvnw.cmd clean install
   ```

3. **Setup Frontend (Next.js):**
   ```bash
   cd unzl
   npm install
   cd ..
   ```

### Step 3: Run the Application

1. **Start Backend (Terminal 1):**
   ```bash
   ./mvnw spring-boot:run
   
   # On Windows:
   ./mvnw.cmd spring-boot:run
   ```
   
   **Backend will be available at:** http://localhost:8080/api

2. **Start Frontend (Terminal 2):**
   ```bash
   cd unzl
   npm run dev
   ```
   
   **Frontend will be available at:** http://localhost:3001

### Step 4: Verify Setup

1. **Test Backend API:**
   ```bash
   curl http://localhost:8080/api/auth/test
   # Should return: {"message":"Auth endpoint is working!"}
   ```

2. **Test Database Connection:**
   ```bash
   curl http://localhost:8080/api/auth/test/db
   # Should return: {"message":"Database is connected and working! Found 2 users in database."}
   ```

3. **Test Login:**
   ```bash
   curl -X POST http://localhost:8080/api/auth/login \
     -H "Content-Type: application/json" \
     -d '{"username":"admin","password":"admin123"}'
   # Should return JWT token and user details
   ```

4. **Access Frontend:**
   - Open browser: http://localhost:3001
   - Login with: `admin` / `admin123`

## 🔧 **Configuration**

### Database Settings
File: `src/main/resources/application-dev.properties`
```properties
spring.datasource.url=***************************************
spring.datasource.username=root
spring.datasource.password=root
```

### Frontend API Settings
File: `unzl/src/lib/api.ts`
```typescript
const API_BASE_URL = 'http://localhost:8080/api';
```

## 👥 **Default Users**

The application creates these users automatically:

1. **Admin User:**
   - Username: `admin`
   - Password: `admin123`
   - Roles: ADMIN, CUSTOMER

2. **Regular User:**
   - Username: `john.doe`
   - Password: `password123`
   - Roles: CUSTOMER

## 🛠️ **Troubleshooting**

### Common Issues:

1. **Port Already in Use:**
   ```bash
   # Kill process on port 8080
   netstat -ano | findstr :8080
   taskkill /PID <PID> /F
   
   # Kill process on port 3001
   netstat -ano | findstr :3001
   taskkill /PID <PID> /F
   ```

2. **MySQL Connection Failed:**
   - Ensure MySQL is running
   - Check username/password in application-dev.properties
   - Verify database `abc_banking` exists

3. **Java Version Issues:**
   ```bash
   # Check JAVA_HOME
   echo $JAVA_HOME
   
   # Set JAVA_HOME (if needed)
   export JAVA_HOME=/path/to/java17
   ```

4. **Node.js Issues:**
   ```bash
   # Clear npm cache
   npm cache clean --force
   
   # Delete node_modules and reinstall
   cd unzl
   rm -rf node_modules
   npm install
   ```

## 📁 **Project Structure**

```
ABCBanking/
├── src/                    # Spring Boot Backend
│   ├── main/java/         # Java source code
│   │   └── org/example/abcbanking/
│   │       ├── controller/    # REST Controllers
│   │       ├── service/       # Business Logic
│   │       ├── repository/    # Data Access
│   │       ├── model/         # Entity Classes
│   │       ├── security/      # Security Configuration
│   │       └── config/        # Application Configuration
│   └── main/resources/    # Configuration files
│       ├── application.properties
│       ├── application-dev.properties
│       └── application-prod.properties
├── unzl/                  # Next.js Frontend
│   ├── src/               # React components and pages
│   │   ├── app/           # Next.js App Router
│   │   ├── components/    # Reusable Components
│   │   ├── contexts/      # React Contexts
│   │   └── lib/           # Utilities and API
│   ├── public/            # Static assets
│   └── package.json       # Frontend dependencies
├── database/              # Database initialization scripts
├── pom.xml               # Maven configuration
├── .gitignore            # Git ignore rules
└── README.md             # Project documentation
```

## 🎯 **Next Steps**

After successful setup:

1. **Explore the Application:**
   - Login to the frontend
   - Navigate through different banking features
   - Test account management, transfers, etc.

2. **Development:**
   - Backend API documentation: http://localhost:8080/api/swagger-ui.html
   - Database console: http://localhost:8080/api/h2-console (if using H2)
   - Frontend development server with hot reload

3. **Customization:**
   - Modify database configuration for your environment
   - Update frontend styling and components
   - Add new banking features as needed

## 📞 **Support**

If you encounter any issues during setup:
1. Check the troubleshooting section above
2. Verify all prerequisites are correctly installed
3. Ensure all ports (3001, 8080, 3306) are available
4. Check application logs for detailed error messages

**Happy Banking! 🏦**
